/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* المتغيرات */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4facfe;
    --danger-color: #ff6b6b;
    --warning-color: #feca57;
    --dark-color: #2c3e50;
    --light-color: #ffffff;
    --gray-color: #95a5a6;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* الأساسيات */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--bg-gradient);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    direction: rtl;
    overflow: hidden;
}

/* الخلفية المتحركة */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    left: 80%;
    animation-delay: 2s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 4s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 10%;
    left: 70%;
    animation-delay: 1s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 40%;
    left: 5%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* الحاوي الرئيسي */
.login-container {
    position: relative;
    width: 100%;
    max-width: 450px;
    margin: 0 auto;
    padding: 20px;
}

/* البطاقات */
.login-card,
.activation-card,
.status-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    padding: 40px;
    transition: var(--transition);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hidden {
    display: none;
}

/* رأس تسجيل الدخول */
.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    font-size: 4rem;
    background: var(--bg-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.app-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.app-subtitle {
    color: var(--gray-color);
    font-size: 1rem;
    margin: 0;
}

/* النماذج */
.login-form,
.activation-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-group {
    position: relative;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-container input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    outline: none;
}

.input-container input:focus {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-container input:focus + label,
.input-container input:not(:placeholder-shown) + label {
    transform: translateY(-35px) scale(0.8);
    color: var(--primary-color);
}

.input-container label {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
    font-size: 1rem;
    transition: var(--transition);
    pointer-events: none;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 8px;
}

.input-icon {
    position: absolute;
    right: 15px;
    color: var(--gray-color);
    font-size: 1.2rem;
    z-index: 1;
}

.toggle-password {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: var(--gray-color);
    cursor: pointer;
    font-size: 1.1rem;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--primary-color);
}

/* خيارات النموذج */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--dark-color);
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    text-decoration: underline;
}

/* الأزرار */
.login-btn,
.btn-primary,
.btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    text-decoration: none;
}

.login-btn,
.btn-primary {
    background: var(--bg-gradient);
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:hover,
.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(149, 165, 166, 0.1);
    color: var(--dark-color);
    border: 2px solid rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
    background: rgba(149, 165, 166, 0.2);
    transform: translateY(-1px);
}

.activation-request-btn {
    width: 100%;
    padding: 15px;
    background: rgba(118, 75, 162, 0.1);
    border: 2px solid rgba(118, 75, 162, 0.3);
    border-radius: 12px;
    color: var(--secondary-color);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.activation-request-btn:hover {
    background: rgba(118, 75, 162, 0.2);
    transform: translateY(-2px);
}

/* تذييل تسجيل الدخول */
.login-footer {
    margin-top: 30px;
    text-align: center;
}

.divider {
    position: relative;
    margin: 25px 0;
    text-align: center;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(0, 0, 0, 0.1);
}

.divider span {
    background: rgba(255, 255, 255, 0.95);
    padding: 0 20px;
    color: var(--gray-color);
    font-size: 0.9rem;
}

.help-links {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;
}

.help-links a {
    color: var(--gray-color);
    text-decoration: none;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition);
}

.help-links a:hover {
    color: var(--primary-color);
}

/* بطاقة طلب التفعيل */
.activation-card {
    max-width: 600px;
    width: 100%;
}

.card-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.back-btn {
    position: absolute;
    right: 0;
    top: 0;
    background: rgba(149, 165, 166, 0.1);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    color: var(--gray-color);
    font-size: 1.2rem;
    transition: var(--transition);
}

.back-btn:hover {
    background: rgba(149, 165, 166, 0.2);
    color: var(--dark-color);
}

.card-header h2 {
    color: var(--dark-color);
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.card-header p {
    color: var(--gray-color);
    font-size: 1rem;
}

/* أقسام النموذج */
.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.form-section h3 {
    color: var(--dark-color);
    font-size: 1.2rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: var(--primary-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

/* معلومات النظام */
.system-info {
    background: rgba(102, 126, 234, 0.05);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: var(--dark-color);
}

.info-value {
    color: var(--primary-color);
    font-family: monospace;
    font-size: 0.9rem;
}

/* خيارات الترخيص */
.license-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.license-option {
    display: block;
    cursor: pointer;
    transition: var(--transition);
}

.license-option input[type="radio"] {
    display: none;
}

.option-content {
    padding: 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.5);
    transition: var(--transition);
}

.license-option input[type="radio"]:checked + .option-content {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.option-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.option-header i {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.option-title {
    font-weight: 600;
    color: var(--dark-color);
    flex: 1;
    margin-right: 15px;
}

.option-price {
    background: var(--bg-gradient);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.option-description {
    color: var(--gray-color);
    font-size: 0.9rem;
    margin: 0;
}

/* منطقة النص */
textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    outline: none;
    resize: vertical;
    min-height: 100px;
}

textarea:focus {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* إجراءات النموذج */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

/* بطاقة الحالة */
.status-card {
    text-align: center;
    max-width: 500px;
}

.status-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.status-icon {
    width: 80px;
    height: 80px;
    background: var(--bg-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.status-content h2 {
    color: var(--dark-color);
    font-size: 1.8rem;
    margin: 0;
}

.status-content p {
    color: var(--gray-color);
    font-size: 1.1rem;
    margin: 0;
}

.request-details {
    background: rgba(0, 0, 0, 0.02);
    padding: 20px;
    border-radius: 12px;
    width: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--dark-color);
}

.detail-value {
    color: var(--primary-color);
    font-weight: 500;
}

.status-pending {
    background: rgba(254, 202, 87, 0.2);
    color: var(--warning-color);
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    width: 100%;
}

/* رسائل التنبيه */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    padding: 15px 25px;
    z-index: 9999;
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
    transition: var(--transition);
    border-left: 4px solid var(--success-color);
}

.toast.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toast-icon {
    font-size: 1.2rem;
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast-message {
    color: var(--dark-color);
    font-weight: 500;
}

/* التجاوب */
@media (max-width: 768px) {
    .login-container {
        padding: 10px;
    }

    .login-card,
    .activation-card,
    .status-card {
        padding: 25px;
        margin: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-actions,
    .status-actions {
        flex-direction: column;
    }

    .help-links {
        flex-direction: column;
        gap: 15px;
    }

    .app-title {
        font-size: 1.5rem;
    }

    .logo-icon {
        font-size: 3rem;
    }
}
