<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تسجيل الدخول المحسن - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --dark-color: #2c3e50;
            --light-color: #ffffff;
            --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
            --shadow-strong: 0 20px 60px rgba(0, 0, 0, 0.15);
            --border-radius: 20px;
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            direction: rtl;
            overflow-x: hidden;
            position: relative;
        }

        /* خلفية متحركة محسنة */
        .animated-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        .particle:nth-child(1) { width: 20px; height: 20px; top: 20%; left: 20%; animation-delay: 0s; }
        .particle:nth-child(2) { width: 30px; height: 30px; top: 60%; left: 80%; animation-delay: 2s; }
        .particle:nth-child(3) { width: 15px; height: 15px; top: 80%; left: 30%; animation-delay: 4s; }
        .particle:nth-child(4) { width: 25px; height: 25px; top: 40%; left: 70%; animation-delay: 1s; }
        .particle:nth-child(5) { width: 35px; height: 35px; top: 10%; left: 60%; animation-delay: 3s; }
        .particle:nth-child(6) { width: 18px; height: 18px; top: 70%; left: 10%; animation-delay: 5s; }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-30px) rotate(180deg);
                opacity: 1;
            }
        }

        /* الحاوي الرئيسي */
        .main-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-wrapper {
            position: relative;
            width: 100%;
            max-width: 500px;
        }

        /* البطاقات المحسنة */
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-strong);
            padding: 40px;
            transition: var(--transition);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .card.hidden {
            display: none;
        }

        /* رأس تسجيل الدخول المحسن */
        .login-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            box-shadow: var(--shadow-soft);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: var(--shadow-soft);
            }
            50% {
                transform: scale(1.05);
                box-shadow: var(--shadow-strong);
            }
        }

        .app-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 10px;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .app-subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
            font-weight: 500;
        }

        .version-badge {
            display: inline-block;
            background: var(--success-gradient);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 10px;
            box-shadow: var(--shadow-soft);
        }

        /* النماذج المحسنة */
        .enhanced-form {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .form-group {
            position: relative;
        }

        .input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .form-input {
            width: 100%;
            padding: 18px 60px 18px 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: var(--transition);
            outline: none;
            font-weight: 500;
        }

        .form-input:focus {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .form-input:focus + .form-label,
        .form-input:not(:placeholder-shown) + .form-label {
            transform: translateY(-45px) scale(0.85);
            color: #667eea;
            font-weight: 600;
        }

        .form-label {
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 1rem;
            transition: var(--transition);
            pointer-events: none;
            background: rgba(255, 255, 255, 0.9);
            padding: 0 10px;
            font-weight: 500;
        }

        .input-icon {
            position: absolute;
            right: 20px;
            color: #7f8c8d;
            font-size: 1.3rem;
            z-index: 1;
            transition: var(--transition);
        }

        .form-input:focus ~ .input-icon {
            color: #667eea;
            transform: scale(1.1);
        }

        .toggle-password {
            position: absolute;
            left: 20px;
            background: none;
            border: none;
            color: #7f8c8d;
            cursor: pointer;
            font-size: 1.2rem;
            transition: var(--transition);
            padding: 5px;
            border-radius: 50%;
        }

        .toggle-password:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        /* خيارات النموذج المحسنة */
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            font-size: 0.95rem;
            color: var(--dark-color);
            font-weight: 500;
            transition: var(--transition);
        }

        .checkbox-container:hover {
            color: #667eea;
        }

        .checkbox-container input[type="checkbox"] {
            display: none;
        }

        .custom-checkbox {
            width: 22px;
            height: 22px;
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            position: relative;
            transition: var(--transition);
            background: white;
        }

        .checkbox-container input[type="checkbox"]:checked + .custom-checkbox {
            background: var(--primary-gradient);
            border-color: #667eea;
            transform: scale(1.1);
        }

        .checkbox-container input[type="checkbox"]:checked + .custom-checkbox::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
            font-size: 0.95rem;
            font-weight: 500;
            transition: var(--transition);
            padding: 5px 10px;
            border-radius: 8px;
        }

        .forgot-password:hover {
            background: rgba(102, 126, 234, 0.1);
            text-decoration: none;
        }

        /* الأزرار المحسنة */
        .enhanced-btn {
            width: 100%;
            padding: 18px 30px;
            border: none;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            text-decoration: none;
            box-shadow: var(--shadow-soft);
        }

        .enhanced-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .enhanced-btn:hover::before {
            left: 100%;
        }

        .enhanced-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-strong);
        }

        .enhanced-btn:active {
            transform: translateY(-1px);
        }

        .enhanced-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .btn-secondary {
            background: rgba(149, 165, 166, 0.1);
            color: var(--dark-color);
            border: 2px solid rgba(149, 165, 166, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(149, 165, 166, 0.2);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: var(--dark-color);
        }

        /* روابط المساعدة المحسنة */
        .help-section {
            margin-top: 30px;
            padding-top: 25px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .help-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .help-link {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            text-decoration: none;
            font-size: 0.95rem;
            font-weight: 500;
            padding: 10px 15px;
            border-radius: 10px;
            transition: var(--transition);
            background: rgba(102, 126, 234, 0.05);
        }

        .help-link:hover {
            background: rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
            text-decoration: none;
        }

        /* بطاقة طلب التفعيل المحسنة */
        .activation-card {
            max-width: 800px;
            width: 100%;
        }

        .card-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .back-btn {
            position: absolute;
            right: 0;
            top: 0;
            background: rgba(149, 165, 166, 0.1);
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            cursor: pointer;
            color: #7f8c8d;
            font-size: 1.3rem;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .back-btn:hover {
            background: rgba(149, 165, 166, 0.2);
            color: var(--dark-color);
            transform: scale(1.1);
        }

        .progress-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 15px;
        }

        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(149, 165, 166, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-weight: 600;
            transition: var(--transition);
            position: relative;
        }

        .progress-step.active {
            background: var(--primary-gradient);
            color: white;
            transform: scale(1.1);
        }

        .progress-step.completed {
            background: var(--success-gradient);
            color: white;
        }

        .progress-step::after {
            content: '';
            position: absolute;
            left: 100%;
            top: 50%;
            width: 15px;
            height: 2px;
            background: rgba(149, 165, 166, 0.3);
            transform: translateY(-50%);
        }

        .progress-step:last-child::after {
            display: none;
        }

        .progress-step.completed::after {
            background: #4facfe;
        }

        /* أقسام النموذج المحسنة */
        .form-section {
            margin-bottom: 35px;
            padding: 25px;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 15px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: var(--transition);
        }

        .form-section:hover {
            background: rgba(0, 0, 0, 0.03);
            border-color: rgba(102, 126, 234, 0.2);
        }

        .section-title {
            color: var(--dark-color);
            font-size: 1.3rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
        }

        .section-icon {
            width: 35px;
            height: 35px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .form-row.single {
            grid-template-columns: 1fr;
        }

        /* خيارات الترخيص المحسنة */
        .license-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }

        .license-option {
            position: relative;
            cursor: pointer;
            transition: var(--transition);
        }

        .license-option input[type="radio"] {
            display: none;
        }

        .license-card {
            padding: 25px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            background: white;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .license-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .license-option:hover .license-card {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: var(--shadow-soft);
        }

        .license-option input[type="radio"]:checked + .license-card {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
            transform: translateY(-5px);
            box-shadow: var(--shadow-strong);
        }

        .license-option input[type="radio"]:checked + .license-card::before {
            transform: scaleX(1);
        }

        .license-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .license-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--dark-color);
        }

        .license-price {
            font-size: 1.1rem;
            font-weight: 600;
            color: #667eea;
        }

        .license-duration {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .license-features {
            list-style: none;
            margin: 15px 0;
        }

        .license-features li {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: var(--dark-color);
        }

        .license-features li::before {
            content: '✓';
            color: #4caf50;
            font-weight: bold;
            width: 16px;
            height: 16px;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .popular-badge {
            position: absolute;
            top: -10px;
            left: 20px;
            background: var(--secondary-gradient);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            box-shadow: var(--shadow-soft);
        }

        /* منطقة النص المحسنة */
        .enhanced-textarea {
            width: 100%;
            min-height: 120px;
            padding: 18px 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: var(--transition);
            outline: none;
            resize: vertical;
            font-family: inherit;
            line-height: 1.6;
        }

        .enhanced-textarea:focus {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
        }

        .character-counter {
            text-align: left;
            font-size: 0.85rem;
            color: #7f8c8d;
            margin-top: 8px;
        }

        /* معلومات النظام المحسنة */
        .system-info {
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
        }

        .system-info-title {
            color: #667eea;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .system-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .system-info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .system-info-label {
            color: var(--dark-color);
            font-weight: 500;
        }

        .system-info-value {
            color: #667eea;
            font-weight: 600;
            font-family: monospace;
        }

        /* رسائل التنبيه المحسنة */
        .toast-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10000;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .toast {
            background: white;
            border-radius: 15px;
            padding: 20px 25px;
            box-shadow: var(--shadow-strong);
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 350px;
            transform: translateX(-100%);
            transition: var(--transition);
            border-left: 5px solid #4caf50;
            position: relative;
            overflow: hidden;
        }

        .toast::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: #4caf50;
            animation: toast-progress 5s linear;
        }

        @keyframes toast-progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-left-color: #4caf50;
        }

        .toast.success::before {
            background: #4caf50;
        }

        .toast.error {
            border-left-color: #f44336;
        }

        .toast.error::before {
            background: #f44336;
        }

        .toast.warning {
            border-left-color: #ff9800;
        }

        .toast.warning::before {
            background: #ff9800;
        }

        .toast-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .toast.success .toast-icon {
            background: #4caf50;
        }

        .toast.error .toast-icon {
            background: #f44336;
        }

        .toast.warning .toast-icon {
            background: #ff9800;
        }

        .toast-content {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .toast-message {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .toast-close {
            background: none;
            border: none;
            color: #bdc3c7;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 5px;
            border-radius: 50%;
            transition: var(--transition);
        }

        .toast-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--dark-color);
        }

        /* التجاوب المحسن */
        @media (max-width: 768px) {
            .main-container {
                padding: 10px;
            }

            .card {
                padding: 25px;
                margin: 10px;
            }

            .app-title {
                font-size: 1.8rem;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .license-options {
                grid-template-columns: 1fr;
            }

            .help-links {
                flex-direction: column;
                gap: 15px;
            }

            .toast {
                min-width: 300px;
                margin: 0 10px;
            }

            .toast-container {
                left: 0;
                right: 0;
                top: 10px;
            }

            .system-info-grid {
                grid-template-columns: 1fr;
            }

            .progress-indicator {
                gap: 10px;
            }

            .progress-step {
                width: 35px;
                height: 35px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .card {
                padding: 20px;
            }

            .app-title {
                font-size: 1.6rem;
            }

            .logo-icon {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .enhanced-btn {
                padding: 15px 25px;
                font-size: 1rem;
            }

            .form-input {
                padding: 15px 50px 15px 15px;
            }

            .license-card {
                padding: 20px;
            }
        }

        /* تأثيرات إضافية */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .bounce-in {
            animation: bounceIn 0.8s ease-out;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
    </style>
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-background">
        <div class="floating-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="main-container">
        <div class="login-wrapper">

            <!-- بطاقة تسجيل الدخول المحسنة -->
            <div class="card login-card fade-in" id="loginCard">
                <div class="login-header">
                    <div class="logo-container">
                        <div class="logo-icon bounce-in">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <div>
                            <h1 class="app-title">مؤسسة وقود المستقبل</h1>
                            <p class="app-subtitle">نظام إدارة شامل ومتطور</p>
                            <span class="version-badge">الإصدار 2.2.0 Enhanced</span>
                        </div>
                    </div>
                </div>

                <form class="enhanced-form" id="loginForm">
                    <div class="form-group">
                        <div class="input-container">
                            <input type="text" class="form-input" id="username" placeholder=" " required>
                            <label class="form-label" for="username">اسم المستخدم</label>
                            <i class="fas fa-user input-icon"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-container">
                            <input type="password" class="form-input" id="password" placeholder=" " required>
                            <label class="form-label" for="password">كلمة المرور</label>
                            <i class="fas fa-lock input-icon"></i>
                            <button type="button" class="toggle-password" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="rememberMe">
                            <div class="custom-checkbox"></div>
                            تذكرني
                        </label>
                        <a href="#" class="forgot-password" onclick="showForgotPassword()">
                            نسيت كلمة المرور؟
                        </a>
                    </div>

                    <button type="submit" class="enhanced-btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>تسجيل الدخول</span>
                    </button>
                </form>

                <div class="help-section">
                    <div class="help-links">
                        <a href="#" class="help-link" onclick="showActivationCard()">
                            <i class="fas fa-key"></i>
                            طلب تفعيل البرنامج
                        </a>
                        <a href="#" class="help-link" onclick="showTrackingCard()">
                            <i class="fas fa-search"></i>
                            تتبع طلب التفعيل
                        </a>
                        <a href="#" class="help-link" onclick="showHelpModal()">
                            <i class="fas fa-question-circle"></i>
                            المساعدة
                        </a>
                        <a href="#" class="help-link" onclick="showAboutModal()">
                            <i class="fas fa-info-circle"></i>
                            حول البرنامج
                        </a>
                    </div>
                </div>
            </div>

            <!-- بطاقة طلب التفعيل المحسنة -->
            <div class="card activation-card hidden slide-in-right" id="activationCard">
                <div class="card-header">
                    <button class="back-btn" onclick="showLoginCard()">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <h2 class="app-title">طلب تفعيل البرنامج</h2>
                    <p class="app-subtitle">املأ البيانات التالية للحصول على ترخيص</p>
                </div>

                <div class="progress-indicator">
                    <div class="progress-step active">1</div>
                    <div class="progress-step">2</div>
                    <div class="progress-step">3</div>
                    <div class="progress-step">4</div>
                </div>

                <form class="enhanced-form" id="activationForm">
                    <!-- القسم الأول: المعلومات الشخصية -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            المعلومات الشخصية
                        </h3>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="input-container">
                                    <input type="text" class="form-input" id="fullName" placeholder=" " required>
                                    <label class="form-label" for="fullName">الاسم الكامل *</label>
                                    <i class="fas fa-id-card input-icon"></i>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-container">
                                    <input type="email" class="form-input" id="email" placeholder=" " required>
                                    <label class="form-label" for="email">البريد الإلكتروني *</label>
                                    <i class="fas fa-envelope input-icon"></i>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="input-container">
                                    <input type="tel" class="form-input" id="phone" placeholder=" " required>
                                    <label class="form-label" for="phone">رقم الهاتف *</label>
                                    <i class="fas fa-phone input-icon"></i>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-container">
                                    <input type="text" class="form-input" id="company" placeholder=" ">
                                    <label class="form-label" for="company">اسم الشركة (اختياري)</label>
                                    <i class="fas fa-building input-icon"></i>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="input-container">
                                    <input type="text" class="form-input" id="jobTitle" placeholder=" ">
                                    <label class="form-label" for="jobTitle">المسمى الوظيفي (اختياري)</label>
                                    <i class="fas fa-briefcase input-icon"></i>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-container">
                                    <select class="form-input" id="country" required>
                                        <option value="">اختر الدولة</option>
                                        <option value="SA">المملكة العربية السعودية</option>
                                        <option value="AE">الإمارات العربية المتحدة</option>
                                        <option value="KW">الكويت</option>
                                        <option value="QA">قطر</option>
                                        <option value="BH">البحرين</option>
                                        <option value="OM">عمان</option>
                                        <option value="JO">الأردن</option>
                                        <option value="LB">لبنان</option>
                                        <option value="EG">مصر</option>
                                        <option value="MA">المغرب</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                    <label class="form-label" for="country">الدولة *</label>
                                    <i class="fas fa-globe input-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- القسم الثاني: خيارات الترخيص -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            اختيار نوع الترخيص
                        </h3>

                        <div class="license-options">
                            <label class="license-option">
                                <input type="radio" name="licenseType" value="trial" required>
                                <div class="license-card">
                                    <div class="license-header">
                                        <div class="license-name">تجريبي</div>
                                        <div class="license-price">مجاني</div>
                                    </div>
                                    <div class="license-duration">مدة الترخيص: 30 يوم</div>
                                    <ul class="license-features">
                                        <li>جميع المميزات الأساسية</li>
                                        <li>حد أقصى 50 زبون</li>
                                        <li>حد أقصى 100 بطاقة غاز</li>
                                        <li>دعم فني محدود</li>
                                        <li>تقارير أساسية</li>
                                    </ul>
                                </div>
                            </label>

                            <label class="license-option">
                                <input type="radio" name="licenseType" value="basic" required>
                                <div class="license-card">
                                    <div class="license-header">
                                        <div class="license-name">أساسي</div>
                                        <div class="license-price">$50/شهر</div>
                                    </div>
                                    <div class="license-duration">مدة الترخيص: شهر واحد</div>
                                    <ul class="license-features">
                                        <li>جميع المميزات الأساسية</li>
                                        <li>حد أقصى 200 زبون</li>
                                        <li>حد أقصى 500 بطاقة غاز</li>
                                        <li>دعم فني عبر البريد</li>
                                        <li>نسخ احتياطية يومية</li>
                                        <li>تقارير متقدمة</li>
                                    </ul>
                                </div>
                            </label>

                            <label class="license-option">
                                <input type="radio" name="licenseType" value="professional" required>
                                <div class="license-card">
                                    <div class="popular-badge">الأكثر شعبية</div>
                                    <div class="license-header">
                                        <div class="license-name">احترافي</div>
                                        <div class="license-price">$500/سنة</div>
                                    </div>
                                    <div class="license-duration">مدة الترخيص: سنة كاملة</div>
                                    <ul class="license-features">
                                        <li>جميع المميزات</li>
                                        <li>عدد غير محدود من السجلات</li>
                                        <li>دعم فني على مدار الساعة</li>
                                        <li>تحديثات مجانية</li>
                                        <li>نسخ احتياطية متقدمة</li>
                                        <li>تقارير مخصصة</li>
                                        <li>تكامل مع الأنظمة الخارجية</li>
                                    </ul>
                                </div>
                            </label>

                            <label class="license-option">
                                <input type="radio" name="licenseType" value="enterprise" required>
                                <div class="license-card">
                                    <div class="license-header">
                                        <div class="license-name">مؤسسي</div>
                                        <div class="license-price">حسب الطلب</div>
                                    </div>
                                    <div class="license-duration">مدة الترخيص: حسب الاتفاق</div>
                                    <ul class="license-features">
                                        <li>جميع مميزات الإصدار الاحترافي</li>
                                        <li>حلول مخصصة</li>
                                        <li>تدريب مخصص</li>
                                        <li>دعم فني مخصص</li>
                                        <li>استضافة خاصة</li>
                                        <li>تخصيص كامل للواجهة</li>
                                        <li>تكامل متقدم</li>
                                    </ul>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- القسم الثالث: معلومات إضافية -->
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-comment-alt"></i>
                            </div>
                            معلومات إضافية
                        </h3>

                        <div class="form-row single">
                            <div class="form-group">
                                <label class="form-label" for="notes" style="position: static; transform: none; background: none; padding: 0; margin-bottom: 10px; color: var(--dark-color); font-weight: 600;">
                                    ملاحظات أو متطلبات خاصة (اختياري)
                                </label>
                                <textarea class="enhanced-textarea" id="notes" placeholder="أضف أي ملاحظات أو متطلبات خاصة هنا..." maxlength="500"></textarea>
                                <div class="character-counter">
                                    <span id="notesCounter">0</span>/500 حرف
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="input-container">
                                    <select class="form-input" id="businessType">
                                        <option value="">اختر نوع النشاط</option>
                                        <option value="gas-station">محطة وقود</option>
                                        <option value="gas-distributor">موزع غاز</option>
                                        <option value="transport">شركة نقل</option>
                                        <option value="logistics">شركة لوجستية</option>
                                        <option value="retail">تجارة تجزئة</option>
                                        <option value="wholesale">تجارة جملة</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                    <label class="form-label" for="businessType">نوع النشاط التجاري</label>
                                    <i class="fas fa-industry input-icon"></i>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-container">
                                    <select class="form-input" id="expectedUsers">
                                        <option value="">اختر العدد المتوقع</option>
                                        <option value="1-5">1-5 مستخدمين</option>
                                        <option value="6-10">6-10 مستخدمين</option>
                                        <option value="11-25">11-25 مستخدم</option>
                                        <option value="26-50">26-50 مستخدم</option>
                                        <option value="51-100">51-100 مستخدم</option>
                                        <option value="100+">أكثر من 100 مستخدم</option>
                                    </select>
                                    <label class="form-label" for="expectedUsers">عدد المستخدمين المتوقع</label>
                                    <i class="fas fa-users input-icon"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- القسم الرابع: معلومات النظام -->
                    <div class="system-info">
                        <div class="system-info-title">
                            <i class="fas fa-desktop"></i>
                            معلومات النظام (تم جمعها تلقائياً)
                        </div>
                        <div class="system-info-grid" id="systemInfoGrid">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="form-row">
                        <button type="button" class="enhanced-btn btn-secondary" onclick="showLoginCard()">
                            <i class="fas fa-arrow-right"></i>
                            <span>العودة</span>
                        </button>
                        <button type="submit" class="enhanced-btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            <span>إرسال طلب التفعيل</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- بطاقة تتبع الطلب -->
            <div class="card tracking-card hidden slide-in-right" id="trackingCard">
                <div class="card-header">
                    <button class="back-btn" onclick="showLoginCard()">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    <h2 class="app-title">تتبع طلب التفعيل</h2>
                    <p class="app-subtitle">أدخل رقم الطلب والبريد الإلكتروني للتتبع</p>
                </div>

                <form class="enhanced-form" id="trackingForm">
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            بيانات التتبع
                        </h3>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="input-container">
                                    <input type="text" class="form-input" id="trackingId" placeholder=" " required>
                                    <label class="form-label" for="trackingId">رقم الطلب *</label>
                                    <i class="fas fa-hashtag input-icon"></i>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-container">
                                    <input type="email" class="form-input" id="trackingEmail" placeholder=" " required>
                                    <label class="form-label" for="trackingEmail">البريد الإلكتروني *</label>
                                    <i class="fas fa-envelope input-icon"></i>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <button type="button" class="enhanced-btn btn-secondary" onclick="showLoginCard()">
                                <i class="fas fa-arrow-right"></i>
                                <span>العودة</span>
                            </button>
                            <button type="submit" class="enhanced-btn btn-primary">
                                <i class="fas fa-search"></i>
                                <span>تتبع الطلب</span>
                            </button>
                        </div>
                    </div>
                </form>

                <!-- نتائج التتبع -->
                <div class="tracking-results hidden" id="trackingResults">
                    <div class="form-section">
                        <h3 class="section-title">
                            <div class="section-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            حالة الطلب
                        </h3>

                        <div id="trackingStatus">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- حاوي رسائل التنبيه -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- النوافذ المنبثقة -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <div class="modal-content" id="modalContent">
            <!-- محتوى النافذة المنبثقة -->
        </div>
    </div>

    <script>
        // متغيرات عامة
        let currentCard = 'login';
        let systemInfo = {};
        let activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل النظام المحسن');

            // جمع معلومات النظام
            collectSystemInfo();

            // إعداد مستمعي الأحداث
            setupEventListeners();

            // تحديث عداد الأحرف
            updateCharacterCounter();

            // إضافة تأثيرات الحركة
            addAnimations();

            console.log('✅ تم تحميل النظام بنجاح');
        });

        // جمع معلومات النظام
        function collectSystemInfo() {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');

            systemInfo = {
                deviceId: generateDeviceId(),
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                screenResolution: `${screen.width}x${screen.height}`,
                colorDepth: screen.colorDepth,
                pixelRatio: window.devicePixelRatio,
                cookieEnabled: navigator.cookieEnabled,
                onlineStatus: navigator.onLine,
                hardwareConcurrency: navigator.hardwareConcurrency,
                maxTouchPoints: navigator.maxTouchPoints,
                webglRenderer: gl ? gl.getParameter(gl.RENDERER) : 'غير متاح',
                timestamp: new Date().toISOString()
            };

            // تحديث واجهة معلومات النظام
            updateSystemInfoDisplay();
        }

        // إنشاء معرف فريد للجهاز
        function generateDeviceId() {
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                deviceId = 'FF-' + Date.now().toString(36) + '-' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('deviceId', deviceId);
            }
            return deviceId;
        }

        // تحديث عرض معلومات النظام
        function updateSystemInfoDisplay() {
            const container = document.getElementById('systemInfoGrid');
            if (!container) return;

            const displayInfo = {
                'معرف الجهاز': systemInfo.deviceId,
                'نظام التشغيل': getOSName(),
                'المتصفح': getBrowserName(),
                'دقة الشاشة': systemInfo.screenResolution,
                'المنطقة الزمنية': systemInfo.timezone,
                'اللغة': systemInfo.language
            };

            container.innerHTML = Object.entries(displayInfo).map(([label, value]) => `
                <div class="system-info-item">
                    <span class="system-info-label">${label}:</span>
                    <span class="system-info-value">${value}</span>
                </div>
            `).join('');
        }

        // الحصول على اسم نظام التشغيل
        function getOSName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Windows')) return 'Windows';
            if (userAgent.includes('Mac')) return 'macOS';
            if (userAgent.includes('Linux')) return 'Linux';
            if (userAgent.includes('Android')) return 'Android';
            if (userAgent.includes('iOS')) return 'iOS';
            return 'غير محدد';
        }

        // الحصول على اسم المتصفح
        function getBrowserName() {
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) return 'Chrome';
            if (userAgent.includes('Firefox')) return 'Firefox';
            if (userAgent.includes('Safari')) return 'Safari';
            if (userAgent.includes('Edge')) return 'Edge';
            if (userAgent.includes('Opera')) return 'Opera';
            return 'غير محدد';
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // نموذج تسجيل الدخول
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // نموذج طلب التفعيل
            document.getElementById('activationForm').addEventListener('submit', handleActivationRequest);

            // نموذج التتبع
            document.getElementById('trackingForm').addEventListener('submit', handleTracking);

            // عداد الأحرف
            document.getElementById('notes').addEventListener('input', updateCharacterCounter);

            // تحديث مؤشر التقدم
            const formInputs = document.querySelectorAll('#activationForm input[required], #activationForm select[required]');
            formInputs.forEach(input => {
                input.addEventListener('input', updateProgressIndicator);
                input.addEventListener('change', updateProgressIndicator);
            });
        }

        // تحديث عداد الأحرف
        function updateCharacterCounter() {
            const textarea = document.getElementById('notes');
            const counter = document.getElementById('notesCounter');
            if (textarea && counter) {
                counter.textContent = textarea.value.length;
            }
        }

        // تحديث مؤشر التقدم
        function updateProgressIndicator() {
            const steps = document.querySelectorAll('.progress-step');
            const requiredInputs = document.querySelectorAll('#activationForm input[required], #activationForm select[required]');

            let completedSections = 0;
            let personalInfoComplete = true;
            let licenseSelected = false;

            // فحص المعلومات الشخصية
            const personalInputs = ['fullName', 'email', 'phone', 'country'];
            personalInputs.forEach(id => {
                const input = document.getElementById(id);
                if (!input || !input.value.trim()) {
                    personalInfoComplete = false;
                }
            });

            // فحص اختيار الترخيص
            const licenseInputs = document.querySelectorAll('input[name="licenseType"]');
            licenseSelected = Array.from(licenseInputs).some(input => input.checked);

            // تحديث الخطوات
            if (personalInfoComplete) {
                steps[0].classList.add('completed');
                steps[1].classList.add('active');
                completedSections++;
            }

            if (licenseSelected) {
                steps[1].classList.add('completed');
                steps[2].classList.add('active');
                completedSections++;
            }

            if (completedSections >= 2) {
                steps[2].classList.add('completed');
                steps[3].classList.add('active');
            }
        }

        // إضافة تأثيرات الحركة
        function addAnimations() {
            // تأثير الظهور التدريجي للعناصر
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // مراقبة العناصر
            document.querySelectorAll('.form-section, .license-option').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'all 0.6s ease-out';
                observer.observe(el);
            });
        }

        // معالجة تسجيل الدخول
        async function handleLogin(event) {
            event.preventDefault();
            console.log('🔐 محاولة تسجيل الدخول');

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!username || !password) {
                showToast('يرجى ملء جميع الحقول المطلوبة', 'error', 'خطأ في البيانات');
                return;
            }

            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // إظهار حالة التحميل
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري التحقق...</span>';

            try {
                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 2000));

                // بيانات تجريبية للاختبار
                const validCredentials = [
                    { username: 'admin', password: 'admin123' },
                    { username: 'user', password: 'user123' },
                    { username: 'test', password: 'test123' }
                ];

                const isValid = validCredentials.some(cred =>
                    cred.username === username && cred.password === password
                );

                if (isValid) {
                    // حفظ بيانات تسجيل الدخول
                    const loginData = {
                        username,
                        loginTime: new Date().toISOString(),
                        rememberMe,
                        systemInfo
                    };

                    if (rememberMe) {
                        localStorage.setItem('loginData', JSON.stringify(loginData));
                    } else {
                        sessionStorage.setItem('loginData', JSON.stringify(loginData));
                    }

                    showToast('تم تسجيل الدخول بنجاح!', 'success', 'مرحباً بك');

                    // التوجيه للتطبيق الرئيسي
                    setTimeout(() => {
                        window.location.href = '../app/main.html';
                    }, 2000);

                } else {
                    throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showToast(error.message, 'error', 'فشل تسجيل الدخول');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // معالجة طلب التفعيل
        async function handleActivationRequest(event) {
            event.preventDefault();
            console.log('📋 إرسال طلب تفعيل');

            const formData = new FormData(event.target);
            const data = {};

            // جمع البيانات من النموذج
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // إضافة البيانات الإضافية
            data.fullName = document.getElementById('fullName').value;
            data.email = document.getElementById('email').value;
            data.phone = document.getElementById('phone').value;
            data.company = document.getElementById('company').value;
            data.jobTitle = document.getElementById('jobTitle').value;
            data.country = document.getElementById('country').value;
            data.businessType = document.getElementById('businessType').value;
            data.expectedUsers = document.getElementById('expectedUsers').value;
            data.notes = document.getElementById('notes').value;

            // التحقق من البيانات المطلوبة
            const requiredFields = ['fullName', 'email', 'phone', 'country', 'licenseType'];
            const missingFields = requiredFields.filter(field => !data[field]);

            if (missingFields.length > 0) {
                showToast('يرجى ملء جميع الحقول المطلوبة', 'error', 'بيانات ناقصة');
                return;
            }

            // التحقق من صحة البريد الإلكتروني
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(data.email)) {
                showToast('يرجى إدخال بريد إلكتروني صحيح', 'error', 'خطأ في البريد الإلكتروني');
                return;
            }

            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // إظهار حالة التحميل
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري الإرسال...</span>';

            try {
                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 3000));

                // إنشاء طلب التفعيل
                const activationRequest = {
                    id: generateRequestId(),
                    data: data,
                    systemInfo: systemInfo,
                    status: 'pending',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };

                // حفظ الطلب
                activationRequests.push(activationRequest);
                localStorage.setItem('activationRequests', JSON.stringify(activationRequests));

                showToast(
                    `تم إرسال طلب التفعيل بنجاح! رقم الطلب: ${activationRequest.id}`,
                    'success',
                    'تم الإرسال بنجاح'
                );

                // إعادة تعيين النموذج
                setTimeout(() => {
                    event.target.reset();
                    updateProgressIndicator();
                    showLoginCard();
                }, 3000);

            } catch (error) {
                console.error('خطأ في إرسال الطلب:', error);
                showToast('حدث خطأ في إرسال الطلب، يرجى المحاولة مرة أخرى', 'error', 'فشل الإرسال');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // إنشاء رقم طلب فريد
        function generateRequestId() {
            const prefix = 'FF';
            const timestamp = Date.now().toString(36).toUpperCase();
            const random = Math.random().toString(36).substr(2, 4).toUpperCase();
            return `${prefix}-${timestamp}-${random}`;
        }

        // معالجة تتبع الطلب
        async function handleTracking(event) {
            event.preventDefault();
            console.log('🔍 تتبع طلب التفعيل');

            const trackingId = document.getElementById('trackingId').value.trim();
            const trackingEmail = document.getElementById('trackingEmail').value.trim();

            if (!trackingId || !trackingEmail) {
                showToast('يرجى ملء جميع الحقول المطلوبة', 'error', 'بيانات ناقصة');
                return;
            }

            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // إظهار حالة التحميل
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري البحث...</span>';

            try {
                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 2000));

                // البحث عن الطلب
                const request = activationRequests.find(req =>
                    req.id === trackingId && req.data.email === trackingEmail
                );

                if (request) {
                    displayTrackingResults(request);
                    showToast('تم العثور على الطلب!', 'success', 'نتائج البحث');
                } else {
                    throw new Error('لم يتم العثور على طلب بهذه البيانات');
                }

            } catch (error) {
                console.error('خطأ في التتبع:', error);
                showToast(error.message, 'error', 'لم يتم العثور على الطلب');
                document.getElementById('trackingResults').classList.add('hidden');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // عرض نتائج التتبع
        function displayTrackingResults(request) {
            const resultsContainer = document.getElementById('trackingResults');
            const statusContainer = document.getElementById('trackingStatus');

            const statusMap = {
                'pending': { text: 'معلق', color: '#ff9800', icon: 'fas fa-clock' },
                'processing': { text: 'قيد المعالجة', color: '#2196f3', icon: 'fas fa-cog fa-spin' },
                'approved': { text: 'مقبول', color: '#4caf50', icon: 'fas fa-check-circle' },
                'rejected': { text: 'مرفوض', color: '#f44336', icon: 'fas fa-times-circle' }
            };

            const status = statusMap[request.status] || statusMap['pending'];

            statusContainer.innerHTML = `
                <div style="text-align: center; padding: 30px;">
                    <div style="font-size: 4rem; color: ${status.color}; margin-bottom: 20px;">
                        <i class="${status.icon}"></i>
                    </div>
                    <h3 style="color: var(--dark-color); margin-bottom: 15px; font-size: 1.5rem;">
                        حالة الطلب: ${status.text}
                    </h3>
                    <div style="background: rgba(0,0,0,0.05); padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; text-align: right;">
                            <div><strong>رقم الطلب:</strong> ${request.id}</div>
                            <div><strong>اسم المتقدم:</strong> ${request.data.fullName}</div>
                            <div><strong>نوع الترخيص:</strong> ${getLicenseTypeText(request.data.licenseType)}</div>
                            <div><strong>تاريخ الطلب:</strong> ${new Date(request.createdAt).toLocaleDateString('ar-DZ')}</div>
                        </div>
                    </div>
                    ${request.status === 'approved' ? `
                        <div style="background: rgba(76, 175, 80, 0.1); border: 1px solid rgba(76, 175, 80, 0.3); padding: 15px; border-radius: 10px; margin-top: 20px;">
                            <p style="color: #4caf50; font-weight: 600;">
                                <i class="fas fa-info-circle"></i>
                                تم قبول طلبك! سيتم إرسال رابط التحميل والترخيص إلى بريدك الإلكتروني قريباً.
                            </p>
                        </div>
                    ` : ''}
                    ${request.status === 'rejected' ? `
                        <div style="background: rgba(244, 67, 54, 0.1); border: 1px solid rgba(244, 67, 54, 0.3); padding: 15px; border-radius: 10px; margin-top: 20px;">
                            <p style="color: #f44336; font-weight: 600;">
                                <i class="fas fa-exclamation-triangle"></i>
                                تم رفض طلبك. يرجى التواصل مع الدعم الفني للمزيد من المعلومات.
                            </p>
                        </div>
                    ` : ''}
                </div>
            `;

            resultsContainer.classList.remove('hidden');
        }

        // الحصول على نص نوع الترخيص
        function getLicenseTypeText(type) {
            const typeMap = {
                'trial': 'تجريبي',
                'basic': 'أساسي',
                'professional': 'احترافي',
                'enterprise': 'مؤسسي'
            };
            return typeMap[type] || type;
        }

        // وظائف التنقل بين البطاقات
        function showLoginCard() {
            hideAllCards();
            document.getElementById('loginCard').classList.remove('hidden');
            currentCard = 'login';
        }

        function showActivationCard() {
            hideAllCards();
            document.getElementById('activationCard').classList.remove('hidden');
            currentCard = 'activation';
            updateSystemInfoDisplay();
        }

        function showTrackingCard() {
            hideAllCards();
            document.getElementById('trackingCard').classList.remove('hidden');
            currentCard = 'tracking';
            document.getElementById('trackingResults').classList.add('hidden');
        }

        function hideAllCards() {
            document.querySelectorAll('.card').forEach(card => {
                card.classList.add('hidden');
            });
        }

        // تبديل إظهار كلمة المرور
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.parentElement.querySelector('.toggle-password i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // إظهار رسائل التنبيه المحسنة
        function showToast(message, type = 'info', title = '') {
            const container = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();

            const iconMap = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.id = toastId;

            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="${iconMap[type]}"></i>
                </div>
                <div class="toast-content">
                    ${title ? `<div class="toast-title">${title}</div>` : ''}
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="closeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(toast);

            // إظهار التنبيه
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // إخفاء التنبيه تلقائياً
            setTimeout(() => {
                closeToast(toastId);
            }, 5000);
        }

        // إغلاق رسالة التنبيه
        function closeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.remove('show');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // النوافذ المنبثقة
        function showHelpModal() {
            const modalContent = `
                <div style="padding: 30px; text-align: center;">
                    <h2 style="color: var(--dark-color); margin-bottom: 20px;">
                        <i class="fas fa-question-circle" style="color: #667eea;"></i>
                        المساعدة والدعم
                    </h2>
                    <div style="text-align: right; line-height: 1.8;">
                        <h3 style="color: #667eea; margin: 20px 0 10px;">كيفية تسجيل الدخول:</h3>
                        <p>• أدخل اسم المستخدم وكلمة المرور الخاصة بك</p>
                        <p>• يمكنك استخدام البيانات التجريبية: admin / admin123</p>

                        <h3 style="color: #667eea; margin: 20px 0 10px;">طلب تفعيل جديد:</h3>
                        <p>• اضغط على "طلب تفعيل البرنامج"</p>
                        <p>• املأ جميع البيانات المطلوبة</p>
                        <p>• اختر نوع الترخيص المناسب</p>
                        <p>• ستحصل على رقم طلب للتتبع</p>

                        <h3 style="color: #667eea; margin: 20px 0 10px;">تتبع الطلب:</h3>
                        <p>• استخدم رقم الطلب والبريد الإلكتروني</p>
                        <p>• يمكنك متابعة حالة طلبك في أي وقت</p>

                        <h3 style="color: #667eea; margin: 20px 0 10px;">الدعم الفني:</h3>
                        <p>• البريد الإلكتروني: <EMAIL></p>
                        <p>• الهاتف: +966 50 123 4567</p>
                        <p>• ساعات العمل: الأحد - الخميس 8:00 ص - 6:00 م</p>
                    </div>
                    <button onclick="closeModal()" style="margin-top: 20px; padding: 10px 30px; background: var(--primary-gradient); color: white; border: none; border-radius: 8px; cursor: pointer;">
                        إغلاق
                    </button>
                </div>
            `;
            showModal(modalContent);
        }

        function showAboutModal() {
            const modalContent = `
                <div style="padding: 30px; text-align: center;">
                    <div style="margin-bottom: 30px;">
                        <div style="width: 80px; height: 80px; background: var(--primary-gradient); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2.5rem; margin: 0 auto 20px;">
                            <i class="fas fa-gas-pump"></i>
                        </div>
                        <h2 style="color: var(--dark-color); margin-bottom: 10px;">مؤسسة وقود المستقبل</h2>
                        <p style="color: #7f8c8d; font-size: 1.1rem;">نظام إدارة شامل ومتطور</p>
                        <span style="background: var(--success-gradient); color: white; padding: 5px 15px; border-radius: 20px; font-size: 0.9rem; margin-top: 10px; display: inline-block;">الإصدار 2.2.0 Enhanced</span>
                    </div>

                    <div style="text-align: right; line-height: 1.8; margin: 30px 0;">
                        <h3 style="color: #667eea; margin-bottom: 15px;">حول النظام:</h3>
                        <p>نظام إدارة شامل مصمم خصيصاً لإدارة محطات الوقود ومحلات الغاز. يوفر النظام جميع الأدوات اللازمة لإدارة الزبائن، المركبات، بطاقات الغاز، والمخزون بطريقة احترافية وآمنة.</p>

                        <h3 style="color: #667eea; margin: 20px 0 15px;">المميزات الرئيسية:</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin: 8px 0;"><i class="fas fa-check" style="color: #4caf50; margin-left: 10px;"></i> إدارة شاملة للزبائن والمركبات</li>
                            <li style="margin: 8px 0;"><i class="fas fa-check" style="color: #4caf50; margin-left: 10px;"></i> نظام بطاقات الغاز المتقدم</li>
                            <li style="margin: 8px 0;"><i class="fas fa-check" style="color: #4caf50; margin-left: 10px;"></i> تقارير مفصلة وإحصائيات</li>
                            <li style="margin: 8px 0;"><i class="fas fa-check" style="color: #4caf50; margin-left: 10px;"></i> واجهة مستخدم عصرية ومتجاوبة</li>
                            <li style="margin: 8px 0;"><i class="fas fa-check" style="color: #4caf50; margin-left: 10px;"></i> أمان متقدم وحماية البيانات</li>
                        </ul>

                        <h3 style="color: #667eea; margin: 20px 0 15px;">معلومات الشركة:</h3>
                        <p><strong>الموقع الإلكتروني:</strong> https://futurefuel.sa</p>
                        <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                        <p><strong>الهاتف:</strong> +966 50 123 4567</p>

                        <div style="margin-top: 30px; padding: 20px; background: rgba(102, 126, 234, 0.1); border-radius: 10px;">
                            <p style="color: #667eea; font-weight: 600; margin: 0;">
                                © 2024 Future Fuel Corporation. جميع الحقوق محفوظة.
                            </p>
                        </div>
                    </div>

                    <button onclick="closeModal()" style="margin-top: 20px; padding: 10px 30px; background: var(--primary-gradient); color: white; border: none; border-radius: 8px; cursor: pointer;">
                        إغلاق
                    </button>
                </div>
            `;
            showModal(modalContent);
        }

        function showModal(content) {
            const overlay = document.getElementById('modalOverlay');
            const modalContent = document.getElementById('modalContent');

            modalContent.innerHTML = content;
            overlay.style.display = 'flex';

            // تأثير الظهور
            setTimeout(() => {
                overlay.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            }, 10);
        }

        function closeModal() {
            const overlay = document.getElementById('modalOverlay');
            overlay.style.opacity = '0';

            setTimeout(() => {
                overlay.style.display = 'none';
            }, 300);
        }

        // إغلاق النافذة المنبثقة عند النقر خارجها
        document.getElementById('modalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            // Escape لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                closeModal();
            }

            // Enter لإرسال النموذج النشط
            if (e.key === 'Enter' && e.ctrlKey) {
                const activeForm = document.querySelector('.card:not(.hidden) form');
                if (activeForm) {
                    activeForm.dispatchEvent(new Event('submit'));
                }
            }
        });

        // إضافة تأثيرات CSS للنافذة المنبثقة
        const modalStyles = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(5px);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                transition: all 0.3s ease;
            }

            .modal-content {
                background: white;
                border-radius: 20px;
                box-shadow: var(--shadow-strong);
                max-width: 600px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                transform: scale(0.8);
                transition: all 0.3s ease;
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = modalStyles;
        document.head.appendChild(styleSheet);

    </script>
</body>
</html>