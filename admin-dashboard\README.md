# لوحة التحكم الإدارية
## مؤسسة وقود المستقبل - Admin Dashboard

### 🎯 **نظرة عامة**

لوحة تحكم إدارية شاملة ومنفصلة تماماً عن التطبيق الرئيسي، مخصصة للمطورين والمديرين لإدارة طلبات التفعيل والمستخدمين والتراخيص بطريقة احترافية وآمنة.

---

### ✨ **المميزات الرئيسية**

#### 🔐 **نظام مصادقة متقدم للمديرين**
- تسجيل دخول ثلاثي المستويات (اسم المستخدم + كلمة المرور + المفتاح السري)
- جلسات آمنة مع انتهاء صلاحية تلقائي
- حماية من الوصول غير المصرح به
- سجل دخول وخروج مفصل

#### 📊 **لوحة تحكم تفاعلية**
- إحصائيات شاملة في الوقت الفعلي
- رسوم بيانية تفاعلية مع Chart.js
- بطاقات إحصائيات ملونة ومتحركة
- تحديث تلقائي للبيانات

#### 📋 **إدارة طلبات التفعيل**
- عرض جميع طلبات التفعيل في جدول تفاعلي
- فلترة متقدمة حسب الحالة ونوع الترخيص والتاريخ
- عرض تفاصيل كاملة لكل طلب
- إجراءات فردية وجماعية (قبول/رفض/معالجة)
- تصدير البيانات إلى CSV

#### 👥 **إدارة المستخدمين والتراخيص**
- قائمة شاملة بجميع المستخدمين
- إدارة التراخيص وتواريخ الانتهاء
- إنشاء تراخيص جديدة
- تجديد وإلغاء التراخيص

#### 📈 **تحليلات وتقارير**
- رسوم بيانية للطلبات الشهرية
- توزيع أنواع التراخيص
- إحصائيات الحالات
- تقارير النشاط اليومي

---

### 🗂️ **بنية المشروع**

```
admin-dashboard/
├── index.html                 # الصفحة الرئيسية
├── styles/
│   ├── dashboard.css         # تنسيقات لوحة التحكم الرئيسية
│   └── components.css        # تنسيقات المكونات والعناصر
├── scripts/
│   ├── dashboard.js          # منطق لوحة التحكم الرئيسي
│   ├── requests-manager.js   # إدارة طلبات التفعيل
│   └── charts.js            # الرسوم البيانية والإحصائيات
└── README.md                 # هذا الملف
```

---

### 🚀 **كيفية الاستخدام**

#### **1. تسجيل الدخول للمديرين**
```javascript
// بيانات المدير الافتراضية (يجب تغييرها في الإنتاج)
اسم المدير: developer
كلمة المرور: dev123456
المفتاح السري: FUTUREFUEL2024ADMIN
```

#### **2. الوصول للوحة التحكم**
1. افتح `admin-dashboard/index.html`
2. أدخل بيانات المدير
3. اضغط "دخول لوحة التحكم"

#### **3. إدارة طلبات التفعيل**
- **عرض الطلبات**: انتقل إلى قسم "طلبات التفعيل"
- **فلترة الطلبات**: استخدم الفلاتر المتاحة
- **عرض التفاصيل**: اضغط على أيقونة العين
- **تغيير الحالة**: اضغط على أيقونة التعديل
- **الإجراءات الجماعية**: حدد عدة طلبات واختر الإجراء

---

### 🎛️ **الأقسام الرئيسية**

#### **1. 📊 الرئيسية (Dashboard)**
- **بطاقات الإحصائيات:**
  - إجمالي الطلبات
  - الطلبات المعلقة
  - الطلبات المقبولة
  - الطلبات المرفوضة

- **الرسوم البيانية:**
  - طلبات التفعيل الشهرية (خط بياني)
  - توزيع أنواع التراخيص (دائري)

- **الطلبات الأخيرة:**
  - قائمة بآخر 5 طلبات
  - معلومات سريعة عن كل طلب

#### **2. 📋 طلبات التفعيل**
- **جدول تفاعلي** مع جميع الطلبات
- **فلاتر متقدمة:**
  - حسب الحالة (معلق، مقبول، مرفوض، قيد المعالجة)
  - حسب نوع الترخيص (تجريبي، أساسي، احترافي، مؤسسي)
  - حسب التاريخ

- **إجراءات فردية:**
  - عرض التفاصيل الكاملة
  - تعديل حالة الطلب
  - حذف الطلب

- **إجراءات جماعية:**
  - قبول متعدد
  - رفض متعدد
  - معالجة متعددة

#### **3. 👥 إدارة المستخدمين**
- قائمة بجميع المستخدمين المسجلين
- معلومات التراخيص لكل مستخدم
- إدارة الصلاحيات والأدوار

#### **4. 🎫 إدارة التراخيص**
- إنشاء تراخيص جديدة
- تجديد التراخيص المنتهية
- إلغاء التراخيص
- تتبع تواريخ الانتهاء

#### **5. 📈 الإحصائيات والتحليلات**
- تقارير مفصلة عن الاستخدام
- إحصائيات الأداء
- تحليل اتجاهات الطلبات

#### **6. 🔔 الإشعارات**
- إشعارات الطلبات الجديدة
- تذكيرات التراخيص المنتهية
- تنبيهات النظام

#### **7. 📝 سجل النظام**
- سجل جميع العمليات
- تتبع أنشطة المديرين
- سجل الأخطاء والتحذيرات

#### **8. ⚙️ الإعدادات**
- إعدادات النظام العامة
- إدارة المديرين
- إعدادات الأمان

---

### 🔧 **المميزات التقنية**

#### **🎨 واجهة مستخدم متطورة**
- تصميم عصري ومتجاوب
- ألوان متدرجة وتأثيرات بصرية
- أيقونات Font Awesome
- انتقالات سلسة

#### **📊 رسوم بيانية تفاعلية**
- Chart.js للرسوم البيانية
- رسوم خطية ودائرية
- تفاعل مع البيانات
- تصدير الرسوم كصور

#### **🔍 بحث وفلترة متقدمة**
- بحث عام في جميع البيانات
- فلاتر متعددة المستويات
- نتائج فورية
- حفظ معايير البحث

#### **📤 تصدير البيانات**
- تصدير إلى CSV
- تصدير الرسوم البيانية
- تقارير مخصصة
- جدولة التصدير

---

### 🛡️ **الأمان والحماية**

#### **مستويات الأمان المطبقة:**
- **مصادقة ثلاثية المستويات**
- **تشفير البيانات المحلية**
- **جلسات آمنة مع انتهاء صلاحية**
- **سجل مفصل للأنشطة**
- **حماية من هجمات XSS**

#### **أفضل الممارسات:**
- تغيير بيانات المدير الافتراضية
- استخدام HTTPS في الإنتاج
- مراجعة سجل الأنشطة بانتظام
- تحديث كلمات المرور دورياً

---

### ⌨️ **اختصارات لوحة المفاتيح**

- **Ctrl + /** - التركيز على البحث العام
- **Escape** - إغلاق النوافذ المنبثقة
- **F5** - تحديث البيانات
- **Ctrl + Enter** - حفظ النموذج النشط

---

### 🔄 **سير العمل المقترح**

#### **للمطور/المدير:**
1. **تسجيل الدخول** إلى لوحة التحكم
2. **مراجعة الإحصائيات** في الصفحة الرئيسية
3. **فحص الطلبات الجديدة** في قسم طلبات التفعيل
4. **مراجعة تفاصيل كل طلب** قبل اتخاذ القرار
5. **قبول أو رفض الطلبات** مع إضافة ملاحظات
6. **إنشاء التراخيص** للطلبات المقبولة
7. **إرسال إشعارات** للمستخدمين
8. **مراجعة التقارير** والإحصائيات دورياً

---

### 📱 **التوافق والدعم**

#### **المتصفحات المدعومة:**
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

#### **الشاشات المدعومة:**
- أجهزة الكمبيوتر (1024px+)
- الأجهزة اللوحية (768px+)
- الهواتف الذكية (320px+)

---

### 🚀 **التطوير المستقبلي**

#### **المميزات المخططة:**
- [ ] نظام إشعارات في الوقت الفعلي
- [ ] تكامل مع قواعد البيانات الخارجية
- [ ] API للتكامل مع أنظمة أخرى
- [ ] تطبيق جوال للمديرين
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تقارير متقدمة مع تصدير PDF
- [ ] نظام الأدوار والصلاحيات المتقدم

---

### 🔧 **التخصيص والتطوير**

#### **تخصيص الألوان:**
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
}
```

#### **إضافة أقسام جديدة:**
1. أضف رابط في القائمة الجانبية
2. أنشئ قسم HTML جديد
3. أضف منطق JavaScript للقسم
4. حدث CSS حسب الحاجة

---

### 📞 **الدعم والمساعدة**

#### **للمطورين:**
- راجع التوثيق في ملفات الكود
- استخدم أدوات التطوير في المتصفح
- تحقق من سجل وحدة التحكم للأخطاء

#### **للمديرين:**
- راجع دليل المستخدم
- تواصل مع فريق التطوير
- استخدم نظام التذاكر للدعم

---

### 📄 **الترخيص**

```
Copyright © 2024 Future Fuel Corporation
جميع الحقوق محفوظة

هذه لوحة التحكم الإدارية محمية بحقوق الطبع والنشر.
الاستخدام مسموح فقط للمطورين والمديرين المصرح لهم.
```

---

### 🏷️ **معلومات الإصدار**

**الإصدار:** 1.0.0  
**تاريخ الإصدار:** ديسمبر 2024  
**حالة الاستقرار:** مستقر  
**آخر تحديث:** اليوم  
**المطور:** Future Fuel Corporation Development Team
