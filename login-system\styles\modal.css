/* النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8) translateY(20px);
    transition: all 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 25px;
}

/* محتوى المساعدة */
.help-content h3 {
    color: var(--dark-color);
    margin-bottom: 20px;
    font-size: 1.4rem;
}

.help-section {
    margin-bottom: 25px;
    padding: 15px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.help-section h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.help-section ul {
    margin: 10px 0;
    padding-right: 20px;
}

.help-section li {
    margin-bottom: 8px;
    color: var(--dark-color);
    line-height: 1.5;
}

.help-section p {
    margin: 8px 0;
    color: var(--dark-color);
    line-height: 1.5;
}

/* محتوى حول البرنامج */
.about-content h3 {
    color: var(--dark-color);
    margin-bottom: 20px;
    font-size: 1.4rem;
    text-align: center;
}

.about-info {
    background: rgba(0, 0, 0, 0.02);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.about-info p {
    margin: 8px 0;
    color: var(--dark-color);
    display: flex;
    justify-content: space-between;
}

.about-info strong {
    color: var(--primary-color);
}

.features-list {
    margin-bottom: 20px;
}

.features-list h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.features-list ul {
    padding-right: 20px;
}

.features-list li {
    margin-bottom: 8px;
    color: var(--dark-color);
    line-height: 1.5;
    position: relative;
}

.features-list li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
    position: absolute;
    right: -20px;
}

.copyright {
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--gray-color);
    font-size: 0.9rem;
}

/* محتوى استعادة كلمة المرور */
.forgot-password-content h3 {
    color: var(--dark-color);
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.forgot-password-content p {
    color: var(--gray-color);
    margin-bottom: 20px;
    line-height: 1.5;
}

/* محتوى التتبع */
.tracking-content h3 {
    color: var(--dark-color);
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.tracking-form .form-group {
    margin-bottom: 20px;
}

.tracking-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.tracking-form input {
    width: 100%;
    padding: 12px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.tracking-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* نتيجة التتبع */
.tracking-result h4 {
    color: var(--dark-color);
    margin-bottom: 20px;
    font-size: 1.2rem;
}

.status-timeline {
    margin-bottom: 20px;
}

.timeline-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.02);
    border-left: 4px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.timeline-item.completed {
    background: rgba(76, 175, 80, 0.1);
    border-left-color: #4caf50;
}

.timeline-item.completed i {
    color: #4caf50;
}

.timeline-item.active {
    background: rgba(102, 126, 234, 0.1);
    border-left-color: var(--primary-color);
    animation: pulse-border 2s infinite;
}

.timeline-item.active i {
    color: var(--primary-color);
    animation: spin 2s linear infinite;
}

@keyframes pulse-border {
    0%, 100% {
        border-left-width: 4px;
    }
    50% {
        border-left-width: 8px;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.timeline-item i {
    font-size: 1.2rem;
    margin-left: 15px;
    width: 20px;
    text-align: center;
}

.timeline-item span {
    flex: 1;
    font-weight: 600;
    color: var(--dark-color);
}

.timeline-item small {
    color: var(--gray-color);
    font-size: 0.85rem;
}

.tracking-note {
    background: rgba(102, 126, 234, 0.1);
    padding: 15px;
    border-radius: 8px;
    color: var(--primary-color);
    font-weight: 500;
    text-align: center;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

/* إجراءات النماذج في النوافذ */
.modal-body .form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-body .btn-primary,
.modal-body .btn-secondary {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-body .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-body .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.modal-body .btn-secondary {
    background: rgba(149, 165, 166, 0.1);
    color: var(--dark-color);
    border: 2px solid rgba(149, 165, 166, 0.3);
}

.modal-body .btn-secondary:hover {
    background: rgba(149, 165, 166, 0.2);
}

/* تجاوب النوافذ المنبثقة */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10px;
    }
    
    .modal-header {
        padding: 15px 20px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-body .form-actions {
        flex-direction: column;
    }
    
    .timeline-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .timeline-item i {
        margin-left: 0;
    }
}
