// متغيرات الرسوم البيانية
let requestsChart = null;
let licensesChart = null;

// تحديث الرسوم البيانية
function updateCharts() {
    updateRequestsChart();
    updateLicensesChart();
}

// رسم بياني لطلبات التفعيل الشهرية
function updateRequestsChart() {
    const ctx = document.getElementById('requestsChart');
    if (!ctx) return;
    
    // تدمير الرسم البياني السابق إذا كان موجوداً
    if (requestsChart) {
        requestsChart.destroy();
    }
    
    // الحصول على البيانات
    const chartData = getRequestsChartData();
    
    requestsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'طلبات التفعيل',
                data: chartData.data,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        title: function(context) {
                            return `${context[0].label}`;
                        },
                        label: function(context) {
                            return `عدد الطلبات: ${context.parsed.y}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#95a5a6',
                        font: {
                            family: 'Segoe UI',
                            size: 12
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(149, 165, 166, 0.1)',
                        drawBorder: false
                    },
                    ticks: {
                        color: '#95a5a6',
                        font: {
                            family: 'Segoe UI',
                            size: 12
                        },
                        stepSize: 1
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#667eea'
                }
            }
        }
    });
}

// رسم بياني لتوزيع أنواع التراخيص
function updateLicensesChart() {
    const ctx = document.getElementById('licensesChart');
    if (!ctx) return;
    
    // تدمير الرسم البياني السابق إذا كان موجوداً
    if (licensesChart) {
        licensesChart.destroy();
    }
    
    // الحصول على البيانات
    const chartData = getLicensesChartData();
    
    licensesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.data,
                backgroundColor: [
                    '#667eea',
                    '#4caf50',
                    '#ff9800',
                    '#f44336'
                ],
                borderColor: '#ffffff',
                borderWidth: 3,
                hoverBorderWidth: 4,
                hoverOffset: 10
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        pointStyle: 'circle',
                        font: {
                            family: 'Segoe UI',
                            size: 12
                        },
                        color: '#2c3e50'
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#667eea',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: true,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            },
            cutout: '60%',
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1000,
                easing: 'easeOutQuart'
            }
        }
    });
}

// الحصول على بيانات رسم طلبات التفعيل
function getRequestsChartData() {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    
    // إنشاء بيانات للأشهر الستة الماضية
    const months = [];
    const data = [];
    const now = new Date();
    
    for (let i = 5; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthName = date.toLocaleDateString('ar-DZ', { month: 'long', year: 'numeric' });
        months.push(monthName);
        
        // حساب عدد الطلبات في هذا الشهر
        const monthRequests = activationRequests.filter(request => {
            const requestDate = new Date(request.createdAt);
            return requestDate.getMonth() === date.getMonth() && 
                   requestDate.getFullYear() === date.getFullYear();
        });
        
        data.push(monthRequests.length);
    }
    
    return {
        labels: months,
        data: data
    };
}

// الحصول على بيانات رسم أنواع التراخيص
function getLicensesChartData() {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    
    // حساب توزيع أنواع التراخيص
    const licenseTypes = {
        'trial': 0,
        'basic': 0,
        'professional': 0,
        'enterprise': 0
    };
    
    activationRequests.forEach(request => {
        if (licenseTypes.hasOwnProperty(request.data.licenseType)) {
            licenseTypes[request.data.licenseType]++;
        }
    });
    
    const labels = [
        'تجريبي',
        'أساسي',
        'احترافي',
        'مؤسسي'
    ];
    
    const data = [
        licenseTypes.trial,
        licenseTypes.basic,
        licenseTypes.professional,
        licenseTypes.enterprise
    ];
    
    return {
        labels: labels,
        data: data
    };
}

// تحديث فترة الرسم البياني
function updateChartPeriod() {
    const period = document.getElementById('chartPeriod').value;
    
    // يمكن تطوير هذه الوظيفة لتغيير فترة الرسم البياني
    console.log('تم تغيير فترة الرسم البياني إلى:', period);
    
    // إعادة تحديث الرسم البياني بناءً على الفترة الجديدة
    updateRequestsChart();
}

// رسم بياني للإحصائيات المتقدمة (للاستخدام المستقبلي)
function createAdvancedChart(canvasId, type, data, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;
    
    const defaultOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    font: {
                        family: 'Segoe UI',
                        size: 12
                    },
                    color: '#2c3e50'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: '#667eea',
                borderWidth: 1,
                cornerRadius: 8
            }
        },
        scales: {
            x: {
                grid: {
                    color: 'rgba(149, 165, 166, 0.1)'
                },
                ticks: {
                    color: '#95a5a6',
                    font: {
                        family: 'Segoe UI',
                        size: 12
                    }
                }
            },
            y: {
                grid: {
                    color: 'rgba(149, 165, 166, 0.1)'
                },
                ticks: {
                    color: '#95a5a6',
                    font: {
                        family: 'Segoe UI',
                        size: 12
                    }
                }
            }
        }
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    
    return new Chart(ctx, {
        type: type,
        data: data,
        options: mergedOptions
    });
}

// رسم بياني لحالة الطلبات
function createStatusChart() {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    
    const statusCounts = {
        pending: 0,
        processing: 0,
        approved: 0,
        rejected: 0
    };
    
    activationRequests.forEach(request => {
        if (statusCounts.hasOwnProperty(request.status)) {
            statusCounts[request.status]++;
        }
    });
    
    const data = {
        labels: ['معلق', 'قيد المعالجة', 'مقبول', 'مرفوض'],
        datasets: [{
            data: [
                statusCounts.pending,
                statusCounts.processing,
                statusCounts.approved,
                statusCounts.rejected
            ],
            backgroundColor: [
                '#ff9800',
                '#2196f3',
                '#4caf50',
                '#f44336'
            ],
            borderWidth: 2,
            borderColor: '#ffffff'
        }]
    };
    
    return data;
}

// رسم بياني للنشاط اليومي
function createDailyActivityChart() {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    
    // إنشاء بيانات للأسبوع الماضي
    const days = [];
    const data = [];
    const now = new Date();
    
    for (let i = 6; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        
        const dayName = date.toLocaleDateString('ar-DZ', { weekday: 'short' });
        days.push(dayName);
        
        // حساب عدد الطلبات في هذا اليوم
        const dayRequests = activationRequests.filter(request => {
            const requestDate = new Date(request.createdAt);
            return requestDate.toDateString() === date.toDateString();
        });
        
        data.push(dayRequests.length);
    }
    
    return {
        labels: days,
        datasets: [{
            label: 'طلبات يومية',
            data: data,
            backgroundColor: 'rgba(102, 126, 234, 0.2)',
            borderColor: '#667eea',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    };
}

// تصدير الرسوم البيانية كصور
function exportChart(chartInstance, filename) {
    if (!chartInstance) {
        showToast('الرسم البياني غير متاح', 'error');
        return;
    }
    
    const url = chartInstance.toBase64Image();
    const link = document.createElement('a');
    link.download = filename;
    link.href = url;
    link.click();
    
    showToast('تم تصدير الرسم البياني', 'success');
}

// تحديث الرسوم البيانية عند تغيير البيانات
function refreshCharts() {
    updateCharts();
    showToast('تم تحديث الرسوم البيانية', 'info');
}

// إعداد مستمعي أحداث الرسوم البيانية
function setupChartsEventListeners() {
    // مستمع تغيير فترة الرسم البياني
    const chartPeriod = document.getElementById('chartPeriod');
    if (chartPeriod) {
        chartPeriod.addEventListener('change', updateChartPeriod);
    }
}

// تهيئة الرسوم البيانية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تأخير قصير للتأكد من تحميل Chart.js
    setTimeout(() => {
        setupChartsEventListeners();
        updateCharts();
    }, 500);
});

// تحديث الرسوم البيانية عند تغيير حجم النافذة
window.addEventListener('resize', function() {
    if (requestsChart) {
        requestsChart.resize();
    }
    if (licensesChart) {
        licensesChart.resize();
    }
});
