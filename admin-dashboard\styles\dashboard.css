/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* المتغيرات */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #2196f3;
    --dark-color: #2c3e50;
    --light-color: #ffffff;
    --gray-color: #95a5a6;
    --bg-color: #f8f9fa;
    --border-color: #e9ecef;
    --text-color: #2c3e50;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --sidebar-width: 280px;
    --header-height: 70px;
}

/* الوضع المظلم */
body.dark-mode {
    --bg-color: #1a1a1a;
    --light-color: #2c2c2c;
    --text-color: #ffffff;
    --border-color: #444444;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* الأساسيات */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    direction: rtl;
    line-height: 1.6;
    overflow-x: hidden;
}

.hidden {
    display: none !important;
}

/* شاشة تسجيل الدخول للمدير */
.admin-login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.admin-login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    padding: 40px;
    max-width: 450px;
    width: 90%;
    text-align: center;
    animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.login-header {
    margin-bottom: 30px;
}

.admin-logo i {
    font-size: 3rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
}

.admin-logo h2 {
    color: var(--dark-color);
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.admin-logo p {
    color: var(--gray-color);
    font-size: 1rem;
}

.admin-login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    position: relative;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-container input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    outline: none;
}

.input-container input:focus {
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-container input:focus + label,
.input-container input:not(:placeholder-shown) + label {
    transform: translateY(-35px) scale(0.8);
    color: var(--primary-color);
}

.input-container label {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
    font-size: 1rem;
    transition: var(--transition);
    pointer-events: none;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 8px;
}

.input-icon {
    position: absolute;
    right: 15px;
    color: var(--gray-color);
    font-size: 1.2rem;
    z-index: 1;
}

.toggle-password {
    position: absolute;
    left: 15px;
    background: none;
    border: none;
    color: var(--gray-color);
    cursor: pointer;
    font-size: 1.1rem;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--primary-color);
}

.admin-login-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.admin-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.admin-login-footer {
    margin-top: 25px;
}

.security-notice {
    background: rgba(255, 152, 0, 0.1);
    border: 1px solid rgba(255, 152, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.security-notice i {
    color: var(--warning-color);
    font-size: 1.2rem;
}

.security-notice p {
    color: var(--warning-color);
    font-size: 0.9rem;
    margin: 0;
}

/* لوحة التحكم الرئيسية */
.dashboard-container {
    display: grid;
    grid-template-areas: 
        "header header"
        "sidebar main";
    grid-template-columns: var(--sidebar-width) 1fr;
    grid-template-rows: var(--header-height) 1fr;
    min-height: 100vh;
}

/* الهيدر */
.dashboard-header {
    grid-area: header;
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-section i {
    font-size: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-text h1 {
    font-size: 1.3rem;
    color: var(--dark-color);
    margin-bottom: 2px;
}

.logo-text span {
    font-size: 0.85rem;
    color: var(--gray-color);
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 20px;
}

.search-container {
    position: relative;
    width: 100%;
}

.search-container i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
    font-size: 1rem;
}

.search-container input {
    width: 100%;
    padding: 10px 15px 10px 45px;
    border: 1px solid var(--border-color);
    border-radius: 25px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
    transition: var(--transition);
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--gray-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.notification-badge {
    position: absolute;
    top: 2px;
    left: 2px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.admin-profile {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    border-radius: 25px;
    background: rgba(102, 126, 234, 0.1);
    cursor: pointer;
    transition: var(--transition);
}

.admin-profile:hover {
    background: rgba(102, 126, 234, 0.2);
}

.profile-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
}

.profile-info {
    display: flex;
    flex-direction: column;
}

.profile-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-color);
}

.profile-role {
    font-size: 0.75rem;
    color: var(--gray-color);
}

.logout-btn {
    background: rgba(244, 67, 54, 0.1) !important;
    color: var(--danger-color) !important;
}

.logout-btn:hover {
    background: rgba(244, 67, 54, 0.2) !important;
}

/* الشريط الجانبي */
.dashboard-sidebar {
    grid-area: sidebar;
    background: var(--light-color);
    border-left: 1px solid var(--border-color);
    overflow-y: auto;
    height: calc(100vh - var(--header-height));
    position: sticky;
    top: var(--header-height);
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
}

.sidebar-menu li {
    margin: 5px 0;
}

.menu-link {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 20px;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    border-right: 3px solid transparent;
    position: relative;
}

.menu-link:hover {
    background: rgba(102, 126, 234, 0.1);
    border-right-color: var(--primary-color);
}

.menu-link.active {
    background: rgba(102, 126, 234, 0.15);
    border-right-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
}

.menu-link i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.menu-badge {
    background: var(--danger-color);
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.75rem;
    font-weight: bold;
    margin-right: auto;
}

/* المحتوى الرئيسي */
.dashboard-main {
    grid-area: main;
    padding: 20px;
    overflow-y: auto;
    height: calc(100vh - var(--header-height));
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.section-header h2 {
    color: var(--dark-color);
    font-size: 1.8rem;
    font-weight: 700;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* الأزرار */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--gray-color);
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #45a049;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #f57c00;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #d32f2f;
}
