<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - لوحة التحكم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .test-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .test-header {
            margin-bottom: 30px;
        }

        .test-header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .test-header p {
            color: #95a5a6;
            font-size: 1rem;
        }

        .credentials-box {
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .credentials-box h3 {
            color: #667eea;
            margin-bottom: 15px;
            text-align: center;
        }

        .credential-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .credential-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .credential-value {
            font-family: monospace;
            background: rgba(0, 0, 0, 0.1);
            padding: 5px 10px;
            border-radius: 5px;
            color: #2c3e50;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .credential-value:hover {
            background: rgba(102, 126, 234, 0.2);
        }

        .form-group {
            margin: 15px 0;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .test-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 0;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-box {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }

        .result-box.success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4caf50;
        }

        .result-box.error {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .auto-fill-btn {
            background: rgba(255, 152, 0, 0.1);
            color: #ff9800;
            border: 2px solid rgba(255, 152, 0, 0.3);
            margin-bottom: 20px;
        }

        .auto-fill-btn:hover {
            background: rgba(255, 152, 0, 0.2);
        }

        .dashboard-link {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
            text-decoration: none;
            border-radius: 8px;
            border: 2px solid rgba(46, 204, 113, 0.3);
            transition: all 0.3s ease;
        }

        .dashboard-link:hover {
            background: rgba(46, 204, 113, 0.2);
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔧 اختبار تسجيل الدخول</h1>
            <p>لوحة التحكم الإدارية</p>
        </div>

        <div class="credentials-box">
            <h3>📋 بيانات تسجيل الدخول الصحيحة</h3>
            <div class="credential-item">
                <span class="credential-label">اسم المدير:</span>
                <span class="credential-value" onclick="copyToClipboard('developer')">developer</span>
            </div>
            <div class="credential-item">
                <span class="credential-label">كلمة المرور:</span>
                <span class="credential-value" onclick="copyToClipboard('dev123456')">dev123456</span>
            </div>
            <div class="credential-item">
                <span class="credential-label">المفتاح السري:</span>
                <span class="credential-value" onclick="copyToClipboard('FUTUREFUEL2024ADMIN')">FUTUREFUEL2024ADMIN</span>
            </div>
        </div>

        <button class="test-btn auto-fill-btn" onclick="autoFillCredentials()">
            📝 ملء البيانات تلقائياً
        </button>

        <form id="testLoginForm">
            <div class="form-group">
                <label for="testUsername">اسم المدير:</label>
                <input type="text" id="testUsername" placeholder="أدخل اسم المدير">
            </div>

            <div class="form-group">
                <label for="testPassword">كلمة المرور:</label>
                <input type="password" id="testPassword" placeholder="أدخل كلمة المرور">
            </div>

            <div class="form-group">
                <label for="testSecretKey">المفتاح السري:</label>
                <input type="text" id="testSecretKey" placeholder="أدخل المفتاح السري">
            </div>

            <button type="submit" class="test-btn" id="testLoginBtn">
                🔐 اختبار تسجيل الدخول
            </button>
        </form>

        <div id="resultBox" class="result-box">
            <p id="resultMessage"></p>
        </div>

        <a href="index.html" class="dashboard-link">
            🎛️ الانتقال للوحة التحكم الفعلية
        </a>
    </div>

    <script>
        // بيانات المدير الصحيحة
        const correctCredentials = {
            username: 'developer',
            password: 'dev123456',
            secretKey: 'FUTUREFUEL2024ADMIN'
        };

        // ملء البيانات تلقائياً
        function autoFillCredentials() {
            document.getElementById('testUsername').value = correctCredentials.username;
            document.getElementById('testPassword').value = correctCredentials.password;
            document.getElementById('testSecretKey').value = correctCredentials.secretKey;
            
            showResult('تم ملء البيانات تلقائياً! يمكنك الآن اختبار تسجيل الدخول.', 'success');
        }

        // نسخ النص للحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showResult(`تم نسخ "${text}" للحافظة!`, 'success');
            }).catch(() => {
                showResult('فشل في نسخ النص', 'error');
            });
        }

        // معالجة نموذج الاختبار
        document.getElementById('testLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('testUsername').value.trim();
            const password = document.getElementById('testPassword').value.trim();
            const secretKey = document.getElementById('testSecretKey').value.trim();
            
            // التحقق من البيانات
            if (!username || !password || !secretKey) {
                showResult('يرجى ملء جميع الحقول!', 'error');
                return;
            }
            
            // إظهار حالة التحميل
            const btn = document.getElementById('testLoginBtn');
            btn.disabled = true;
            btn.textContent = '🔄 جاري الاختبار...';
            
            // محاكاة تأخير الشبكة
            setTimeout(() => {
                // التحقق من صحة البيانات
                if (username === correctCredentials.username && 
                    password === correctCredentials.password && 
                    secretKey === correctCredentials.secretKey) {
                    
                    showResult('✅ نجح تسجيل الدخول! البيانات صحيحة.', 'success');
                    
                    // حفظ بيانات المصادقة
                    const authData = {
                        username: username,
                        role: 'admin',
                        loginTime: new Date().getTime()
                    };
                    localStorage.setItem('adminAuth', JSON.stringify(authData));
                    
                    // إعادة توجيه بعد 2 ثانية
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 2000);
                    
                } else {
                    // تحديد الخطأ بدقة
                    let errorMsg = '❌ فشل تسجيل الدخول! ';
                    
                    if (username !== correctCredentials.username) {
                        errorMsg += 'اسم المدير غير صحيح. ';
                    }
                    if (password !== correctCredentials.password) {
                        errorMsg += 'كلمة المرور غير صحيحة. ';
                    }
                    if (secretKey !== correctCredentials.secretKey) {
                        errorMsg += 'المفتاح السري غير صحيح. ';
                    }
                    
                    showResult(errorMsg, 'error');
                }
                
                // إعادة تعيين الزر
                btn.disabled = false;
                btn.textContent = '🔐 اختبار تسجيل الدخول';
                
            }, 1500);
        });

        // إظهار النتيجة
        function showResult(message, type) {
            const resultBox = document.getElementById('resultBox');
            const resultMessage = document.getElementById('resultMessage');
            
            resultMessage.textContent = message;
            resultBox.className = `result-box ${type}`;
            resultBox.style.display = 'block';
            
            // إخفاء الرسالة بعد 5 ثوان للرسائل العادية
            if (type !== 'success' || !message.includes('نجح')) {
                setTimeout(() => {
                    resultBox.style.display = 'none';
                }, 5000);
            }
        }

        // فحص الجلسة المحفوظة
        window.addEventListener('load', function() {
            const savedAuth = localStorage.getItem('adminAuth');
            if (savedAuth) {
                const authData = JSON.parse(savedAuth);
                const now = new Date().getTime();
                
                // فحص انتهاء الجلسة (4 ساعات)
                if (now - authData.loginTime < 4 * 60 * 60 * 1000) {
                    showResult('🎉 لديك جلسة نشطة! سيتم توجيهك للوحة التحكم...', 'success');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 2000);
                } else {
                    localStorage.removeItem('adminAuth');
                    showResult('⏰ انتهت صلاحية الجلسة السابقة. يرجى تسجيل الدخول مرة أخرى.', 'error');
                }
            }
        });
    </script>
</body>
</html>
