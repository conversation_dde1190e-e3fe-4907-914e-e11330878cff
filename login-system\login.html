<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/login.css">
    <link rel="stylesheet" href="styles/modal.css">
</head>
<body>
    <!-- خلفية متحركة -->
    <div class="animated-background">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- الحاوي الرئيسي -->
    <div class="login-container">
        <!-- بطاقة تسجيل الدخول -->
        <div class="login-card" id="loginCard">
            <!-- الشعار والعنوان -->
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-gas-pump logo-icon"></i>
                    <h1 class="app-title">مؤسسة وقود المستقبل</h1>
                    <p class="app-subtitle">نظام إدارة شامل ومتطور</p>
                </div>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <div class="input-container">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" id="username" placeholder="اسم المستخدم" required>
                        <label for="username">اسم المستخدم</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="input-container">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="password" placeholder="كلمة المرور" required>
                        <label for="password">كلمة المرور</label>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password" onclick="showForgotPassword()">
                        نسيت كلمة المرور؟
                    </a>
                </div>

                <button type="submit" class="login-btn">
                    <span class="btn-text">تسجيل الدخول</span>
                    <i class="fas fa-arrow-left btn-icon"></i>
                </button>
            </form>

            <!-- خيارات إضافية -->
            <div class="login-footer">
                <div class="divider">
                    <span>أو</span>
                </div>
                
                <button class="activation-request-btn" onclick="showActivationRequest()">
                    <i class="fas fa-key"></i>
                    طلب تفعيل البرنامج
                </button>

                <div class="help-links">
                    <a href="#" onclick="showHelp()">
                        <i class="fas fa-question-circle"></i>
                        المساعدة
                    </a>
                    <a href="#" onclick="showAbout()">
                        <i class="fas fa-info-circle"></i>
                        حول البرنامج
                    </a>
                </div>
            </div>
        </div>

        <!-- بطاقة طلب التفعيل -->
        <div class="activation-card hidden" id="activationCard">
            <div class="card-header">
                <button class="back-btn" onclick="showLoginCard()">
                    <i class="fas fa-arrow-right"></i>
                </button>
                <h2>طلب تفعيل البرنامج</h2>
                <p>املأ البيانات التالية لطلب تفعيل البرنامج</p>
            </div>

            <form class="activation-form" id="activationForm">
                <!-- معلومات شخصية -->
                <div class="form-section">
                    <h3><i class="fas fa-user"></i> المعلومات الشخصية</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <input type="text" id="fullName" placeholder="الاسم الكامل" required>
                            <label for="fullName">الاسم الكامل</label>
                        </div>
                        <div class="form-group">
                            <input type="email" id="email" placeholder="البريد الإلكتروني" required>
                            <label for="email">البريد الإلكتروني</label>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <input type="tel" id="phone" placeholder="رقم الهاتف" required>
                            <label for="phone">رقم الهاتف</label>
                        </div>
                        <div class="form-group">
                            <input type="text" id="company" placeholder="اسم الشركة/المؤسسة">
                            <label for="company">اسم الشركة/المؤسسة</label>
                        </div>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="form-section">
                    <h3><i class="fas fa-desktop"></i> معلومات النظام</h3>
                    
                    <div class="system-info" id="systemInfo">
                        <div class="info-item">
                            <span class="info-label">معرف الجهاز:</span>
                            <span class="info-value" id="deviceId">جاري التحميل...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">نظام التشغيل:</span>
                            <span class="info-value" id="osInfo">جاري التحميل...</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">إصدار البرنامج:</span>
                            <span class="info-value">2.2.0</span>
                        </div>
                    </div>
                </div>

                <!-- نوع الترخيص -->
                <div class="form-section">
                    <h3><i class="fas fa-certificate"></i> نوع الترخيص المطلوب</h3>
                    
                    <div class="license-options">
                        <label class="license-option">
                            <input type="radio" name="licenseType" value="trial" checked>
                            <div class="option-content">
                                <div class="option-header">
                                    <i class="fas fa-clock"></i>
                                    <span class="option-title">تجريبي (30 يوم)</span>
                                    <span class="option-price">مجاني</span>
                                </div>
                                <p class="option-description">جميع المميزات مع قيود محدودة</p>
                            </div>
                        </label>

                        <label class="license-option">
                            <input type="radio" name="licenseType" value="basic">
                            <div class="option-content">
                                <div class="option-header">
                                    <i class="fas fa-star"></i>
                                    <span class="option-title">أساسي (شهري)</span>
                                    <span class="option-price">$50/شهر</span>
                                </div>
                                <p class="option-description">مميزات أساسية مع دعم محدود</p>
                            </div>
                        </label>

                        <label class="license-option">
                            <input type="radio" name="licenseType" value="professional">
                            <div class="option-content">
                                <div class="option-header">
                                    <i class="fas fa-crown"></i>
                                    <span class="option-title">احترافي (سنوي)</span>
                                    <span class="option-price">$500/سنة</span>
                                </div>
                                <p class="option-description">جميع المميزات + دعم فني</p>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- ملاحظات إضافية -->
                <div class="form-section">
                    <h3><i class="fas fa-comment"></i> ملاحظات إضافية</h3>
                    <textarea id="notes" placeholder="أي ملاحظات أو متطلبات خاصة..." rows="4"></textarea>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="showLoginCard()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        إرسال طلب التفعيل
                    </button>
                </div>
            </form>
        </div>

        <!-- بطاقة حالة الطلب -->
        <div class="status-card hidden" id="statusCard">
            <div class="status-content">
                <div class="status-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h2>تم إرسال طلب التفعيل بنجاح!</h2>
                <p>سيتم مراجعة طلبك والرد عليك خلال 24 ساعة</p>
                
                <div class="request-details">
                    <div class="detail-item">
                        <span class="detail-label">رقم الطلب:</span>
                        <span class="detail-value" id="requestId">#REQ-2024-001</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الطلب:</span>
                        <span class="detail-value" id="requestDate">اليوم</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value status-pending">قيد المراجعة</span>
                    </div>
                </div>

                <div class="status-actions">
                    <button class="btn-primary" onclick="showLoginCard()">
                        <i class="fas fa-arrow-right"></i>
                        العودة لتسجيل الدخول
                    </button>
                    <button class="btn-secondary" onclick="trackRequest()">
                        <i class="fas fa-search"></i>
                        تتبع الطلب
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <!-- سكريبت JavaScript -->
    <script src="scripts/login.js"></script>
</body>
</html>
