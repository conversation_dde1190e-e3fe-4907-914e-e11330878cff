/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--light-color);
    border-radius: 12px;
    padding: 25px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: var(--transition);
    border-left: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.stat-card.primary {
    border-left-color: var(--primary-color);
}

.stat-card.success {
    border-left-color: var(--success-color);
}

.stat-card.warning {
    border-left-color: var(--warning-color);
}

.stat-card.danger {
    border-left-color: var(--danger-color);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-color), #5a6fd8);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #45a049);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #f57c00);
}

.stat-card.danger .stat-icon {
    background: linear-gradient(135deg, var(--danger-color), #d32f2f);
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.stat-content p {
    font-size: 1rem;
    color: var(--gray-color);
    margin-bottom: 8px;
}

.stat-change {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 3px 8px;
    border-radius: 12px;
}

.stat-change.positive {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.stat-change.negative {
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.stat-change.neutral {
    background: rgba(158, 158, 158, 0.1);
    color: var(--gray-color);
}

/* الرسوم البيانية */
.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background: var(--light-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.chart-header h3 {
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.chart-actions select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
}

.chart-container {
    position: relative;
    height: 300px;
}

/* الطلبات الأخيرة */
.recent-requests {
    background: var(--light-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.view-all-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: var(--transition);
}

.view-all-link:hover {
    text-decoration: underline;
}

.requests-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.request-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: var(--bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.request-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.request-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.request-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
}

.request-details h4 {
    color: var(--dark-color);
    font-size: 1rem;
    margin-bottom: 3px;
}

.request-details p {
    color: var(--gray-color);
    font-size: 0.85rem;
}

.request-meta {
    display: flex;
    align-items: center;
    gap: 15px;
}

.request-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.request-status.pending {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.request-status.approved {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.request-status.rejected {
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.request-time {
    color: var(--gray-color);
    font-size: 0.8rem;
}

/* فلاتر البحث */
.filters-section {
    background: var(--light-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.filters-row {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 150px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--dark-color);
}

.filter-group select,
.filter-group input {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
    transition: var(--transition);
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* الجداول */
.table-container {
    background: var(--light-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.requests-table {
    width: 100%;
    border-collapse: collapse;
}

.requests-table th,
.requests-table td {
    padding: 15px 12px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.requests-table th {
    background: var(--bg-color);
    color: var(--dark-color);
    font-weight: 600;
    font-size: 0.9rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.requests-table tbody tr {
    transition: var(--transition);
}

.requests-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.requests-table td {
    font-size: 0.9rem;
    color: var(--text-color);
}

.table-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.table-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 4px;
}

.table-btn.view {
    background: rgba(33, 150, 243, 0.1);
    color: var(--info-color);
}

.table-btn.edit {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.table-btn.delete {
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.table-btn:hover {
    transform: scale(1.05);
}

/* الإجراءات الجماعية */
.bulk-actions {
    background: var(--light-color);
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: var(--shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 2px solid var(--primary-color);
}

.bulk-info {
    color: var(--dark-color);
    font-weight: 600;
}

.bulk-buttons {
    display: flex;
    gap: 10px;
}

/* النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    transition: var(--transition);
}

.modal-overlay.active {
    display: flex;
    opacity: 1;
}

.modal-content {
    background: var(--light-color);
    border-radius: 15px;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: var(--transition);
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

/* رسائل التنبيه */
.toast-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    background: var(--light-color);
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 300px;
    transform: translateX(-100%);
    transition: var(--transition);
    border-left: 4px solid var(--success-color);
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--danger-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast.info {
    border-left-color: var(--info-color);
}

.toast-icon {
    font-size: 1.2rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--danger-color);
}

.toast.warning .toast-icon {
    color: var(--warning-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-message {
    flex: 1;
    color: var(--text-color);
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-color);
    cursor: pointer;
    font-size: 1.1rem;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* التجاوب */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    :root {
        --sidebar-width: 0;
    }
    
    .dashboard-container {
        grid-template-areas: 
            "header"
            "main";
        grid-template-columns: 1fr;
    }
    
    .dashboard-sidebar {
        display: none;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .bulk-actions {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .requests-table {
        font-size: 0.8rem;
    }
    
    .requests-table th,
    .requests-table td {
        padding: 10px 8px;
    }
}
