// إدارة طلبات التفعيل
let selectedRequests = new Set();
let currentFilters = {
    status: 'all',
    licenseType: 'all',
    date: ''
};

// تحديث الفلاتر عند تغيير القيم
function applyFilters() {
    currentFilters.status = document.getElementById('statusFilter')?.value || 'all';
    currentFilters.licenseType = document.getElementById('licenseFilter')?.value || 'all';
    currentFilters.date = document.getElementById('dateFilter')?.value || '';

    loadActivationRequests();
}

// تحديث الطلبات
function refreshRequests() {
    loadActivationRequests();
    showToast('تم تحديث الطلبات', 'success');
}

// تحميل طلبات التفعيل
function loadActivationRequests() {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    
    // تطبيق الفلاتر
    const filteredRequests = applyRequestFilters(activationRequests);
    
    // عرض الطلبات في الجدول
    displayRequestsTable(filteredRequests);
    
    // تحديث عداد الطلبات المحددة
    updateSelectedCount();
}

// تطبيق فلاتر الطلبات
function applyRequestFilters(requests) {
    return requests.filter(request => {
        // فلتر الحالة
        if (currentFilters.status !== 'all' && request.status !== currentFilters.status) {
            return false;
        }
        
        // فلتر نوع الترخيص
        if (currentFilters.licenseType !== 'all' && request.data.licenseType !== currentFilters.licenseType) {
            return false;
        }
        
        // فلتر التاريخ
        if (currentFilters.date) {
            const requestDate = new Date(request.createdAt).toISOString().split('T')[0];
            if (requestDate !== currentFilters.date) {
                return false;
            }
        }
        
        return true;
    });
}

// عرض الطلبات في الجدول
function displayRequestsTable(requests) {
    const tbody = document.getElementById('requestsTableBody');
    if (!tbody) return;
    
    if (requests.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>لا توجد طلبات تطابق المعايير المحددة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = requests.map(request => `
        <tr data-request-id="${request.id}">
            <td>
                <input type="checkbox" class="request-checkbox" value="${request.id}" 
                       onchange="toggleRequestSelection('${request.id}')">
            </td>
            <td>
                <span class="request-id">${request.id}</span>
            </td>
            <td>
                <div class="user-info">
                    <strong>${request.data.fullName}</strong>
                    ${request.data.company ? `<br><small>${request.data.company}</small>` : ''}
                </div>
            </td>
            <td>
                <a href="mailto:${request.data.email}" class="email-link">
                    ${request.data.email}
                </a>
            </td>
            <td>
                <span class="license-badge ${request.data.licenseType}">
                    ${getLicenseTypeText(request.data.licenseType)}
                </span>
            </td>
            <td>
                <span class="request-date">
                    ${formatDate(request.createdAt)}
                </span>
            </td>
            <td>
                <span class="request-status ${request.status}">
                    ${getStatusText(request.status)}
                </span>
            </td>
            <td>
                <div class="table-actions">
                    <button class="table-btn view" onclick="viewRequestDetails('${request.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="table-btn edit" onclick="editRequestStatus('${request.id}')" title="تعديل الحالة">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="table-btn delete" onclick="deleteRequest('${request.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// تبديل تحديد الطلب
function toggleRequestSelection(requestId) {
    if (selectedRequests.has(requestId)) {
        selectedRequests.delete(requestId);
    } else {
        selectedRequests.add(requestId);
    }
    
    updateSelectedCount();
    updateBulkActionsVisibility();
}

// تحديد/إلغاء تحديد جميع الطلبات
function toggleSelectAllRequests() {
    const selectAllCheckbox = document.getElementById('selectAllRequests');
    const requestCheckboxes = document.querySelectorAll('.request-checkbox');
    
    if (selectAllCheckbox.checked) {
        requestCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
            selectedRequests.add(checkbox.value);
        });
    } else {
        requestCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            selectedRequests.delete(checkbox.value);
        });
    }
    
    updateSelectedCount();
    updateBulkActionsVisibility();
}

// تحديث عداد الطلبات المحددة
function updateSelectedCount() {
    const selectedCount = document.getElementById('selectedCount');
    if (selectedCount) {
        selectedCount.textContent = selectedRequests.size;
    }
}

// تحديث ظهور الإجراءات الجماعية
function updateBulkActionsVisibility() {
    const bulkActions = document.getElementById('bulkActions');
    if (bulkActions) {
        if (selectedRequests.size > 0) {
            bulkActions.style.display = 'flex';
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

// عرض تفاصيل الطلب
function viewRequestDetails(requestId) {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    const request = activationRequests.find(req => req.id === requestId);
    
    if (!request) {
        showToast('الطلب غير موجود', 'error');
        return;
    }
    
    const modalContent = `
        <div class="modal-header">
            <h3>تفاصيل طلب التفعيل</h3>
            <button class="close-btn" onclick="closeModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="request-details-grid">
                <div class="detail-section">
                    <h4><i class="fas fa-info-circle"></i> معلومات الطلب</h4>
                    <div class="detail-item">
                        <span class="detail-label">رقم الطلب:</span>
                        <span class="detail-value">${request.id}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">تاريخ الطلب:</span>
                        <span class="detail-value">${formatDateTime(request.createdAt)}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">الحالة:</span>
                        <span class="detail-value">
                            <span class="request-status ${request.status}">${getStatusText(request.status)}</span>
                        </span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4><i class="fas fa-user"></i> المعلومات الشخصية</h4>
                    <div class="detail-item">
                        <span class="detail-label">الاسم الكامل:</span>
                        <span class="detail-value">${request.data.fullName}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">البريد الإلكتروني:</span>
                        <span class="detail-value">
                            <a href="mailto:${request.data.email}">${request.data.email}</a>
                        </span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">رقم الهاتف:</span>
                        <span class="detail-value">
                            <a href="tel:${request.data.phone}">${request.data.phone}</a>
                        </span>
                    </div>
                    ${request.data.company ? `
                        <div class="detail-item">
                            <span class="detail-label">الشركة:</span>
                            <span class="detail-value">${request.data.company}</span>
                        </div>
                    ` : ''}
                </div>
                
                <div class="detail-section">
                    <h4><i class="fas fa-certificate"></i> معلومات الترخيص</h4>
                    <div class="detail-item">
                        <span class="detail-label">نوع الترخيص:</span>
                        <span class="detail-value">
                            <span class="license-badge ${request.data.licenseType}">
                                ${getLicenseTypeText(request.data.licenseType)}
                            </span>
                        </span>
                    </div>
                </div>
                
                <div class="detail-section">
                    <h4><i class="fas fa-desktop"></i> معلومات النظام</h4>
                    <div class="detail-item">
                        <span class="detail-label">معرف الجهاز:</span>
                        <span class="detail-value system-info">${request.data.systemInfo.deviceId}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">نظام التشغيل:</span>
                        <span class="detail-value">${request.data.systemInfo.os}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">المتصفح:</span>
                        <span class="detail-value">${request.data.systemInfo.browser}</span>
                    </div>
                </div>
                
                ${request.data.notes ? `
                    <div class="detail-section">
                        <h4><i class="fas fa-comment"></i> ملاحظات إضافية</h4>
                        <div class="notes-content">
                            ${request.data.notes}
                        </div>
                    </div>
                ` : ''}
            </div>
            
            <div class="modal-actions">
                <button class="btn btn-success" onclick="approveRequest('${request.id}')">
                    <i class="fas fa-check"></i> قبول الطلب
                </button>
                <button class="btn btn-danger" onclick="rejectRequest('${request.id}')">
                    <i class="fas fa-times"></i> رفض الطلب
                </button>
                <button class="btn btn-warning" onclick="processRequest('${request.id}')">
                    <i class="fas fa-cog"></i> معالجة الطلب
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    `;
    
    showModal(modalContent);
}

// تعديل حالة الطلب
function editRequestStatus(requestId) {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    const request = activationRequests.find(req => req.id === requestId);
    
    if (!request) {
        showToast('الطلب غير موجود', 'error');
        return;
    }
    
    const modalContent = `
        <div class="modal-header">
            <h3>تعديل حالة الطلب</h3>
            <button class="close-btn" onclick="closeModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="editStatusForm">
                <div class="form-group">
                    <label>رقم الطلب:</label>
                    <input type="text" value="${request.id}" readonly>
                </div>
                
                <div class="form-group">
                    <label>اسم المتقدم:</label>
                    <input type="text" value="${request.data.fullName}" readonly>
                </div>
                
                <div class="form-group">
                    <label>الحالة الحالية:</label>
                    <span class="request-status ${request.status}">${getStatusText(request.status)}</span>
                </div>
                
                <div class="form-group">
                    <label for="newStatus">الحالة الجديدة:</label>
                    <select id="newStatus" required>
                        <option value="pending" ${request.status === 'pending' ? 'selected' : ''}>معلق</option>
                        <option value="processing" ${request.status === 'processing' ? 'selected' : ''}>قيد المعالجة</option>
                        <option value="approved" ${request.status === 'approved' ? 'selected' : ''}>مقبول</option>
                        <option value="rejected" ${request.status === 'rejected' ? 'selected' : ''}>مرفوض</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="statusNote">ملاحظة (اختياري):</label>
                    <textarea id="statusNote" placeholder="أضف ملاحظة حول تغيير الحالة..." rows="3"></textarea>
                </div>
                
                <div class="modal-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;
    
    showModal(modalContent);
    
    // معالجة نموذج تعديل الحالة
    document.getElementById('editStatusForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const newStatus = document.getElementById('newStatus').value;
        const statusNote = document.getElementById('statusNote').value;
        
        updateRequestStatus(requestId, newStatus, statusNote);
    });
}

// تحديث حالة الطلب
function updateRequestStatus(requestId, newStatus, note = '') {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    const requestIndex = activationRequests.findIndex(req => req.id === requestId);
    
    if (requestIndex === -1) {
        showToast('الطلب غير موجود', 'error');
        return;
    }
    
    // تحديث الحالة
    activationRequests[requestIndex].status = newStatus;
    activationRequests[requestIndex].lastUpdated = new Date().toISOString();
    
    if (note) {
        if (!activationRequests[requestIndex].notes) {
            activationRequests[requestIndex].notes = [];
        }
        activationRequests[requestIndex].notes.push({
            text: note,
            timestamp: new Date().toISOString(),
            author: adminData.username
        });
    }
    
    // حفظ التغييرات
    localStorage.setItem('activationRequests', JSON.stringify(activationRequests));
    
    // تحديث الواجهة
    loadActivationRequests();
    updateDashboardStats();
    closeModal();
    
    showToast(`تم تحديث حالة الطلب إلى "${getStatusText(newStatus)}"`, 'success');
    
    // إرسال إشعار للمستخدم (محاكاة)
    sendStatusUpdateNotification(requestId, newStatus);
}

// قبول الطلب
function approveRequest(requestId) {
    if (confirm('هل أنت متأكد من قبول هذا الطلب؟')) {
        updateRequestStatus(requestId, 'approved', 'تم قبول الطلب وإرسال الترخيص');
        generateLicense(requestId);
    }
}

// رفض الطلب
function rejectRequest(requestId) {
    const reason = prompt('يرجى إدخال سبب الرفض:');
    if (reason) {
        updateRequestStatus(requestId, 'rejected', `تم رفض الطلب: ${reason}`);
    }
}

// معالجة الطلب
function processRequest(requestId) {
    updateRequestStatus(requestId, 'processing', 'الطلب قيد المعالجة');
}

// حذف الطلب
function deleteRequest(requestId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.')) {
        const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
        const filteredRequests = activationRequests.filter(req => req.id !== requestId);
        
        localStorage.setItem('activationRequests', JSON.stringify(filteredRequests));
        
        loadActivationRequests();
        updateDashboardStats();
        
        showToast('تم حذف الطلب بنجاح', 'success');
    }
}

// الإجراءات الجماعية
function bulkApprove() {
    if (selectedRequests.size === 0) {
        showToast('يرجى تحديد طلبات للمعالجة', 'warning');
        return;
    }
    
    if (confirm(`هل أنت متأكد من قبول ${selectedRequests.size} طلب؟`)) {
        selectedRequests.forEach(requestId => {
            updateRequestStatus(requestId, 'approved', 'تم قبول الطلب بشكل جماعي');
            generateLicense(requestId);
        });
        
        selectedRequests.clear();
        updateBulkActionsVisibility();
        showToast('تم قبول الطلبات المحددة', 'success');
    }
}

function bulkReject() {
    if (selectedRequests.size === 0) {
        showToast('يرجى تحديد طلبات للمعالجة', 'warning');
        return;
    }
    
    const reason = prompt('يرجى إدخال سبب الرفض:');
    if (reason) {
        selectedRequests.forEach(requestId => {
            updateRequestStatus(requestId, 'rejected', `تم رفض الطلب بشكل جماعي: ${reason}`);
        });
        
        selectedRequests.clear();
        updateBulkActionsVisibility();
        showToast('تم رفض الطلبات المحددة', 'success');
    }
}

function bulkProcess() {
    if (selectedRequests.size === 0) {
        showToast('يرجى تحديد طلبات للمعالجة', 'warning');
        return;
    }
    
    selectedRequests.forEach(requestId => {
        updateRequestStatus(requestId, 'processing', 'الطلب قيد المعالجة الجماعية');
    });
    
    selectedRequests.clear();
    updateBulkActionsVisibility();
    showToast('تم تحديث الطلبات المحددة إلى "قيد المعالجة"', 'success');
}

// تصدير البيانات
function exportRequests() {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    
    if (activationRequests.length === 0) {
        showToast('لا توجد بيانات للتصدير', 'warning');
        return;
    }
    
    // تحويل البيانات إلى CSV
    const csvData = convertToCSV(activationRequests);
    
    // تحميل الملف
    downloadCSV(csvData, `activation-requests-${new Date().toISOString().split('T')[0]}.csv`);
    
    showToast('تم تصدير البيانات بنجاح', 'success');
}

// وظائف مساعدة
function getLicenseTypeText(type) {
    const typeMap = {
        'trial': 'تجريبي',
        'basic': 'أساسي',
        'professional': 'احترافي',
        'enterprise': 'مؤسسي'
    };
    return typeMap[type] || type;
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('ar-DZ');
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('ar-DZ');
}

function showModal(content) {
    const modalOverlay = document.getElementById('modalOverlay');
    const modalContent = document.getElementById('modalContent');
    
    modalContent.innerHTML = content;
    modalOverlay.classList.add('active');
}

function closeModal() {
    const modalOverlay = document.getElementById('modalOverlay');
    modalOverlay.classList.remove('active');
}

// إنشاء ترخيص (محاكاة)
function generateLicense(requestId) {
    // محاكاة إنشاء ترخيص
    console.log(`تم إنشاء ترخيص للطلب: ${requestId}`);
}

// إرسال إشعار تحديث الحالة (محاكاة)
function sendStatusUpdateNotification(requestId, newStatus) {
    // محاكاة إرسال إشعار
    console.log(`تم إرسال إشعار للطلب ${requestId} بالحالة الجديدة: ${newStatus}`);
}

// تحويل البيانات إلى CSV
function convertToCSV(data) {
    const headers = ['رقم الطلب', 'الاسم', 'البريد الإلكتروني', 'الهاتف', 'الشركة', 'نوع الترخيص', 'الحالة', 'تاريخ الطلب'];
    
    const rows = data.map(request => [
        request.id,
        request.data.fullName,
        request.data.email,
        request.data.phone,
        request.data.company || '',
        getLicenseTypeText(request.data.licenseType),
        getStatusText(request.status),
        formatDateTime(request.createdAt)
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// تحميل ملف CSV
function downloadCSV(csvData, filename) {
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}
