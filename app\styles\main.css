/* إعادة تعيين الأساسيات */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* المتغيرات */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --bg-color: #ffffff;
    --text-color: #2c3e50;
    --border-color: #bdc3c7;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* الوضع المظلم */
body.dark-mode {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --border-color: #444444;
    --light-color: #2c2c2c;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* الأساسيات */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    direction: rtl;
    transition: all 0.3s ease;
}

/* الهيدر */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem 2rem;
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: bold;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge.hidden {
    display: none;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 80px;
    right: 0;
    width: 250px;
    height: calc(100vh - 80px);
    background: var(--light-color);
    border-left: 1px solid var(--border-color);
    overflow-y: auto;
    z-index: 999;
}

.nav-menu {
    list-style: none;
    padding: 1rem 0;
}

.nav-menu li {
    margin: 0.5rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(52, 152, 219, 0.1);
    border-right-color: var(--primary-color);
}

.nav-link.active {
    background: rgba(52, 152, 219, 0.2);
    border-right-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: bold;
}

.nav-link i {
    font-size: 1.2rem;
    width: 20px;
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: 80px;
    margin-right: 250px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 2rem;
}

/* الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--light-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    background: var(--primary-color);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-info h3 {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-info p {
    color: var(--text-color);
    opacity: 0.8;
}

/* رأس القسم */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* شريط البحث */
.search-bar {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    max-width: 500px;
}

.search-bar input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    background: var(--bg-color);
    color: var(--text-color);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* قسم الفلاتر */
.filters-section {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.filter-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    margin-top: 1rem;
}

.filter-options select,
.filter-options input[type="date"] {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background: var(--bg-color);
    color: var(--text-color);
    min-width: 150px;
}

.filter-options select:focus,
.filter-options input[type="date"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* إحصائيات الأقسام */
.suppliers-stats,
.sales-stats,
.purchases-stats,
.debts-stats,
.customers-stats,
.cards-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.bulk-actions {
    background: var(--light-color);
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    border: 1px solid var(--border-color);
}

.bulk-actions::before {
    content: "الإجراءات الجماعية:";
    font-weight: bold;
    color: var(--text-color);
}

/* الأزرار */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background: #27ae60;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

/* الجداول */
.table-container {
    background: var(--light-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--primary-color);
    color: white;
    font-weight: bold;
}

tr:hover {
    background: rgba(52, 152, 219, 0.05);
}

/* النوافذ المنبثقة */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: var(--bg-color);
    padding: 2rem;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow);
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 1rem;
    background: var(--bg-color);
    color: var(--text-color);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* الإشعارات */
.toast {
    position: fixed;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--secondary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 5px;
    box-shadow: var(--shadow);
    z-index: 3000;
    opacity: 0;
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
}

.toast.error {
    background: var(--danger-color);
}

/* لوحة الإشعارات */
.notifications-panel {
    position: fixed;
    top: 80px;
    left: -350px;
    width: 350px;
    height: calc(100vh - 80px);
    background: var(--light-color);
    border-right: 1px solid var(--border-color);
    transition: left 0.3s ease;
    z-index: 1500;
    overflow-y: auto;
}

.notifications-panel.active {
    left: 0;
}

.notifications-header {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
}

.notifications-list {
    padding: 1rem;
}

/* بطاقات الشهادات */
.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.certificate-card {
    background: var(--light-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.3s ease;
}

.certificate-card:hover {
    transform: translateY(-5px);
}

.certificate-icon {
    background: var(--primary-color);
    color: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 1rem;
}

.certificate-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.certificate-card p {
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 1.5rem;
}

/* إحصائيات المخزون */
.inventory-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card.warning .stat-icon {
    background: var(--warning-color);
}

.stat-card.info .stat-icon {
    background: var(--primary-color);
}

.stat-card.success .stat-icon {
    background: var(--secondary-color);
}

.stat-card.danger .stat-icon {
    background: var(--danger-color);
}

.stat-card.primary .stat-icon {
    background: var(--primary-color);
}

/* شبكة التقارير */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.report-card {
    background: var(--light-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: transform 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
}

.report-icon {
    background: var(--secondary-color);
    color: white;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin: 0 auto 1rem;
}

.report-card h3 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.report-card p {
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 1.5rem;
}

/* شبكة الإعدادات */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.settings-card {
    background: var(--light-color);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.settings-card h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

/* عناصر الإعدادات */
.setting-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

/* مفتاح التبديل */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* تنسيق المخزون منخفض المستوى */
.low-stock {
    background-color: #ffebee !important;
    color: #c62828 !important;
    font-weight: bold;
}

.dark-mode .low-stock {
    background-color: #4a2c2a !important;
    color: #ef5350 !important;
}

/* حالات مختلفة */
.status-active {
    color: #27ae60;
    font-weight: bold;
}

.status-inactive,
.status-expired {
    color: #e74c3c;
    font-weight: bold;
}

.status-pending {
    color: #f39c12;
    font-weight: bold;
}

.status-cancelled {
    color: #e74c3c;
    font-weight: bold;
}

.status-paid {
    color: #27ae60;
    font-weight: bold;
}

.status-overdue {
    color: #e74c3c;
    font-weight: bold;
}

.status-due-soon {
    color: #f39c12;
    font-weight: bold;
}

.status-default {
    color: #95a5a6;
    font-weight: bold;
}

/* أنواع الديون */
.debt-type {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: bold;
}

.debt-for-us {
    background-color: #d4edda;
    color: #155724;
}

.debt-on-us {
    background-color: #f8d7da;
    color: #721c24;
}

/* الأيام المتبقية */
.overdue {
    color: #e74c3c;
    font-weight: bold;
}

.due-soon {
    color: #f39c12;
    font-weight: bold;
}

/* تفاصيل الموردين */
.supplier-details,
.details-grid {
    margin: 1rem 0;
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.detail-item {
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.detail-item strong {
    color: var(--primary-color);
    display: block;
    margin-bottom: 0.25rem;
}

/* النموذج الشامل */
.comprehensive-form-container {
    max-height: 80vh;
    overflow-y: auto;
    padding: 1rem;
}

.comprehensive-form {
    max-width: 900px;
    margin: 0 auto;
}

.form-header {
    background: linear-gradient(135deg, var(--primary-color), #2980b9);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin: 1.5rem 0 1rem 0;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-header:first-child {
    margin-top: 0;
}

.form-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.form-header i {
    margin-left: 0.5rem;
}

.form-section {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
    background: var(--bg-color);
    color: var(--text-color);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group input[required] + label::after,
.form-group select[required] + label::after {
    content: " *";
    color: #e74c3c;
}

/* خيارات الخدمة */
.service-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.service-option {
    position: relative;
}

.service-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.service-label {
    display: block;
    padding: 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-color);
    text-align: center;
}

.service-label:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.service-option input[type="radio"]:checked + .service-label {
    border-color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

.service-label i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
    color: var(--primary-color);
}

.service-label span {
    display: block;
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.service-label small {
    display: block;
    color: var(--text-color);
    opacity: 0.7;
    font-size: 0.85rem;
}

/* أزرار النموذج */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem 0 1rem 0;
    border-top: 1px solid var(--border-color);
    margin-top: 2rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* ملخص البيانات */
.data-summary {
    max-width: 600px;
    margin: 0 auto;
}

.data-summary h3 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--secondary-color);
}

.summary-section {
    background: var(--light-color);
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
}

.summary-section h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 1rem;
}

.summary-section ul {
    margin: 0;
    padding-right: 1rem;
}

.summary-section li {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.summary-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.text-success {
    color: var(--secondary-color) !important;
}

/* تنسيقات الشاشات الصغيرة */
@media (max-width: 768px) {
    .comprehensive-form-container {
        padding: 0.5rem;
        max-height: 90vh;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .service-options {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-large {
        width: 100%;
        max-width: 300px;
    }
}

/* تفاصيل الزبون */
.customer-details-view {
    max-width: 800px;
    margin: 0 auto;
}

.customer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, var(--primary-color), #2980b9);
    color: white;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.customer-header h3 {
    margin: 0;
    font-size: 1.4rem;
}

.customer-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

/* التبويبات */
.details-tabs {
    margin-bottom: 2rem;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 1rem;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    color: var(--text-color);
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
}

.tab-btn:hover {
    background: var(--light-color);
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--light-color);
}

.tab-content {
    display: none;
    padding: 1rem;
    background: var(--light-color);
    border-radius: 8px;
    min-height: 300px;
}

.tab-content.active {
    display: block;
}

/* شبكة المعلومات */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: 6px;
    border-left: 4px solid var(--primary-color);
}

.info-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    color: var(--primary-color);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.info-item span {
    color: var(--text-color);
    font-size: 1rem;
}

/* قوائم السيارات والخدمات */
.vehicles-list,
.services-list,
.cards-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.vehicle-card,
.service-card,
.card-item {
    background: var(--bg-color);
    border-radius: 8px;
    padding: 1rem;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.vehicle-header,
.service-header,
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.vehicle-header h4,
.service-header h4,
.card-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.vehicle-number,
.service-status,
.card-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.vehicle-number {
    background: var(--primary-color);
    color: white;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.service-notes {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: var(--light-color);
    border-radius: 4px;
    font-size: 0.9rem;
    font-style: italic;
}

.no-data {
    text-align: center;
    color: var(--text-color);
    opacity: 0.7;
    font-style: italic;
    padding: 2rem;
}

/* إجراءات الزبون */
.customer-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 1.5rem 0;
    border-top: 1px solid var(--border-color);
    margin-top: 2rem;
}

/* معلومات الزبون في الجدول */
.customer-info,
.phone-info {
    line-height: 1.4;
}

.customer-info strong {
    color: var(--text-color);
}

.customer-info small,
.phone-info small {
    color: var(--text-color);
    opacity: 0.7;
}

.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
    min-width: 20px;
}

.badge-info {
    background: var(--primary-color);
    color: white;
}

/* تنسيقات الشاشات الصغيرة للتفاصيل */
@media (max-width: 768px) {
    .customer-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .tab-buttons {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .detail-row {
        flex-direction: column;
        gap: 0.25rem;
    }

    .customer-actions {
        flex-direction: column;
        align-items: center;
    }

    .customer-actions .btn {
        width: 100%;
        max-width: 250px;
    }
}

/* التنبيهات الذكية */
.smart-alerts-container {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 350px;
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1000;
}

.smart-alert {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    animation: slideIn 0.3s ease-out;
}

.smart-alert.alert-warning {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.1) 0%, var(--bg-color) 10%);
}

.smart-alert.alert-danger {
    border-left: 4px solid #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, var(--bg-color) 10%);
}

.smart-alert.alert-info {
    border-left: 4px solid #3b82f6;
    background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, var(--bg-color) 10%);
}

.smart-alert.alert-success {
    border-left: 4px solid #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.1) 0%, var(--bg-color) 10%);
}

.alert-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.alert-warning .alert-icon {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
}

.alert-danger .alert-icon {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
}

.alert-info .alert-icon {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
}

.alert-success .alert-icon {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
}

.alert-content p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.alert-actions {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.alert-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    min-width: 60px;
}

.no-alerts {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* قوائم التنبيهات */
.expiring-cards-list,
.today-appointments-list,
.low-stock-list,
.overdue-debts-list {
    max-width: 800px;
    margin: 0 auto;
}

.cards-grid,
.appointments-grid,
.items-grid,
.debts-grid {
    display: grid;
    gap: 1rem;
    margin: 1rem 0;
}

.expiring-card-item,
.appointment-item,
.stock-item,
.debt-item {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.expiring-card-item.urgent {
    border-left: 4px solid #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, var(--bg-color) 10%);
}

.expiring-card-item.warning {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.1) 0%, var(--bg-color) 10%);
}

.stock-item.critical {
    border-left: 4px solid #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, var(--bg-color) 10%);
}

.stock-item.warning {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(90deg, rgba(245, 158, 11, 0.1) 0%, var(--bg-color) 10%);
}

.debt-item.debt-for-us {
    border-left: 4px solid #10b981;
    background: linear-gradient(90deg, rgba(16, 185, 129, 0.1) 0%, var(--bg-color) 10%);
}

.debt-item.debt-on-us {
    border-left: 4px solid #ef4444;
    background: linear-gradient(90deg, rgba(239, 68, 68, 0.1) 0%, var(--bg-color) 10%);
}

.card-info,
.appointment-info,
.item-info,
.debt-info {
    flex: 1;
}

.card-info h4,
.appointment-info h4,
.item-info h4,
.debt-info h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-info p,
.appointment-info p,
.item-info p,
.debt-info p {
    margin: 0.25rem 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.expiry-info,
.overdue-info {
    font-weight: 600;
    color: #ef4444;
}

.card-actions,
.appointment-actions,
.item-actions,
.debt-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.appointment-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.stock-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.current-stock,
.min-stock {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
}

.stock-bar {
    width: 100%;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.stock-fill {
    height: 100%;
    background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
    transition: width 0.3s ease;
}

.bulk-renewal-actions,
.bulk-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.debt-summary {
    margin-top: 2rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    text-align: center;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 600;
}

.summary-item .value {
    color: var(--primary-color);
}

/* الرسوم المتحركة */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* تنسيقات الشاشات الصغيرة */
@media (max-width: 768px) {
    .smart-alerts-container {
        right: 10px;
        left: 10px;
        width: auto;
    }

    .smart-alert {
        flex-direction: column;
        text-align: center;
    }

    .alert-actions {
        flex-direction: row;
        justify-content: center;
    }

    .cards-grid,
    .appointments-grid,
    .items-grid,
    .debts-grid {
        grid-template-columns: 1fr;
    }

    .expiring-card-item,
    .appointment-item,
    .stock-item,
    .debt-item {
        flex-direction: column;
        text-align: center;
    }

    .card-actions,
    .appointment-actions,
    .item-actions,
    .debt-actions {
        flex-direction: row;
        justify-content: center;
    }
}

/* ==================== أنماط الموظفين ونظام الحضور ==================== */

/* معلومات الموظف */
.employee-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.employee-info strong {
    font-size: 1rem;
    color: var(--text-color);
}

.employee-info small {
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.contact-info i {
    width: 16px;
    color: var(--text-secondary);
}

/* تفاصيل الموظف */
.employee-details {
    max-width: 800px;
    margin: 0 auto;
}

.employee-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
}

.employee-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
}

.employee-basic-info h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-size: 1.8rem;
}

.employee-basic-info .position {
    margin: 0 0 0.5rem 0;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.employee-basic-info .status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.info-item.full-width {
    grid-column: 1 / -1;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
}

.info-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.info-item span {
    color: var(--text-color);
    font-weight: 500;
}

.permissions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.permission-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
}

.stat-card {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid var(--primary-color);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(var(--primary-color-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.stat-info h4 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-color);
}

.stat-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.employee-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

/* نظام الحضور */
.attendance-form {
    max-width: 600px;
    margin: 0 auto;
}

.attendance-form .employee-info {
    text-align: center;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    margin-bottom: 2rem;
}

.attendance-form .employee-info h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.attendance-form .employee-info .date {
    color: var(--text-secondary);
    font-weight: 600;
}

.current-status {
    margin-bottom: 2rem;
}

.current-status h4 {
    margin-bottom: 1rem;
    color: var(--text-color);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.status-item {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}

.status-item label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.status-item .time {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.status-item .hours {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

.status-item .status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.attendance-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 2rem 0;
}

.attendance-actions .btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.manual-entry {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    margin-top: 2rem;
}

.manual-entry h4 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
}

/* سجل الحضور */
.attendance-history {
    max-width: 1000px;
    margin: 0 auto;
}

.history-header {
    text-align: center;
    margin-bottom: 2rem;
}

.history-header h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.attendance-summary {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-stats .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-color);
    border-radius: 8px;
}

.summary-stats .stat-item label {
    color: var(--text-secondary);
    font-weight: 600;
}

.summary-stats .stat-item span {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.1rem;
}

.attendance-table {
    background: var(--bg-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.attendance-table table {
    width: 100%;
    border-collapse: collapse;
}

.attendance-table th {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
}

.attendance-table td {
    padding: 0.75rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.attendance-table .time {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--primary-color);
}

.attendance-table .hours {
    font-weight: 600;
    color: var(--text-color);
}

.attendance-table .notes {
    font-style: italic;
    color: var(--text-secondary);
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.attendance-table .no-data {
    padding: 2rem;
    color: var(--text-secondary);
    font-style: italic;
}

.history-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* الصلاحيات */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin: 1rem 0;
}

.permissions-grid label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.permissions-grid label:hover {
    background: var(--border-color);
}

.permissions-grid input[type="checkbox"] {
    margin: 0;
}

/* حالات الموظفين */
.status-active {
    background: #10b981;
    color: white;
}

.status-pending {
    background: #f59e0b;
    color: white;
}

.status-warning {
    background: #f97316;
    color: white;
}

.status-cancelled {
    background: #ef4444;
    color: white;
}

.status-expired {
    background: #6b7280;
    color: white;
}

.status-default {
    background: var(--border-color);
    color: var(--text-color);
}

/* تنسيقات الشاشات الصغيرة */
@media (max-width: 768px) {
    .employee-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .employee-avatar {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }

    .employee-actions {
        flex-direction: column;
    }

    .attendance-actions {
        gap: 0.5rem;
    }

    .history-actions {
        flex-direction: column;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }
}

/* ==================== نظام الرواتب ==================== */

.payroll-system {
    max-width: 1200px;
    margin: 0 auto;
}

.payroll-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: 12px;
}

.payroll-header h3 {
    margin: 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.month-selector {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.month-selector label {
    font-weight: 600;
    color: var(--text-color);
}

.month-selector input[type="month"] {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-color);
}

.payroll-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.summary-card {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.summary-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(var(--primary-color-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.summary-card .card-info h4 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-color);
}

.summary-card .card-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.payroll-table-container {
    background: var(--bg-color);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.payroll-table {
    width: 100%;
    border-collapse: collapse;
}

.payroll-table th {
    background: var(--primary-color);
    color: white;
    padding: 1rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.payroll-table td {
    padding: 0.75rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
}

.payroll-table .employee-info {
    text-align: right;
}

.payroll-table .employee-info strong {
    color: var(--text-color);
    font-size: 0.95rem;
}

.payroll-table .allowances {
    color: #10b981;
    font-weight: 600;
}

.payroll-table .deductions {
    color: #ef4444;
    font-weight: 600;
}

.payroll-table .net-salary {
    background: rgba(var(--primary-color-rgb), 0.1);
    font-weight: bold;
    color: var(--primary-color);
}

.payroll-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* كشف الراتب */
.payslip {
    max-width: 600px;
    margin: 0 auto;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
}

.payslip-header {
    background: var(--primary-color);
    color: white;
    padding: 2rem;
    text-align: center;
}

.payslip-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
}

.payslip-header p {
    margin: 0;
    opacity: 0.9;
}

.payslip-body {
    padding: 2rem;
}

.payslip-section {
    margin-bottom: 2rem;
}

.payslip-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.payslip-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.payslip-row:last-child {
    border-bottom: none;
}

.payslip-row.total {
    background: rgba(var(--primary-color-rgb), 0.1);
    padding: 1rem;
    margin: 1rem -1rem 0 -1rem;
    font-weight: bold;
    font-size: 1.1rem;
}

.payslip-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.payslip-value {
    color: var(--text-color);
    font-weight: 600;
}

.payslip-value.positive {
    color: #10b981;
}

.payslip-value.negative {
    color: #ef4444;
}

.payslip-footer {
    background: var(--bg-secondary);
    padding: 1rem 2rem;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* تقرير الرواتب */
.payroll-report {
    max-width: 1000px;
    margin: 0 auto;
}

.report-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
}

.report-header h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
}

.report-header .report-period {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.report-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.report-summary .summary-item {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid var(--primary-color);
}

.report-summary .summary-item h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.report-summary .summary-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* تنسيقات الشاشات الصغيرة */
@media (max-width: 768px) {
    .payroll-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .month-selector {
        flex-direction: column;
        gap: 0.5rem;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .payroll-table-container {
        overflow-x: auto;
    }

    .payroll-table {
        min-width: 800px;
    }

    .payroll-actions {
        flex-direction: column;
    }

    .payslip-body {
        padding: 1rem;
    }

    .payslip-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .report-summary {
        grid-template-columns: 1fr;
    }
}

/* ==================== مركز التقارير المتقدم ==================== */

.reports-center {
    max-width: 1200px;
    margin: 0 auto;
}

.reports-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
}

.reports-header h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.reports-header p {
    margin: 0;
    color: var(--text-secondary);
}

.reports-categories {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.report-category {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.category-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.category-header h4 {
    margin: 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.report-card {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.report-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(var(--primary-color-rgb), 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.report-info h5 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    font-size: 1rem;
    font-weight: 600;
}

.report-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.reports-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* تقارير التحليل */
.analytics-report {
    max-width: 1000px;
    margin: 0 auto;
}

.report-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: 12px;
}

.report-header h2 {
    margin: 0 0 0.5rem 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.report-date {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.report-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.summary-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
}

.summary-card .card-icon.customers {
    background: #3b82f6;
}

.summary-card .card-icon.active {
    background: #10b981;
}

.summary-card .card-icon.new {
    background: #f59e0b;
}

.summary-card .card-icon.percentage {
    background: #8b5cf6;
}

.summary-card .card-content h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--text-color);
}

.summary-card .card-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.report-sections {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-bottom: 2rem;
}

.report-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
}

.report-section h3 {
    margin: 0 0 1rem 0;
    color: var(--text-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.chart-container {
    overflow-x: auto;
}

.report-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

/* نماذج الصيانة والمخزون */
.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: var(--bg-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.checkbox-group label:hover {
    background: var(--border-color);
}

.checkbox-group input[type="checkbox"] {
    margin: 0;
}

/* تنسيقات الشاشات الصغيرة */
@media (max-width: 768px) {
    .reports-grid {
        grid-template-columns: 1fr;
    }

    .report-card {
        flex-direction: column;
        text-align: center;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .summary-card {
        flex-direction: column;
        text-align: center;
    }

    .reports-actions {
        flex-direction: column;
    }

    .report-actions {
        flex-direction: column;
    }

    .checkbox-group {
        grid-template-columns: 1fr;
    }
}



/* تنسيقات جدول الإرسال */
.transmission-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    border-radius: 12px 12px 0 0;
    margin-bottom: 0;
}

.transmission-header h1 {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
    font-weight: bold;
}

.transmission-header h2 {
    margin: 0 0 1rem 0;
    font-size: 1.4rem;
    font-weight: 600;
    opacity: 0.9;
}

.transmission-header p {
    margin: 0.3rem 0;
    font-size: 1.1rem;
    opacity: 0.8;
}

.transmission-header .highlight {
    font-weight: bold;
    font-size: 1.2rem;
    margin: 1rem 0;
}

.transmission-controls {
    padding: 1.5rem;
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.controls-left {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.controls-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    display: flex;
    align-items: center;
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.search-box input {
    border: none;
    padding: 0.75rem 1rem;
    outline: none;
    width: 250px;
    font-size: 0.9rem;
    background: var(--bg-color);
    color: var(--text-color);
}

.search-box button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    cursor: pointer;
}

.transmission-filters {
    padding: 1rem 1.5rem;
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-color);
}

.filter-group select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--bg-color);
    color: var(--text-color);
    min-width: 120px;
}

.transmission-table-container {
    padding: 1.5rem;
    overflow-x: auto;
}

#transmission-main-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

#transmission-main-table th {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 1rem 0.75rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

#transmission-main-table td {
    padding: 0.875rem 0.75rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--text-color);
}

#transmission-main-table tbody tr:nth-child(even) {
    background-color: var(--light-color);
}

#transmission-main-table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
    transition: all 0.2s ease;
}

.transmission-summary {
    padding: 1.5rem;
    background: var(--light-color);
    border-top: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    border-radius: 0 0 12px 12px;
}

.transmission-summary .summary-card {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: var(--shadow);
}

.transmission-summary .summary-card h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    opacity: 0.9;
}

.transmission-summary .summary-card .count {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-color);
    opacity: 0.7;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-print {
    /* سيتم إخفاؤها عند الطباعة */
}

/* تنسيقات الطباعة لجدول الإرسال */
@media print {
    .transmission-controls,
    .transmission-filters,
    .transmission-summary,
    .no-print {
        display: none !important;
    }

    .transmission-header {
        background: white !important;
        color: black !important;
        border: 2px solid black;
    }

    #transmission-main-table th {
        background: #f0f0f0 !important;
        color: black !important;
    }

    #transmission-main-table td,
    #transmission-main-table th {
        border: 1px solid black !important;
        padding: 8px 4px !important;
        font-size: 12px !important;
    }
}

/* الاستجابة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .stats-grid,
    .certificates-grid,
    .reports-grid {
        grid-template-columns: 1fr;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-actions {
        flex-wrap: wrap;
    }

    .search-bar {
        max-width: 100%;
    }
}
