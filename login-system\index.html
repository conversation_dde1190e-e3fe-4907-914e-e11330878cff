<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مؤسسة وقود المستقبل - نظام الإدارة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            overflow: hidden;
        }

        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 50px;
            max-width: 800px;
            width: 90%;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            animation: rotate 10s linear infinite;
            z-index: -1;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .logo-section {
            margin-bottom: 40px;
        }

        .logo-icon {
            font-size: 5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .welcome-title {
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .welcome-subtitle {
            font-size: 1.3rem;
            color: #95a5a6;
            margin-bottom: 10px;
        }

        .version-info {
            font-size: 1rem;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 40px;
        }

        .welcome-description {
            font-size: 1.1rem;
            color: #2c3e50;
            line-height: 1.8;
            margin-bottom: 50px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .action-buttons {
            display: flex;
            gap: 25px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 40px;
        }

        .action-btn {
            padding: 18px 35px;
            border: none;
            border-radius: 15px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 12px;
            min-width: 200px;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .action-btn.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .action-btn.secondary {
            background: rgba(46, 204, 113, 0.1);
            color: #27ae60;
            border: 2px solid rgba(46, 204, 113, 0.3);
        }

        .action-btn.secondary:hover {
            background: rgba(46, 204, 113, 0.2);
            transform: translateY(-2px);
        }

        .action-btn.tertiary {
            background: rgba(241, 196, 15, 0.1);
            color: #f39c12;
            border: 2px solid rgba(241, 196, 15, 0.3);
        }

        .action-btn.tertiary:hover {
            background: rgba(241, 196, 15, 0.2);
            transform: translateY(-2px);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .feature-item {
            background: rgba(102, 126, 234, 0.05);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
            background: rgba(102, 126, 234, 0.1);
        }

        .feature-item i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .feature-item h4 {
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1rem;
        }

        .feature-item p {
            color: #95a5a6;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .footer-info {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            color: #95a5a6;
            font-size: 0.9rem;
        }

        .footer-info p {
            margin: 5px 0;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape-3 {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .welcome-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .welcome-title {
                font-size: 2.2rem;
            }

            .welcome-subtitle {
                font-size: 1.1rem;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .action-btn {
                width: 100%;
                max-width: 300px;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- الأشكال المتحركة في الخلفية -->
    <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
    </div>

    <div class="welcome-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-gas-pump"></i>
            </div>
            <h1 class="welcome-title">مؤسسة وقود المستقبل</h1>
            <p class="welcome-subtitle">نظام إدارة شامل ومتطور</p>
            <p class="version-info">الإصدار 2.2.0 - Enhanced Edition</p>
        </div>

        <div class="welcome-description">
            مرحباً بك في نظام إدارة مؤسسة وقود المستقبل. نظام شامل ومتطور لإدارة جميع 
            عمليات المؤسسة من الزبائن والمركبات إلى بطاقات الغاز والمخزون. 
            اختر من الخيارات أدناه للبدء.
        </div>

        <div class="action-buttons">
            <a href="login.html" class="action-btn primary">
                <i class="fas fa-sign-in-alt"></i>
                تسجيل الدخول
            </a>
            
            <a href="demo.html" class="action-btn secondary">
                <i class="fas fa-play"></i>
                العرض التجريبي
            </a>
            
            <a href="../app/main.html" class="action-btn tertiary">
                <i class="fas fa-desktop"></i>
                التطبيق الرئيسي
            </a>
        </div>

        <div class="features-grid">
            <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <h4>أمان متقدم</h4>
                <p>نظام مصادقة آمن مع تشفير البيانات</p>
            </div>

            <div class="feature-item">
                <i class="fas fa-users"></i>
                <h4>إدارة الزبائن</h4>
                <p>نظام شامل لإدارة الزبائن والمركبات</p>
            </div>

            <div class="feature-item">
                <i class="fas fa-credit-card"></i>
                <h4>بطاقات الغاز</h4>
                <p>إدارة وتتبع بطاقات الغاز والتجديد</p>
            </div>

            <div class="feature-item">
                <i class="fas fa-chart-bar"></i>
                <h4>التقارير</h4>
                <p>تقارير مفصلة وإحصائيات شاملة</p>
            </div>
        </div>

        <div class="footer-info">
            <p><strong>© 2024 Future Fuel Corporation</strong></p>
            <p>جميع الحقوق محفوظة</p>
            <p>للدعم الفني: <EMAIL> | +966 50 123 4567</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي
            const container = document.querySelector('.welcome-container');
            container.style.opacity = '0';
            container.style.transform = 'scale(0.9) translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.8s ease-out';
                container.style.opacity = '1';
                container.style.transform = 'scale(1) translateY(0)';
            }, 100);

            // تأثير تدريجي للعناصر
            const elements = document.querySelectorAll('.feature-item, .action-btn');
            elements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    element.style.transition = 'all 0.6s ease-out';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 300 + (index * 100));
            });

            // تحديث الوقت
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleTimeString('ar-SA');
                console.log('الوقت الحالي:', timeString);
            }

            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
