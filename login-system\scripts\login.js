// متغيرات عامة
let currentCard = 'login';
let systemInfo = {};

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadSystemInfo();
    setupEventListeners();
});

// تهيئة التطبيق
function initializeApp() {
    console.log('🚀 تم تحميل نظام تسجيل الدخول');
    
    // فحص البيانات المحفوظة
    checkSavedCredentials();
    
    // تحديث معلومات النظام
    updateSystemInfo();
    
    // إضافة تأثيرات بصرية
    addVisualEffects();
}

// تحميل معلومات النظام
function loadSystemInfo() {
    // معرف الجهاز (محاكاة)
    systemInfo.deviceId = generateDeviceId();
    
    // معلومات نظام التشغيل
    systemInfo.os = getOSInfo();
    
    // معلومات المتصفح
    systemInfo.browser = getBrowserInfo();
    
    // تحديث واجهة المستخدم
    updateSystemInfoDisplay();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // نموذج طلب التفعيل
    const activationForm = document.getElementById('activationForm');
    if (activationForm) {
        activationForm.addEventListener('submit', handleActivationRequest);
    }
    
    // تأثيرات الإدخال
    setupInputEffects();
    
    // اختصارات لوحة المفاتيح
    setupKeyboardShortcuts();
}

// معالجة تسجيل الدخول
async function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // إظهار حالة التحميل
    showLoadingState('loginForm');
    
    try {
        // محاكاة طلب تسجيل الدخول
        const result = await authenticateUser(username, password);
        
        if (result.success) {
            // حفظ البيانات إذا كان مطلوباً
            if (rememberMe) {
                saveCredentials(username);
            }
            
            // إظهار رسالة نجاح
            showToast('تم تسجيل الدخول بنجاح!', 'success');
            
            // الانتقال للتطبيق الرئيسي
            setTimeout(() => {
                redirectToMainApp();
            }, 1500);
            
        } else {
            throw new Error(result.message || 'فشل في تسجيل الدخول');
        }
        
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        showToast(error.message || 'حدث خطأ في تسجيل الدخول', 'error');
    } finally {
        hideLoadingState('loginForm');
    }
}

// معالجة طلب التفعيل
async function handleActivationRequest(event) {
    event.preventDefault();
    
    // جمع بيانات النموذج
    const formData = collectActivationData();
    
    // التحقق من صحة البيانات
    if (!validateActivationData(formData)) {
        return;
    }
    
    // إظهار حالة التحميل
    showLoadingState('activationForm');
    
    try {
        // إرسال طلب التفعيل
        const result = await submitActivationRequest(formData);
        
        if (result.success) {
            // إظهار بطاقة النجاح
            showStatusCard(result.requestId);
            
            // إظهار رسالة نجاح
            showToast('تم إرسال طلب التفعيل بنجاح!', 'success');
            
        } else {
            throw new Error(result.message || 'فشل في إرسال طلب التفعيل');
        }
        
    } catch (error) {
        console.error('خطأ في طلب التفعيل:', error);
        showToast(error.message || 'حدث خطأ في إرسال الطلب', 'error');
    } finally {
        hideLoadingState('activationForm');
    }
}

// جمع بيانات طلب التفعيل
function collectActivationData() {
    return {
        fullName: document.getElementById('fullName').value,
        email: document.getElementById('email').value,
        phone: document.getElementById('phone').value,
        company: document.getElementById('company').value,
        licenseType: document.querySelector('input[name="licenseType"]:checked').value,
        notes: document.getElementById('notes').value,
        systemInfo: systemInfo,
        timestamp: new Date().toISOString()
    };
}

// التحقق من صحة بيانات التفعيل
function validateActivationData(data) {
    const errors = [];
    
    if (!data.fullName.trim()) {
        errors.push('الاسم الكامل مطلوب');
    }
    
    if (!data.email.trim() || !isValidEmail(data.email)) {
        errors.push('البريد الإلكتروني غير صحيح');
    }
    
    if (!data.phone.trim()) {
        errors.push('رقم الهاتف مطلوب');
    }
    
    if (errors.length > 0) {
        showToast(errors.join('\n'), 'error');
        return false;
    }
    
    return true;
}

// إرسال طلب التفعيل (محاكاة)
async function submitActivationRequest(data) {
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // محاكاة نجاح الطلب
    const requestId = generateRequestId();
    
    // حفظ الطلب محلياً
    saveActivationRequest(data, requestId);
    
    return {
        success: true,
        requestId: requestId,
        message: 'تم إرسال طلب التفعيل بنجاح'
    };
}

// مصادقة المستخدم (محاكاة)
async function authenticateUser(username, password) {
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // فحص بيانات تجريبية
    if (username === 'admin' && password === 'admin123') {
        return {
            success: true,
            user: {
                id: 1,
                username: username,
                role: 'admin'
            }
        };
    }
    
    // فحص إذا كان المستخدم لديه ترخيص
    const license = checkUserLicense(username);
    if (license && license.isValid) {
        return {
            success: true,
            user: {
                id: license.userId,
                username: username,
                role: 'user',
                license: license
            }
        };
    }
    
    return {
        success: false,
        message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
    };
}

// فحص ترخيص المستخدم
function checkUserLicense(username) {
    const licenses = JSON.parse(localStorage.getItem('userLicenses') || '{}');
    return licenses[username] || null;
}

// إظهار/إخفاء البطاقات
function showLoginCard() {
    hideAllCards();
    document.getElementById('loginCard').classList.remove('hidden');
    currentCard = 'login';
}

function showActivationRequest() {
    hideAllCards();
    document.getElementById('activationCard').classList.remove('hidden');
    currentCard = 'activation';
}

function showStatusCard(requestId) {
    hideAllCards();
    const statusCard = document.getElementById('statusCard');
    statusCard.classList.remove('hidden');
    
    // تحديث معلومات الطلب
    document.getElementById('requestId').textContent = requestId;
    document.getElementById('requestDate').textContent = new Date().toLocaleDateString('ar-DZ');
    
    currentCard = 'status';
}

function hideAllCards() {
    document.getElementById('loginCard').classList.add('hidden');
    document.getElementById('activationCard').classList.add('hidden');
    document.getElementById('statusCard').classList.add('hidden');
}

// وظائف مساعدة
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.querySelector('.toggle-password i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleBtn.className = 'fas fa-eye';
    }
}

function generateDeviceId() {
    // إنشاء معرف فريد للجهاز
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);
    
    const fingerprint = canvas.toDataURL();
    return btoa(fingerprint).substring(0, 16);
}

function getOSInfo() {
    const userAgent = navigator.userAgent;
    let os = 'Unknown';
    
    if (userAgent.indexOf('Windows') !== -1) os = 'Windows';
    else if (userAgent.indexOf('Mac') !== -1) os = 'macOS';
    else if (userAgent.indexOf('Linux') !== -1) os = 'Linux';
    else if (userAgent.indexOf('Android') !== -1) os = 'Android';
    else if (userAgent.indexOf('iOS') !== -1) os = 'iOS';
    
    return os;
}

function getBrowserInfo() {
    const userAgent = navigator.userAgent;
    let browser = 'Unknown';
    
    if (userAgent.indexOf('Chrome') !== -1) browser = 'Chrome';
    else if (userAgent.indexOf('Firefox') !== -1) browser = 'Firefox';
    else if (userAgent.indexOf('Safari') !== -1) browser = 'Safari';
    else if (userAgent.indexOf('Edge') !== -1) browser = 'Edge';
    
    return browser;
}

function updateSystemInfoDisplay() {
    const deviceIdElement = document.getElementById('deviceId');
    const osInfoElement = document.getElementById('osInfo');
    
    if (deviceIdElement) {
        deviceIdElement.textContent = systemInfo.deviceId;
    }
    
    if (osInfoElement) {
        osInfoElement.textContent = `${systemInfo.os} - ${systemInfo.browser}`;
    }
}

function generateRequestId() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `REQ-${year}${month}${day}-${random}`;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function saveActivationRequest(data, requestId) {
    const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    requests.push({
        id: requestId,
        data: data,
        status: 'pending',
        createdAt: new Date().toISOString()
    });
    localStorage.setItem('activationRequests', JSON.stringify(requests));
}

function checkSavedCredentials() {
    const savedUsername = localStorage.getItem('savedUsername');
    if (savedUsername) {
        document.getElementById('username').value = savedUsername;
        document.getElementById('rememberMe').checked = true;
    }
}

function saveCredentials(username) {
    localStorage.setItem('savedUsername', username);
}

function redirectToMainApp() {
    // الانتقال للتطبيق الرئيسي
    window.location.href = '../app/main.html';
}

// إظهار حالة التحميل
function showLoadingState(formId) {
    const form = document.getElementById(formId);
    const submitBtn = form.querySelector('button[type="submit"]');

    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
    }
}

// إخفاء حالة التحميل
function hideLoadingState(formId) {
    const form = document.getElementById(formId);
    const submitBtn = form.querySelector('button[type="submit"]');

    if (submitBtn) {
        submitBtn.disabled = false;

        if (formId === 'loginForm') {
            submitBtn.innerHTML = '<span class="btn-text">تسجيل الدخول</span><i class="fas fa-arrow-left btn-icon"></i>';
        } else if (formId === 'activationForm') {
            submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> إرسال طلب التفعيل';
        }
    }
}

// إظهار رسالة التنبيه
function showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    const toastIcon = toast.querySelector('.toast-icon');
    const toastMessage = toast.querySelector('.toast-message');

    // تحديد الأيقونة حسب النوع
    if (type === 'success') {
        toastIcon.className = 'toast-icon fas fa-check-circle';
        toast.classList.remove('error');
    } else if (type === 'error') {
        toastIcon.className = 'toast-icon fas fa-exclamation-circle';
        toast.classList.add('error');
    }

    toastMessage.textContent = message;
    toast.classList.add('show');

    // إخفاء الرسالة بعد 4 ثوان
    setTimeout(() => {
        toast.classList.remove('show');
    }, 4000);
}

// إعداد تأثيرات الإدخال
function setupInputEffects() {
    const inputs = document.querySelectorAll('input, textarea');

    inputs.forEach(input => {
        // تأثير التركيز
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });

        // تأثير الكتابة
        input.addEventListener('input', function() {
            if (this.value.trim() !== '') {
                this.parentElement.classList.add('has-value');
            } else {
                this.parentElement.classList.remove('has-value');
            }
        });
    });
}

// إعداد اختصارات لوحة المفاتيح
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Enter للانتقال بين الحقول
        if (event.key === 'Enter' && !event.shiftKey) {
            const activeElement = document.activeElement;

            if (activeElement.tagName === 'INPUT' && activeElement.type !== 'submit') {
                event.preventDefault();
                const inputs = Array.from(document.querySelectorAll('input:not([type="hidden"]):not([disabled])'));
                const currentIndex = inputs.indexOf(activeElement);

                if (currentIndex < inputs.length - 1) {
                    inputs[currentIndex + 1].focus();
                }
            }
        }

        // Escape للعودة
        if (event.key === 'Escape') {
            if (currentCard === 'activation' || currentCard === 'status') {
                showLoginCard();
            }
        }

        // Ctrl+Enter لإرسال النموذج
        if (event.ctrlKey && event.key === 'Enter') {
            const activeForm = document.querySelector(`#${currentCard}Card form`);
            if (activeForm) {
                activeForm.dispatchEvent(new Event('submit'));
            }
        }
    });
}

// إضافة تأثيرات بصرية
function addVisualEffects() {
    // تأثير الظهور التدريجي للبطاقات
    const cards = document.querySelectorAll('.login-card, .activation-card, .status-card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease-out';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);
    });

    // تأثير حركة الأشكال في الخلفية
    animateBackgroundShapes();
}

// تحريك الأشكال في الخلفية
function animateBackgroundShapes() {
    const shapes = document.querySelectorAll('.shape');

    shapes.forEach((shape, index) => {
        // حركة عشوائية للأشكال
        setInterval(() => {
            const randomX = Math.random() * 100;
            const randomY = Math.random() * 100;

            shape.style.transform = `translate(${randomX}px, ${randomY}px) rotate(${Math.random() * 360}deg)`;
        }, 3000 + (index * 1000));
    });
}

// تحديث معلومات النظام
function updateSystemInfo() {
    // تحديث الوقت الحالي
    setInterval(() => {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-DZ');

        // يمكن إضافة عرض الوقت في مكان ما في الواجهة
        console.log('الوقت الحالي:', timeString);
    }, 1000);
}

// وظائف إضافية للواجهة
function showHelp() {
    const helpContent = `
        <div class="help-content">
            <h3>المساعدة</h3>
            <div class="help-section">
                <h4>تسجيل الدخول:</h4>
                <ul>
                    <li>استخدم اسم المستخدم وكلمة المرور المرسلة إليك</li>
                    <li>يمكنك اختيار "تذكرني" لحفظ بيانات الدخول</li>
                    <li>في حالة نسيان كلمة المرور، اضغط على "نسيت كلمة المرور"</li>
                </ul>
            </div>
            <div class="help-section">
                <h4>طلب التفعيل:</h4>
                <ul>
                    <li>املأ جميع البيانات المطلوبة بدقة</li>
                    <li>اختر نوع الترخيص المناسب لاحتياجاتك</li>
                    <li>سيتم الرد على طلبك خلال 24 ساعة</li>
                </ul>
            </div>
            <div class="help-section">
                <h4>الدعم الفني:</h4>
                <p>للحصول على المساعدة، تواصل معنا على:</p>
                <p>📧 <EMAIL></p>
                <p>📞 +966 50 123 4567</p>
            </div>
        </div>
    `;

    showModal('المساعدة', helpContent);
}

function showAbout() {
    const aboutContent = `
        <div class="about-content">
            <h3>حول البرنامج</h3>
            <div class="about-info">
                <p><strong>اسم البرنامج:</strong> نظام إدارة مؤسسة وقود المستقبل</p>
                <p><strong>الإصدار:</strong> 2.2.0</p>
                <p><strong>تاريخ الإصدار:</strong> ديسمبر 2024</p>
                <p><strong>المطور:</strong> Future Fuel Corporation</p>
            </div>
            <div class="features-list">
                <h4>المميزات الرئيسية:</h4>
                <ul>
                    <li>إدارة شاملة للزبائن والمركبات</li>
                    <li>نظام بطاقات الغاز المتطور</li>
                    <li>جدول الإرسال الذكي</li>
                    <li>إدارة المخزون والمبيعات</li>
                    <li>نظام التقارير والإحصائيات</li>
                    <li>النسخ الاحتياطي التلقائي</li>
                </ul>
            </div>
            <div class="copyright">
                <p>© 2024 Future Fuel Corporation. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    `;

    showModal('حول البرنامج', aboutContent);
}

function showForgotPassword() {
    const forgotContent = `
        <div class="forgot-password-content">
            <h3>استعادة كلمة المرور</h3>
            <p>أدخل بريدك الإلكتروني لإرسال رابط استعادة كلمة المرور:</p>
            <form id="forgotPasswordForm">
                <div class="form-group">
                    <input type="email" id="forgotEmail" placeholder="البريد الإلكتروني" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn-primary">إرسال</button>
                </div>
            </form>
        </div>
    `;

    showModal('استعادة كلمة المرور', forgotContent);

    // معالجة نموذج استعادة كلمة المرور
    document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.getElementById('forgotEmail').value;

        if (isValidEmail(email)) {
            showToast('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success');
            closeModal();
        } else {
            showToast('البريد الإلكتروني غير صحيح', 'error');
        }
    });
}

function showModal(title, content) {
    // إنشاء النافذة المنبثقة
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إظهار النافذة
    setTimeout(() => {
        modal.classList.add('active');
    }, 10);
}

function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.classList.remove('active');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

function trackRequest() {
    const trackingContent = `
        <div class="tracking-content">
            <h3>تتبع طلب التفعيل</h3>
            <div class="tracking-form">
                <div class="form-group">
                    <label>رقم الطلب:</label>
                    <input type="text" id="trackingId" placeholder="مثال: REQ-20241201-001">
                </div>
                <div class="form-group">
                    <label>البريد الإلكتروني:</label>
                    <input type="email" id="trackingEmail" placeholder="البريد الإلكتروني المستخدم في الطلب">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button type="button" class="btn-primary" onclick="performTracking()">تتبع</button>
                </div>
            </div>
        </div>
    `;

    showModal('تتبع الطلب', trackingContent);
}

function performTracking() {
    const trackingId = document.getElementById('trackingId').value;
    const trackingEmail = document.getElementById('trackingEmail').value;

    if (!trackingId || !trackingEmail) {
        showToast('يرجى ملء جميع الحقول', 'error');
        return;
    }

    // محاكاة البحث عن الطلب
    setTimeout(() => {
        const trackingResult = `
            <div class="tracking-result">
                <h4>نتيجة التتبع</h4>
                <div class="status-timeline">
                    <div class="timeline-item completed">
                        <i class="fas fa-check"></i>
                        <span>تم استلام الطلب</span>
                        <small>منذ يومين</small>
                    </div>
                    <div class="timeline-item completed">
                        <i class="fas fa-eye"></i>
                        <span>قيد المراجعة</span>
                        <small>منذ يوم واحد</small>
                    </div>
                    <div class="timeline-item active">
                        <i class="fas fa-cog"></i>
                        <span>جاري المعالجة</span>
                        <small>الآن</small>
                    </div>
                    <div class="timeline-item">
                        <i class="fas fa-paper-plane"></i>
                        <span>إرسال الترخيص</span>
                        <small>قريباً</small>
                    </div>
                </div>
                <p class="tracking-note">سيتم إرسال الترخيص إلى بريدك الإلكتروني خلال 24 ساعة.</p>
            </div>
        `;

        showModal('حالة الطلب', trackingResult);
    }, 1000);
}
