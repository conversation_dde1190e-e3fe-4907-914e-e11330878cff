<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles/dashboard.css">
    <link rel="stylesheet" href="styles/components.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول للمدير -->
    <div class="admin-login-overlay" id="adminLoginOverlay">
        <div class="admin-login-card">
            <div class="login-header">
                <div class="admin-logo">
                    <i class="fas fa-shield-alt"></i>
                    <h2>لوحة التحكم الإدارية</h2>
                    <p>مؤسسة وقود المستقبل</p>
                </div>
            </div>
            
            <form class="admin-login-form" id="adminLoginForm">
                <div class="form-group">
                    <div class="input-container">
                        <i class="fas fa-user-shield input-icon"></i>
                        <input type="text" id="adminUsername" placeholder="اسم المدير" required>
                        <label for="adminUsername">اسم المدير</label>
                    </div>
                </div>

                <div class="form-group">
                    <div class="input-container">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="adminPassword" placeholder="كلمة المرور" required>
                        <label for="adminPassword">كلمة المرور</label>
                        <button type="button" class="toggle-password" onclick="toggleAdminPassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <div class="input-container">
                        <i class="fas fa-key input-icon"></i>
                        <input type="text" id="adminSecretKey" placeholder="المفتاح السري" required>
                        <label for="adminSecretKey">المفتاح السري</label>
                    </div>
                </div>

                <button type="submit" class="admin-login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول لوحة التحكم
                </button>
            </form>

            <div class="admin-login-footer">
                <div class="security-notice">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>هذه منطقة محظورة للمطورين والمديرين فقط</p>
                </div>
            </div>
        </div>
    </div>

    <!-- لوحة التحكم الرئيسية -->
    <div class="dashboard-container hidden" id="dashboardContainer">
        <!-- الهيدر -->
        <header class="dashboard-header">
            <div class="header-left">
                <div class="logo-section">
                    <i class="fas fa-gas-pump"></i>
                    <div class="logo-text">
                        <h1>لوحة التحكم الإدارية</h1>
                        <span>مؤسسة وقود المستقبل</span>
                    </div>
                </div>
            </div>
            
            <div class="header-center">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="البحث في الطلبات..." id="globalSearch">
                </div>
            </div>

            <div class="header-right">
                <div class="header-actions">
                    <button class="header-btn" id="notificationsBtn" title="الإشعارات">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge">5</span>
                    </button>
                    
                    <button class="header-btn" id="settingsBtn" title="الإعدادات">
                        <i class="fas fa-cog"></i>
                    </button>
                    
                    <div class="admin-profile" id="adminProfile">
                        <img src="https://via.placeholder.com/40x40/667eea/ffffff?text=A" alt="Admin" class="profile-avatar">
                        <div class="profile-info">
                            <span class="profile-name">المطور الرئيسي</span>
                            <span class="profile-role">مدير النظام</span>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    
                    <button class="header-btn logout-btn" onclick="logout()" title="تسجيل الخروج">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- الشريط الجانبي -->
        <nav class="dashboard-sidebar">
            <ul class="sidebar-menu">
                <li>
                    <a href="#dashboard" class="menu-link active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>الرئيسية</span>
                    </a>
                </li>
                <li>
                    <a href="#activation-requests" class="menu-link" data-section="activation-requests">
                        <i class="fas fa-file-alt"></i>
                        <span>طلبات التفعيل</span>
                        <span class="menu-badge" id="requestsBadge">12</span>
                    </a>
                </li>
                <li>
                    <a href="#users-management" class="menu-link" data-section="users-management">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                </li>
                <li>
                    <a href="#licenses-management" class="menu-link" data-section="licenses-management">
                        <i class="fas fa-certificate"></i>
                        <span>إدارة التراخيص</span>
                    </a>
                </li>
                <li>
                    <a href="#analytics" class="menu-link" data-section="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>الإحصائيات</span>
                    </a>
                </li>
                <li>
                    <a href="#notifications" class="menu-link" data-section="notifications">
                        <i class="fas fa-bell"></i>
                        <span>الإشعارات</span>
                    </a>
                </li>
                <li>
                    <a href="#system-logs" class="menu-link" data-section="system-logs">
                        <i class="fas fa-list-alt"></i>
                        <span>سجل النظام</span>
                    </a>
                </li>
                <li>
                    <a href="#settings" class="menu-link" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="dashboard-main">
            <!-- قسم الرئيسية -->
            <section id="dashboard" class="dashboard-section active">
                <div class="section-header">
                    <h2>لوحة التحكم الرئيسية</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="refreshDashboard()">
                            <i class="fas fa-sync"></i>
                            تحديث البيانات
                        </button>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalRequests">0</h3>
                            <p>إجمالي الطلبات</p>
                            <span class="stat-change positive">+12% من الشهر الماضي</span>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="pendingRequests">0</h3>
                            <p>طلبات معلقة</p>
                            <span class="stat-change neutral">تحتاج مراجعة</span>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="approvedRequests">0</h3>
                            <p>طلبات مقبولة</p>
                            <span class="stat-change positive">+8% هذا الأسبوع</span>
                        </div>
                    </div>

                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="rejectedRequests">0</h3>
                            <p>طلبات مرفوضة</p>
                            <span class="stat-change negative">-3% من الأسبوع الماضي</span>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>طلبات التفعيل الشهرية</h3>
                            <div class="chart-actions">
                                <select id="chartPeriod">
                                    <option value="month">هذا الشهر</option>
                                    <option value="quarter">هذا الربع</option>
                                    <option value="year">هذا العام</option>
                                </select>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="requestsChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>توزيع أنواع التراخيص</h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="licensesChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- الطلبات الأخيرة -->
                <div class="recent-requests">
                    <div class="card-header">
                        <h3>الطلبات الأخيرة</h3>
                        <a href="#activation-requests" class="view-all-link">عرض الكل</a>
                    </div>
                    <div class="requests-list" id="recentRequestsList">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                </div>
            </section>

            <!-- قسم طلبات التفعيل -->
            <section id="activation-requests" class="dashboard-section">
                <div class="section-header">
                    <h2>إدارة طلبات التفعيل</h2>
                    <div class="header-actions">
                        <button class="btn btn-secondary" onclick="exportRequests()">
                            <i class="fas fa-download"></i>
                            تصدير البيانات
                        </button>
                        <button class="btn btn-primary" onclick="refreshRequests()">
                            <i class="fas fa-sync"></i>
                            تحديث
                        </button>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="filters-section">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label>حالة الطلب:</label>
                            <select id="statusFilter">
                                <option value="all">جميع الحالات</option>
                                <option value="pending">معلق</option>
                                <option value="approved">مقبول</option>
                                <option value="rejected">مرفوض</option>
                                <option value="processing">قيد المعالجة</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>نوع الترخيص:</label>
                            <select id="licenseFilter">
                                <option value="all">جميع الأنواع</option>
                                <option value="trial">تجريبي</option>
                                <option value="basic">أساسي</option>
                                <option value="professional">احترافي</option>
                                <option value="enterprise">مؤسسي</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>التاريخ:</label>
                            <input type="date" id="dateFilter">
                        </div>

                        <div class="filter-group">
                            <button class="btn btn-secondary" onclick="clearFilters()">
                                <i class="fas fa-times"></i>
                                مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>

                <!-- جدول الطلبات -->
                <div class="table-container">
                    <table class="requests-table" id="requestsTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAllRequests">
                                </th>
                                <th>رقم الطلب</th>
                                <th>اسم المتقدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>نوع الترخيص</th>
                                <th>تاريخ الطلب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="requestsTableBody">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- إجراءات جماعية -->
                <div class="bulk-actions" id="bulkActions" style="display: none;">
                    <span class="bulk-info">تم تحديد <span id="selectedCount">0</span> طلب</span>
                    <div class="bulk-buttons">
                        <button class="btn btn-success" onclick="bulkApprove()">
                            <i class="fas fa-check"></i>
                            قبول المحدد
                        </button>
                        <button class="btn btn-danger" onclick="bulkReject()">
                            <i class="fas fa-times"></i>
                            رفض المحدد
                        </button>
                        <button class="btn btn-warning" onclick="bulkProcess()">
                            <i class="fas fa-cog"></i>
                            معالجة المحدد
                        </button>
                    </div>
                </div>
            </section>

            <!-- الأقسام الأخرى ستتم إضافتها لاحقاً -->
        </main>
    </div>

    <!-- النوافذ المنبثقة -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-content" id="modalContent">
            <!-- محتوى النافذة المنبثقة -->
        </div>
    </div>

    <!-- رسائل التنبيه -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- تحميل الملفات -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="scripts/dashboard.js"></script>
    <script src="scripts/requests-manager.js"></script>
    <script src="scripts/charts.js"></script>
</body>
</html>
