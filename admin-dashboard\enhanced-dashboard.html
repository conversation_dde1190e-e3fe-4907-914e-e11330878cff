<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية المحسنة - مؤسسة وقود المستقبل الجزائر</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --dark-color: #2c3e50;
            --light-color: #ffffff;
            --sidebar-width: 280px;
            --header-height: 70px;
            --shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.08);
            --shadow-strong: 0 8px 40px rgba(0, 0, 0, 0.12);
            --border-radius: 15px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            direction: rtl;
            overflow-x: hidden;
        }

        /* شاشة تسجيل الدخول المحسنة */
        .admin-login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: var(--transition);
        }

        .admin-login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 50px;
            max-width: 450px;
            width: 90%;
            box-shadow: var(--shadow-strong);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .admin-login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .admin-logo {
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin: 0 auto 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 10px;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .login-subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .credentials-info {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: right;
        }

        .credentials-info h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .credentials-info p {
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            color: var(--dark-color);
            font-size: 0.95rem;
        }

        .form-group {
            position: relative;
            margin-bottom: 25px;
        }

        .form-input {
            width: 100%;
            padding: 18px 60px 18px 20px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: var(--transition);
            outline: none;
            font-weight: 500;
        }

        .form-input:focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .form-input:focus + .form-label,
        .form-input:not(:placeholder-shown) + .form-label {
            transform: translateY(-45px) scale(0.85);
            color: #667eea;
            font-weight: 600;
        }

        .form-label {
            position: absolute;
            right: 60px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 1rem;
            transition: var(--transition);
            pointer-events: none;
            background: rgba(255, 255, 255, 0.9);
            padding: 0 10px;
            font-weight: 500;
        }

        .input-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #7f8c8d;
            font-size: 1.3rem;
            transition: var(--transition);
        }

        .form-input:focus ~ .input-icon {
            color: #667eea;
            transform: translateY(-50%) scale(1.1);
        }

        .enhanced-btn {
            width: 100%;
            padding: 18px 30px;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow: var(--shadow-soft);
        }

        .enhanced-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s;
        }

        .enhanced-btn:hover::before {
            left: 100%;
        }

        .enhanced-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-strong);
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: var(--dark-color);
            margin-bottom: 20px;
        }

        .auto-fill-btn {
            width: 100%;
            padding: 12px;
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.4);
            border-radius: 10px;
            color: #856404;
            cursor: pointer;
            font-weight: 600;
            margin-bottom: 20px;
            transition: var(--transition);
        }

        .auto-fill-btn:hover {
            background: rgba(255, 193, 7, 0.3);
            transform: translateY(-2px);
        }

        /* لوحة التحكم الرئيسية */
        .dashboard-container {
            display: none;
            min-height: 100vh;
            background: #f8fafc;
        }

        .dashboard-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: white;
            box-shadow: var(--shadow-soft);
            z-index: 1000;
            transition: var(--transition);
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 25px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.8rem;
            margin: 0 auto 15px;
        }

        .sidebar-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .sidebar-subtitle {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-section {
            margin-bottom: 30px;
        }

        .nav-section-title {
            padding: 0 25px 10px;
            font-size: 0.8rem;
            font-weight: 600;
            color: #95a5a6;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 15px 25px;
            color: #7f8c8d;
            text-decoration: none;
            transition: var(--transition);
            position: relative;
            font-weight: 500;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            right: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-gradient);
            transform: scaleY(0);
            transition: var(--transition);
        }

        .nav-item:hover,
        .nav-item.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .nav-item:hover::before,
        .nav-item.active::before {
            transform: scaleY(1);
        }

        .nav-item i {
            width: 20px;
            margin-left: 15px;
            font-size: 1.1rem;
        }

        .nav-badge {
            margin-right: auto;
            background: var(--secondary-gradient);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        /* المحتوى الرئيسي */
        .main-content {
            margin-right: var(--sidebar-width);
            min-height: 100vh;
            transition: var(--transition);
        }

        .dashboard-header {
            background: white;
            height: var(--header-height);
            box-shadow: var(--shadow-soft);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .sidebar-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark-color);
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: var(--transition);
        }

        .sidebar-toggle:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--dark-color);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-search {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-input {
            padding: 10px 40px 10px 15px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 25px;
            background: #f8fafc;
            outline: none;
            width: 250px;
            transition: var(--transition);
        }

        .search-input:focus {
            background: white;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 15px;
            color: #7f8c8d;
        }

        .header-notifications {
            position: relative;
            cursor: pointer;
        }

        .notification-icon {
            font-size: 1.3rem;
            color: #7f8c8d;
            transition: var(--transition);
        }

        .notification-icon:hover {
            color: var(--dark-color);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            left: -5px;
            background: var(--secondary-gradient);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            padding: 8px 15px;
            border-radius: 25px;
            transition: var(--transition);
        }

        .user-menu:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .user-info {
            text-align: right;
        }

        .user-name {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.9rem;
        }

        .user-role {
            color: #7f8c8d;
            font-size: 0.8rem;
        }

        .logout-btn {
            background: var(--danger-gradient);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }

        .logout-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-soft);
        }

        /* محتوى الصفحة */
        .page-content {
            padding: 30px;
        }

        /* بطاقات الإحصائيات المحسنة */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-soft);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-strong);
        }

        .stat-card:hover::before {
            transform: scaleX(1);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
        }

        .stat-icon.primary {
            background: var(--primary-gradient);
        }

        .stat-icon.success {
            background: var(--success-gradient);
        }

        .stat-icon.warning {
            background: var(--warning-gradient);
            color: var(--dark-color);
        }

        .stat-icon.danger {
            background: var(--danger-gradient);
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.8rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 20px;
        }

        .stat-trend.up {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }

        .stat-trend.down {
            background: rgba(244, 67, 54, 0.1);
            color: #f44336;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1rem;
            font-weight: 500;
        }

        .stat-description {
            color: #95a5a6;
            font-size: 0.85rem;
            margin-top: 10px;
            line-height: 1.4;
        }

        /* الرسوم البيانية */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 25px;
            margin-bottom: 40px;
        }

        .chart-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark-color);
        }

        .chart-period {
            display: flex;
            gap: 10px;
        }

        .period-btn {
            padding: 6px 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: var(--transition);
        }

        .period-btn.active,
        .period-btn:hover {
            background: var(--primary-gradient);
            color: white;
            border-color: transparent;
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        /* جدول الطلبات المحسن */
        .requests-section {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .section-header {
            padding: 25px 30px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-icon {
            width: 40px;
            height: 40px;
            background: var(--primary-gradient);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
        }

        .section-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .filter-dropdown {
            position: relative;
        }

        .filter-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            background: #f8fafc;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .filter-btn:hover {
            background: white;
            border-color: #667eea;
        }

        .bulk-actions {
            display: flex;
            gap: 10px;
            opacity: 0;
            transform: translateY(-10px);
            transition: var(--transition);
        }

        .bulk-actions.show {
            opacity: 1;
            transform: translateY(0);
        }

        .bulk-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 600;
            transition: var(--transition);
        }

        .bulk-btn.approve {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }

        .bulk-btn.reject {
            background: rgba(244, 67, 54, 0.1);
            color: #f44336;
        }

        .bulk-btn:hover {
            transform: translateY(-2px);
        }

        /* جدول البيانات */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 15px 20px;
            text-align: right;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.9rem;
            position: sticky;
            top: 0;
        }

        .data-table tbody tr {
            transition: var(--transition);
        }

        .data-table tbody tr:hover {
            background: rgba(102, 126, 234, 0.02);
        }

        .table-checkbox {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .request-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .request-avatar {
            width: 45px;
            height: 45px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .request-details h4 {
            color: var(--dark-color);
            font-weight: 600;
            margin-bottom: 4px;
        }

        .request-details p {
            color: #7f8c8d;
            font-size: 0.85rem;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
        }

        .status-pending {
            background: rgba(255, 152, 0, 0.1);
            color: #ff9800;
        }

        .status-approved {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }

        .status-rejected {
            background: rgba(244, 67, 54, 0.1);
            color: #f44336;
        }

        .status-processing {
            background: rgba(33, 150, 243, 0.1);
            color: #2196f3;
        }

        .license-badge {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .license-trial {
            background: rgba(156, 39, 176, 0.1);
            color: #9c27b0;
        }

        .license-basic {
            background: rgba(33, 150, 243, 0.1);
            color: #2196f3;
        }

        .license-professional {
            background: rgba(255, 152, 0, 0.1);
            color: #ff9800;
        }

        .license-enterprise {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .action-btn.view {
            background: rgba(33, 150, 243, 0.1);
            color: #2196f3;
        }

        .action-btn.approve {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
        }

        .action-btn.reject {
            background: rgba(244, 67, 54, 0.1);
            color: #f44336;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        /* النوافذ المنبثقة المحسنة */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-strong);
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.8);
            transition: var(--transition);
        }

        .modal-overlay.show .modal-content {
            transform: scale(1);
        }

        .modal-header {
            padding: 25px 30px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark-color);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #7f8c8d;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: var(--transition);
        }

        .modal-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--dark-color);
        }

        .modal-body {
            padding: 30px;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        /* رسائل التنبيه المحسنة */
        .toast-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10001;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .toast {
            background: white;
            border-radius: 12px;
            padding: 20px 25px;
            box-shadow: var(--shadow-strong);
            display: flex;
            align-items: center;
            gap: 15px;
            min-width: 350px;
            transform: translateX(-100%);
            transition: var(--transition);
            border-left: 5px solid #4caf50;
            position: relative;
            overflow: hidden;
        }

        .toast::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: #4caf50;
            animation: toast-progress 5s linear;
        }

        @keyframes toast-progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            border-left-color: #4caf50;
        }

        .toast.success::before {
            background: #4caf50;
        }

        .toast.error {
            border-left-color: #f44336;
        }

        .toast.error::before {
            background: #f44336;
        }

        .toast.warning {
            border-left-color: #ff9800;
        }

        .toast.warning::before {
            background: #ff9800;
        }

        .toast-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .toast.success .toast-icon {
            background: #4caf50;
        }

        .toast.error .toast-icon {
            background: #f44336;
        }

        .toast.warning .toast-icon {
            background: #ff9800;
        }

        .toast-content {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .toast-message {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .toast-close {
            background: none;
            border: none;
            color: #bdc3c7;
            cursor: pointer;
            font-size: 1.2rem;
            padding: 5px;
            border-radius: 50%;
            transition: var(--transition);
        }

        .toast-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: var(--dark-color);
        }

        /* التجاوب المحسن */
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .dashboard-sidebar {
                transform: translateX(100%);
                width: 100%;
            }

            .dashboard-sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .sidebar-toggle {
                display: block;
            }

            .page-content {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header-search {
                display: none;
            }

            .section-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .section-actions {
                justify-content: space-between;
            }

            .data-table {
                font-size: 0.85rem;
            }

            .data-table th,
            .data-table td {
                padding: 10px 15px;
            }

            .toast {
                min-width: 300px;
                margin: 0 10px;
            }

            .toast-container {
                left: 0;
                right: 0;
                top: 10px;
            }

            .modal-content {
                width: 95%;
                margin: 10px;
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 1.5rem;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-value {
                font-size: 2rem;
            }

            .chart-card {
                padding: 20px;
            }

            .chart-container {
                height: 250px;
            }

            .action-buttons {
                flex-direction: column;
                gap: 5px;
            }

            .action-btn {
                width: 30px;
                height: 30px;
                font-size: 0.8rem;
            }
        }

        /* تأثيرات الحركة */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-right {
            animation: slideInRight 0.6s ease-out;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .bounce-in {
            animation: bounceIn 0.8s ease-out;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* حالة التحميل */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .loading-content {
            text-align: center;
            color: var(--dark-color);
        }

        .loading-content .loading-spinner {
            width: 40px;
            height: 40px;
            border-color: rgba(102, 126, 234, 0.3);
            border-top-color: #667eea;
            margin-bottom: 15px;
        }

        /* حالة فارغة */
        .empty-state {
            text-align: center;
            padding: 60px 30px;
            color: #7f8c8d;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .empty-state-description {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .empty-state-action {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: var(--primary-gradient);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: var(--transition);
        }

        .empty-state-action:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-soft);
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول الإدارية -->
    <div class="admin-login-overlay" id="adminLoginOverlay">
        <div class="admin-login-card fade-in">
            <div class="login-header">
                <div class="admin-logo bounce-in">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="login-title">لوحة التحكم الإدارية</h1>
                <p class="login-subtitle">مؤسسة وقود المستقبل - الجزائر</p>
            </div>

            <div class="credentials-info">
                <h4>📋 بيانات تسجيل الدخول:</h4>
                <p><strong>اسم المدير:</strong> developer</p>
                <p><strong>كلمة المرور:</strong> dev123456</p>
                <p><strong>المفتاح السري:</strong> FUTUREFUEL2024ADMIN</p>
            </div>

            <button class="auto-fill-btn" onclick="autoFillAdminLogin()">
                📝 ملء البيانات تلقائياً
            </button>

            <div id="adminLoginMessage" class="message" style="display: none;"></div>

            <form id="adminLoginForm">
                <div class="form-group">
                    <input type="text" class="form-input" id="adminUsername" placeholder=" " required>
                    <label class="form-label" for="adminUsername">اسم المدير</label>
                    <i class="fas fa-user-shield input-icon"></i>
                </div>

                <div class="form-group">
                    <input type="password" class="form-input" id="adminPassword" placeholder=" " required>
                    <label class="form-label" for="adminPassword">كلمة المرور</label>
                    <i class="fas fa-lock input-icon"></i>
                </div>

                <div class="form-group">
                    <input type="text" class="form-input" id="adminSecretKey" placeholder=" " required>
                    <label class="form-label" for="adminSecretKey">المفتاح السري</label>
                    <i class="fas fa-key input-icon"></i>
                </div>

                <button type="submit" class="enhanced-btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>دخول لوحة التحكم</span>
                </button>
            </form>
        </div>
    </div>

    <!-- لوحة التحكم الرئيسية -->
    <div class="dashboard-container" id="dashboardContainer">
        <!-- الشريط الجانبي -->
        <aside class="dashboard-sidebar" id="dashboardSidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-gas-pump"></i>
                </div>
                <h3 class="sidebar-title">وقود المستقبل</h3>
                <p class="sidebar-subtitle">لوحة التحكم - الجزائر</p>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">الرئيسية</div>
                    <a href="#" class="nav-item active" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة المعلومات</span>
                    </a>
                    <a href="#" class="nav-item" onclick="showSection('requests')">
                        <i class="fas fa-file-alt"></i>
                        <span>طلبات التفعيل</span>
                        <span class="nav-badge" id="pendingRequestsBadge">0</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">إدارة النظام</div>
                    <a href="#" class="nav-item" onclick="showSection('users')">
                        <i class="fas fa-users"></i>
                        <span>إدارة المستخدمين</span>
                    </a>
                    <a href="#" class="nav-item" onclick="showSection('licenses')">
                        <i class="fas fa-certificate"></i>
                        <span>إدارة التراخيص</span>
                    </a>
                    <a href="#" class="nav-item" onclick="showSection('analytics')">
                        <i class="fas fa-chart-bar"></i>
                        <span>التحليلات والتقارير</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">الإعدادات</div>
                    <a href="#" class="nav-item" onclick="showSection('notifications')">
                        <i class="fas fa-bell"></i>
                        <span>الإشعارات</span>
                    </a>
                    <a href="#" class="nav-item" onclick="showSection('logs')">
                        <i class="fas fa-list-alt"></i>
                        <span>سجل النظام</span>
                    </a>
                    <a href="#" class="nav-item" onclick="showSection('settings')">
                        <i class="fas fa-cog"></i>
                        <span>إعدادات النظام</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- رأس الصفحة -->
            <header class="dashboard-header">
                <div class="header-left">
                    <button class="sidebar-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div>
                        <h1 class="page-title" id="pageTitle">لوحة المعلومات</h1>
                        <div class="breadcrumb" id="breadcrumb">
                            <a href="#">الرئيسية</a>
                            <i class="fas fa-chevron-left"></i>
                            <span>لوحة المعلومات</span>
                        </div>
                    </div>
                </div>

                <div class="header-right">
                    <div class="header-search">
                        <input type="text" class="search-input" placeholder="البحث...">
                        <i class="fas fa-search search-icon"></i>
                    </div>

                    <div class="header-notifications" onclick="showNotifications()">
                        <i class="fas fa-bell notification-icon"></i>
                        <span class="notification-badge" id="notificationBadge">3</span>
                    </div>

                    <div class="user-menu" onclick="showUserMenu()">
                        <div class="user-avatar">
                            <span id="userInitials">AD</span>
                        </div>
                        <div class="user-info">
                            <div class="user-name" id="userName">المطور الرئيسي</div>
                            <div class="user-role">مدير النظام</div>
                        </div>
                        <i class="fas fa-chevron-down"></i>
                    </div>

                    <button class="logout-btn" onclick="logout()">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </button>
                </div>
            </header>

            <!-- محتوى الصفحة -->
            <div class="page-content">
                <!-- قسم لوحة المعلومات -->
                <div class="content-section" id="dashboardSection">
                    <!-- بطاقات الإحصائيات -->
                    <div class="stats-grid">
                        <div class="stat-card fade-in">
                            <div class="stat-header">
                                <div class="stat-icon primary">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <div class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+12%</span>
                                </div>
                            </div>
                            <div class="stat-value" id="totalRequestsCount">0</div>
                            <div class="stat-label">إجمالي الطلبات</div>
                            <div class="stat-description">العدد الكلي لطلبات التفعيل المستلمة</div>
                        </div>

                        <div class="stat-card fade-in">
                            <div class="stat-header">
                                <div class="stat-icon warning">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+5</span>
                                </div>
                            </div>
                            <div class="stat-value" id="pendingRequestsCount">0</div>
                            <div class="stat-label">طلبات معلقة</div>
                            <div class="stat-description">الطلبات التي تحتاج إلى مراجعة</div>
                        </div>

                        <div class="stat-card fade-in">
                            <div class="stat-header">
                                <div class="stat-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-trend up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+8</span>
                                </div>
                            </div>
                            <div class="stat-value" id="approvedRequestsCount">0</div>
                            <div class="stat-label">طلبات مقبولة</div>
                            <div class="stat-description">الطلبات التي تم الموافقة عليها</div>
                        </div>

                        <div class="stat-card fade-in">
                            <div class="stat-header">
                                <div class="stat-icon danger">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="stat-trend down">
                                    <i class="fas fa-arrow-down"></i>
                                    <span>-2</span>
                                </div>
                            </div>
                            <div class="stat-value" id="rejectedRequestsCount">0</div>
                            <div class="stat-label">طلبات مرفوضة</div>
                            <div class="stat-description">الطلبات التي تم رفضها</div>
                        </div>
                    </div>

                    <!-- الرسوم البيانية -->
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">إحصائيات الطلبات</h3>
                                <div class="chart-period">
                                    <button class="period-btn active" onclick="updateChart('week')">أسبوع</button>
                                    <button class="period-btn" onclick="updateChart('month')">شهر</button>
                                    <button class="period-btn" onclick="updateChart('year')">سنة</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="requestsChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3 class="chart-title">توزيع أنواع التراخيص</h3>
                            </div>
                            <div class="chart-container">
                                <canvas id="licensesChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- ملخص سريع -->
                    <div class="stats-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); margin-bottom: 40px;">
                        <div class="stat-card" style="padding: 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: var(--primary-gradient); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--dark-color);" id="activeUsersCount">0</div>
                                    <div style="color: #7f8c8d; font-size: 0.9rem;">مستخدمين نشطين</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card" style="padding: 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: var(--success-gradient); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="fas fa-download"></i>
                                </div>
                                <div>
                                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--dark-color);" id="downloadsCount">0</div>
                                    <div style="color: #7f8c8d; font-size: 0.9rem;">تحميلات اليوم</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card" style="padding: 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: var(--warning-gradient); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: var(--dark-color);">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div>
                                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--dark-color);" id="issuesCount">0</div>
                                    <div style="color: #7f8c8d; font-size: 0.9rem;">مشاكل تحتاج حل</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card" style="padding: 20px;">
                            <div style="display: flex; align-items: center; gap: 15px;">
                                <div style="width: 50px; height: 50px; background: var(--danger-gradient); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white;">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div>
                                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--dark-color);" id="serverStatusCount">99%</div>
                                    <div style="color: #7f8c8d; font-size: 0.9rem;">حالة الخادم</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم طلبات التفعيل -->
                <div class="content-section" id="requestsSection" style="display: none;">
                    <div class="requests-section">
                        <div class="section-header">
                            <div class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                طلبات التفعيل
                            </div>
                            <div class="section-actions">
                                <div class="bulk-actions" id="bulkActions">
                                    <button class="bulk-btn approve" onclick="bulkApprove()">
                                        <i class="fas fa-check"></i>
                                        قبول المحدد
                                    </button>
                                    <button class="bulk-btn reject" onclick="bulkReject()">
                                        <i class="fas fa-times"></i>
                                        رفض المحدد
                                    </button>
                                </div>
                                <div class="filter-dropdown">
                                    <button class="filter-btn" onclick="toggleFilter()">
                                        <i class="fas fa-filter"></i>
                                        <span>تصفية</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
                                <button class="enhanced-btn btn-primary" style="width: auto; padding: 10px 20px;" onclick="refreshRequests()">
                                    <i class="fas fa-sync-alt"></i>
                                    <span>تحديث</span>
                                </button>
                            </div>
                        </div>

                        <div class="table-container" style="position: relative;">
                            <div class="loading-overlay" id="tableLoading">
                                <div class="loading-content">
                                    <div class="loading-spinner"></div>
                                    <p>جاري تحميل البيانات...</p>
                                </div>
                            </div>

                            <table class="data-table" id="requestsTable">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <input type="checkbox" class="table-checkbox" id="selectAllRequests" onchange="toggleSelectAll()">
                                        </th>
                                        <th>المتقدم</th>
                                        <th>نوع الترخيص</th>
                                        <th>تاريخ الطلب</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="requestsTableBody">
                                    <!-- سيتم ملؤها بواسطة JavaScript -->
                                </tbody>
                            </table>

                            <div class="empty-state" id="emptyState" style="display: none;">
                                <div class="empty-state-icon">
                                    <i class="fas fa-inbox"></i>
                                </div>
                                <h3 class="empty-state-title">لا توجد طلبات تفعيل</h3>
                                <p class="empty-state-description">
                                    لم يتم استلام أي طلبات تفعيل حتى الآن.<br>
                                    يمكن للمستخدمين إرسال طلبات التفعيل من نظام تسجيل الدخول.
                                </p>
                                <a href="../login-system/enhanced-login.html" class="empty-state-action" target="_blank">
                                    <i class="fas fa-plus"></i>
                                    إرسال طلب تجريبي
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الأقسام الأخرى (قيد التطوير) -->
                <div class="content-section" id="usersSection" style="display: none;">
                    <div class="chart-card">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="empty-state-title">إدارة المستخدمين</h3>
                            <p class="empty-state-description">
                                هذا القسم قيد التطوير حالياً.<br>
                                سيتم إضافة إدارة شاملة للمستخدمين قريباً.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="content-section" id="licensesSection" style="display: none;">
                    <div class="chart-card">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <h3 class="empty-state-title">إدارة التراخيص</h3>
                            <p class="empty-state-description">
                                هذا القسم قيد التطوير حالياً.<br>
                                سيتم إضافة إدارة شاملة للتراخيص قريباً.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="content-section" id="analyticsSection" style="display: none;">
                    <div class="chart-card">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h3 class="empty-state-title">التحليلات والتقارير</h3>
                            <p class="empty-state-description">
                                هذا القسم قيد التطوير حالياً.<br>
                                سيتم إضافة تحليلات وتقارير مفصلة قريباً.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="content-section" id="notificationsSection" style="display: none;">
                    <div class="chart-card">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <h3 class="empty-state-title">إدارة الإشعارات</h3>
                            <p class="empty-state-description">
                                هذا القسم قيد التطوير حالياً.<br>
                                سيتم إضافة نظام إشعارات متقدم قريباً.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="content-section" id="logsSection" style="display: none;">
                    <div class="chart-card">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <h3 class="empty-state-title">سجل النظام</h3>
                            <p class="empty-state-description">
                                هذا القسم قيد التطوير حالياً.<br>
                                سيتم إضافة سجل مفصل لجميع العمليات قريباً.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="content-section" id="settingsSection" style="display: none;">
                    <div class="chart-card">
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <h3 class="empty-state-title">إعدادات النظام</h3>
                            <p class="empty-state-description">
                                هذا القسم قيد التطوير حالياً.<br>
                                سيتم إضافة إعدادات شاملة للنظام قريباً.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- حاوي رسائل التنبيه -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- النوافذ المنبثقة -->
    <div class="modal-overlay" id="modalOverlay">
        <div class="modal-content" id="modalContent">
            <!-- محتوى النافذة المنبثقة -->
        </div>
    </div>

    <script>
        // متغيرات عامة
        const ADMIN_CREDENTIALS = {
            username: 'developer',
            password: 'dev123456',
            secretKey: 'FUTUREFUEL2024ADMIN'
        };

        let isLoggedIn = false;
        let currentSection = 'dashboard';
        let activationRequests = [];
        let selectedRequests = [];
        let charts = {};

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل لوحة التحكم المحسنة');

            // فحص حالة تسجيل الدخول
            checkAuthStatus();

            // إعداد مستمعي الأحداث
            setupEventListeners();

            // تحميل البيانات
            loadData();

            // إضافة أدوات المطور
            addDeveloperTools();

            console.log('✅ تم تحميل لوحة التحكم بنجاح');
        });

        // فحص حالة المصادقة
        function checkAuthStatus() {
            const savedAuth = localStorage.getItem('adminAuth');
            if (savedAuth) {
                try {
                    const authData = JSON.parse(savedAuth);
                    const now = new Date().getTime();

                    // فحص انتهاء الجلسة (4 ساعات)
                    if (now - authData.loginTime < 4 * 60 * 60 * 1000) {
                        isLoggedIn = true;
                        showDashboard();
                        return;
                    } else {
                        localStorage.removeItem('adminAuth');
                    }
                } catch (e) {
                    localStorage.removeItem('adminAuth');
                }
            }

            showLoginScreen();
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // نموذج تسجيل الدخول
            document.getElementById('adminLoginForm').addEventListener('submit', handleAdminLogin);

            // إغلاق النافذة المنبثقة عند النقر خارجها
            document.getElementById('modalOverlay').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            // اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        }

        // تحميل البيانات
        function loadData() {
            console.log('📥 تحميل البيانات من localStorage...');

            // تحميل طلبات التفعيل
            const storedRequests = localStorage.getItem('activationRequests');
            console.log('💾 البيانات المحفوظة:', storedRequests);

            activationRequests = JSON.parse(storedRequests || '[]');
            console.log('📊 الطلبات المحملة:', activationRequests);
            console.log('🔢 عدد الطلبات:', activationRequests.length);

            // تحديث الواجهة
            if (isLoggedIn) {
                console.log('✅ المستخدم مسجل دخوله - تحديث الواجهة');
                updateDashboardData();
                updateRequestsTable();
                if (typeof Chart !== 'undefined') {
                    initializeCharts();
                } else {
                    console.warn('⚠️ Chart.js غير محمل بعد');
                }
            } else {
                console.log('❌ المستخدم غير مسجل دخوله');
            }
        }

        // معالجة تسجيل دخول المدير
        async function handleAdminLogin(event) {
            event.preventDefault();
            console.log('🔐 محاولة تسجيل دخول المدير');

            const username = document.getElementById('adminUsername').value.trim();
            const password = document.getElementById('adminPassword').value.trim();
            const secretKey = document.getElementById('adminSecretKey').value.trim();

            if (!username || !password || !secretKey) {
                showToast('يرجى ملء جميع الحقول المطلوبة', 'error', 'بيانات ناقصة');
                return;
            }

            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            // إظهار حالة التحميل
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="loading-spinner"></div> <span>جاري التحقق...</span>';

            try {
                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 2000));

                // التحقق من البيانات
                if (username === ADMIN_CREDENTIALS.username &&
                    password === ADMIN_CREDENTIALS.password &&
                    secretKey === ADMIN_CREDENTIALS.secretKey) {

                    console.log('✅ نجح تسجيل دخول المدير');

                    // حفظ بيانات المصادقة
                    const authData = {
                        username: username,
                        role: 'admin',
                        loginTime: new Date().getTime()
                    };
                    localStorage.setItem('adminAuth', JSON.stringify(authData));

                    isLoggedIn = true;
                    showToast('تم تسجيل الدخول بنجاح!', 'success', 'مرحباً بك');

                    // الانتقال للوحة التحكم
                    setTimeout(() => {
                        showDashboard();
                        loadData();
                    }, 1000);

                } else {
                    throw new Error('بيانات المصادقة غير صحيحة');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showToast(error.message, 'error', 'فشل تسجيل الدخول');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        }

        // إظهار شاشة تسجيل الدخول
        function showLoginScreen() {
            document.getElementById('adminLoginOverlay').style.display = 'flex';
            document.getElementById('dashboardContainer').style.display = 'none';
        }

        // إظهار لوحة التحكم
        function showDashboard() {
            document.getElementById('adminLoginOverlay').style.display = 'none';
            document.getElementById('dashboardContainer').style.display = 'block';

            // تحديث معلومات المستخدم
            const authData = JSON.parse(localStorage.getItem('adminAuth') || '{}');
            document.getElementById('userName').textContent = authData.username || 'المدير';
            document.getElementById('userInitials').textContent = (authData.username || 'AD').substring(0, 2).toUpperCase();
        }

        // ملء بيانات تسجيل الدخول تلقائياً
        function autoFillAdminLogin() {
            document.getElementById('adminUsername').value = ADMIN_CREDENTIALS.username;
            document.getElementById('adminPassword').value = ADMIN_CREDENTIALS.password;
            document.getElementById('adminSecretKey').value = ADMIN_CREDENTIALS.secretKey;
            showToast('تم ملء البيانات تلقائياً!', 'success', 'جاهز للدخول');
        }

        // تحديث بيانات لوحة التحكم
        function updateDashboardData() {
            console.log('📊 تحديث بيانات لوحة التحكم');

            // حساب الإحصائيات
            const stats = {
                total: activationRequests.length,
                pending: activationRequests.filter(req => req.status === 'pending').length,
                approved: activationRequests.filter(req => req.status === 'approved').length,
                rejected: activationRequests.filter(req => req.status === 'rejected').length
            };

            // تحديث بطاقات الإحصائيات
            document.getElementById('totalRequestsCount').textContent = stats.total;
            document.getElementById('pendingRequestsCount').textContent = stats.pending;
            document.getElementById('approvedRequestsCount').textContent = stats.approved;
            document.getElementById('rejectedRequestsCount').textContent = stats.rejected;

            // تحديث شارة الطلبات المعلقة
            document.getElementById('pendingRequestsBadge').textContent = stats.pending;

            // تحديث الإحصائيات الإضافية (بيانات تجريبية)
            document.getElementById('activeUsersCount').textContent = Math.floor(Math.random() * 50) + 10;
            document.getElementById('downloadsCount').textContent = Math.floor(Math.random() * 20) + 5;
            document.getElementById('issuesCount').textContent = Math.floor(Math.random() * 5);

            // تحديث شارة الإشعارات
            document.getElementById('notificationBadge').textContent = stats.pending + Math.floor(Math.random() * 3);
        }

        // تحديث جدول الطلبات
        function updateRequestsTable() {
            console.log('🔄 تحديث جدول الطلبات...');
            console.log('📊 عدد الطلبات:', activationRequests.length);
            console.log('📋 الطلبات:', activationRequests);

            const tableBody = document.getElementById('requestsTableBody');
            const emptyState = document.getElementById('emptyState');

            if (!tableBody) {
                console.error('❌ لم يتم العثور على عنصر requestsTableBody');
                return;
            }

            if (!emptyState) {
                console.error('❌ لم يتم العثور على عنصر emptyState');
                return;
            }

            if (activationRequests.length === 0) {
                console.log('📭 لا توجد طلبات - إظهار الحالة الفارغة');
                tableBody.innerHTML = '';
                emptyState.style.display = 'block';
                return;
            }

            console.log('📋 إظهار الطلبات في الجدول');
            emptyState.style.display = 'none';

            tableBody.innerHTML = activationRequests.map(request => `
                <tr data-request-id="${request.id}">
                    <td>
                        <input type="checkbox" class="table-checkbox request-checkbox" value="${request.id}" onchange="updateBulkActions()">
                    </td>
                    <td>
                        <div class="request-info">
                            <div class="request-avatar">
                                ${request.data.fullName.charAt(0).toUpperCase()}
                            </div>
                            <div class="request-details">
                                <h4>${request.data.fullName}</h4>
                                <p>${request.data.email}</p>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="license-badge license-${request.data.licenseType}">
                            ${getLicenseTypeText(request.data.licenseType)}
                        </span>
                    </td>
                    <td>${new Date(request.createdAt).toLocaleDateString('ar-DZ')}</td>
                    <td>
                        <span class="status-badge status-${request.status}">
                            ${getStatusText(request.status)}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button class="action-btn view" onclick="viewRequest('${request.id}')" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${request.status === 'pending' ? `
                                <button class="action-btn approve" onclick="approveRequest('${request.id}')" title="قبول">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="action-btn reject" onclick="rejectRequest('${request.id}')" title="رفض">
                                    <i class="fas fa-times"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        // الحصول على نص نوع الترخيص
        function getLicenseTypeText(type) {
            const typeMap = {
                'trial': 'تجريبي',
                'basic': 'أساسي',
                'professional': 'احترافي',
                'enterprise': 'مؤسسي'
            };
            return typeMap[type] || type;
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const statusMap = {
                'pending': 'معلق',
                'processing': 'قيد المعالجة',
                'approved': 'مقبول',
                'rejected': 'مرفوض'
            };
            return statusMap[status] || status;
        }

        // الحصول على اسم الولاية
        function getWilayaText(wilayaCode) {
            const wilayaMap = {
                '01': 'أدرار', '02': 'الشلف', '03': 'الأغواط', '04': 'أم البواقي',
                '05': 'باتنة', '06': 'بجاية', '07': 'بسكرة', '08': 'بشار',
                '09': 'البليدة', '10': 'البويرة', '11': 'تمنراست', '12': 'تبسة',
                '13': 'تلمسان', '14': 'تيارت', '15': 'تيزي وزو', '16': 'الجزائر العاصمة',
                '17': 'الجلفة', '18': 'جيجل', '19': 'سطيف', '20': 'سعيدة',
                '21': 'سكيكدة', '22': 'سيدي بلعباس', '23': 'عنابة', '24': 'قالمة',
                '25': 'قسنطينة', '26': 'المدية', '27': 'مستغانم', '28': 'المسيلة',
                '29': 'معسكر', '30': 'ورقلة', '31': 'وهران', '32': 'البيض',
                '33': 'إليزي', '34': 'برج بوعريريج', '35': 'بومرداس', '36': 'الطارف',
                '37': 'تندوف', '38': 'تيسمسيلت', '39': 'الوادي', '40': 'خنشلة',
                '41': 'سوق أهراس', '42': 'تيبازة', '43': 'ميلة', '44': 'عين الدفلى',
                '45': 'النعامة', '46': 'عين تموشنت', '47': 'غرداية', '48': 'غليزان',
                '49': 'تيميمون', '50': 'برج باجي مختار', '51': 'أولاد جلال', '52': 'بني عباس',
                '53': 'عين صالح', '54': 'عين قزام', '55': 'تقرت', '56': 'جانت',
                '57': 'المقرية', '58': 'المنيعة'
            };
            return wilayaMap[wilayaCode] || wilayaCode;
        }

        // تهيئة الرسوم البيانية
        function initializeCharts() {
            // رسم بياني لإحصائيات الطلبات
            const requestsCtx = document.getElementById('requestsChart').getContext('2d');
            charts.requests = new Chart(requestsCtx, {
                type: 'line',
                data: {
                    labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                    datasets: [{
                        label: 'طلبات جديدة',
                        data: [12, 19, 3, 5, 2, 3, 7],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'طلبات مقبولة',
                        data: [8, 15, 2, 4, 1, 2, 5],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // رسم بياني لتوزيع أنواع التراخيص
            const licensesCtx = document.getElementById('licensesChart').getContext('2d');
            const licenseStats = {
                trial: activationRequests.filter(req => req.data.licenseType === 'trial').length,
                basic: activationRequests.filter(req => req.data.licenseType === 'basic').length,
                professional: activationRequests.filter(req => req.data.licenseType === 'professional').length,
                enterprise: activationRequests.filter(req => req.data.licenseType === 'enterprise').length
            };

            charts.licenses = new Chart(licensesCtx, {
                type: 'doughnut',
                data: {
                    labels: ['تجريبي', 'أساسي', 'احترافي', 'مؤسسي'],
                    datasets: [{
                        data: [licenseStats.trial, licenseStats.basic, licenseStats.professional, licenseStats.enterprise],
                        backgroundColor: [
                            '#9c27b0',
                            '#2196f3',
                            '#ff9800',
                            '#4caf50'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    }
                }
            });
        }

        // وظائف التنقل
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // إزالة الفئة النشطة من جميع عناصر التنقل
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // إظهار القسم المحدد
            document.getElementById(sectionName + 'Section').style.display = 'block';

            // إضافة الفئة النشطة للعنصر المحدد
            event.target.classList.add('active');

            // تحديث عنوان الصفحة
            const titles = {
                'dashboard': 'لوحة المعلومات',
                'requests': 'طلبات التفعيل',
                'users': 'إدارة المستخدمين',
                'licenses': 'إدارة التراخيص',
                'analytics': 'التحليلات والتقارير',
                'notifications': 'الإشعارات',
                'logs': 'سجل النظام',
                'settings': 'إعدادات النظام'
            };

            document.getElementById('pageTitle').textContent = titles[sectionName] || sectionName;
            document.getElementById('breadcrumb').innerHTML = `
                <a href="#">الرئيسية</a>
                <i class="fas fa-chevron-left"></i>
                <span>${titles[sectionName] || sectionName}</span>
            `;

            currentSection = sectionName;

            // تحديث البيانات حسب القسم
            if (sectionName === 'requests') {
                console.log('🔄 الانتقال لقسم الطلبات - تحديث الجدول');
                loadData(); // إعادة تحميل البيانات
                updateRequestsTable();
            }
        }

        // تبديل الشريط الجانبي (للهواتف)
        function toggleSidebar() {
            const sidebar = document.getElementById('dashboardSidebar');
            sidebar.classList.toggle('show');
        }

        // وظائف إدارة الطلبات
        function viewRequest(requestId) {
            const request = activationRequests.find(req => req.id === requestId);
            if (!request) return;

            const modalContent = `
                <div class="modal-header">
                    <h3 class="modal-title">تفاصيل طلب التفعيل</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 25px;">
                        <div>
                            <h4 style="color: var(--dark-color); margin-bottom: 10px;">المعلومات الشخصية</h4>
                            <p><strong>الاسم الكامل:</strong> ${request.data.fullName}</p>
                            <p><strong>البريد الإلكتروني:</strong> ${request.data.email}</p>
                            <p><strong>رقم الهاتف:</strong> ${request.data.phone}</p>
                            <p><strong>الشركة:</strong> ${request.data.company || 'غير محدد'}</p>
                            <p><strong>المسمى الوظيفي:</strong> ${request.data.jobTitle || 'غير محدد'}</p>
                            <p><strong>الولاية:</strong> ${getWilayaText(request.data.wilaya)}</p>
                        </div>
                        <div>
                            <h4 style="color: var(--dark-color); margin-bottom: 10px;">تفاصيل الطلب</h4>
                            <p><strong>رقم الطلب:</strong> ${request.id}</p>
                            <p><strong>نوع الترخيص:</strong> ${getLicenseTypeText(request.data.licenseType)}</p>
                            <p><strong>تاريخ الطلب:</strong> ${new Date(request.createdAt).toLocaleString('ar-DZ')}</p>
                            <p><strong>الحالة:</strong> <span class="status-badge status-${request.status}">${getStatusText(request.status)}</span></p>
                            <p><strong>نوع النشاط:</strong> ${request.data.businessType || 'غير محدد'}</p>
                            <p><strong>عدد المستخدمين:</strong> ${request.data.expectedUsers || 'غير محدد'}</p>
                        </div>
                    </div>
                    ${request.data.notes ? `
                        <div style="margin-bottom: 25px;">
                            <h4 style="color: var(--dark-color); margin-bottom: 10px;">ملاحظات إضافية</h4>
                            <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid rgba(0,0,0,0.1);">
                                ${request.data.notes}
                            </div>
                        </div>
                    ` : ''}
                    <div>
                        <h4 style="color: var(--dark-color); margin-bottom: 10px;">معلومات النظام</h4>
                        <div style="background: #f8fafc; padding: 15px; border-radius: 8px; border: 1px solid rgba(0,0,0,0.1); font-family: monospace; font-size: 0.85rem;">
                            <p><strong>معرف الجهاز:</strong> ${request.systemInfo?.deviceId || 'غير متاح'}</p>
                            <p><strong>نظام التشغيل:</strong> ${request.systemInfo?.platform || 'غير متاح'}</p>
                            <p><strong>المتصفح:</strong> ${request.systemInfo?.userAgent?.split(' ')[0] || 'غير متاح'}</p>
                            <p><strong>دقة الشاشة:</strong> ${request.systemInfo?.screenResolution || 'غير متاح'}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    ${request.status === 'pending' ? `
                        <button class="enhanced-btn btn-primary" onclick="approveRequest('${request.id}'); closeModal();" style="width: auto; padding: 10px 20px;">
                            <i class="fas fa-check"></i>
                            <span>قبول الطلب</span>
                        </button>
                        <button class="enhanced-btn" onclick="rejectRequest('${request.id}'); closeModal();" style="width: auto; padding: 10px 20px; background: var(--danger-gradient); color: white;">
                            <i class="fas fa-times"></i>
                            <span>رفض الطلب</span>
                        </button>
                    ` : ''}
                    <button class="enhanced-btn" onclick="closeModal()" style="width: auto; padding: 10px 20px; background: #95a5a6; color: white;">
                        إغلاق
                    </button>
                </div>
            `;

            showModal(modalContent);
        }

        function approveRequest(requestId) {
            const requestIndex = activationRequests.findIndex(req => req.id === requestId);
            if (requestIndex === -1) return;

            activationRequests[requestIndex].status = 'approved';
            activationRequests[requestIndex].updatedAt = new Date().toISOString();

            // حفظ التحديث
            localStorage.setItem('activationRequests', JSON.stringify(activationRequests));

            // تحديث الواجهة
            updateDashboardData();
            updateRequestsTable();

            showToast(`تم قبول طلب ${activationRequests[requestIndex].data.fullName} بنجاح`, 'success', 'تم القبول');
        }

        function rejectRequest(requestId) {
            const requestIndex = activationRequests.findIndex(req => req.id === requestId);
            if (requestIndex === -1) return;

            activationRequests[requestIndex].status = 'rejected';
            activationRequests[requestIndex].updatedAt = new Date().toISOString();

            // حفظ التحديث
            localStorage.setItem('activationRequests', JSON.stringify(activationRequests));

            // تحديث الواجهة
            updateDashboardData();
            updateRequestsTable();

            showToast(`تم رفض طلب ${activationRequests[requestIndex].data.fullName}`, 'warning', 'تم الرفض');
        }

        // وظائف الإجراءات المجمعة
        function toggleSelectAll() {
            const selectAllCheckbox = document.getElementById('selectAllRequests');
            const requestCheckboxes = document.querySelectorAll('.request-checkbox');

            requestCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });

            updateBulkActions();
        }

        function updateBulkActions() {
            const checkedBoxes = document.querySelectorAll('.request-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');

            selectedRequests = Array.from(checkedBoxes).map(cb => cb.value);

            if (selectedRequests.length > 0) {
                bulkActions.classList.add('show');
            } else {
                bulkActions.classList.remove('show');
            }
        }

        function bulkApprove() {
            if (selectedRequests.length === 0) return;

            if (confirm(`هل أنت متأكد من قبول ${selectedRequests.length} طلب؟`)) {
                selectedRequests.forEach(requestId => {
                    approveRequest(requestId);
                });

                // إلغاء التحديد
                document.getElementById('selectAllRequests').checked = false;
                updateBulkActions();

                showToast(`تم قبول ${selectedRequests.length} طلب بنجاح`, 'success', 'إجراء مجمع');
            }
        }

        function bulkReject() {
            if (selectedRequests.length === 0) return;

            if (confirm(`هل أنت متأكد من رفض ${selectedRequests.length} طلب؟`)) {
                selectedRequests.forEach(requestId => {
                    rejectRequest(requestId);
                });

                // إلغاء التحديد
                document.getElementById('selectAllRequests').checked = false;
                updateBulkActions();

                showToast(`تم رفض ${selectedRequests.length} طلب`, 'warning', 'إجراء مجمع');
            }
        }

        // تحديث البيانات
        function refreshRequests() {
            const tableLoading = document.getElementById('tableLoading');
            tableLoading.classList.add('show');

            setTimeout(() => {
                loadData();
                tableLoading.classList.remove('show');
                showToast('تم تحديث البيانات بنجاح', 'success', 'تحديث');
            }, 1000);
        }

        // وظائف النوافذ المنبثقة
        function showModal(content) {
            const overlay = document.getElementById('modalOverlay');
            const modalContent = document.getElementById('modalContent');

            modalContent.innerHTML = content;
            overlay.classList.add('show');
        }

        function closeModal() {
            const overlay = document.getElementById('modalOverlay');
            overlay.classList.remove('show');
        }

        // وظائف الإشعارات والقوائم
        function showNotifications() {
            const modalContent = `
                <div class="modal-header">
                    <h3 class="modal-title">الإشعارات</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div style="text-align: center; padding: 40px; color: #7f8c8d;">
                        <i class="fas fa-bell" style="font-size: 3rem; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3 style="margin-bottom: 10px; color: var(--dark-color);">لا توجد إشعارات جديدة</h3>
                        <p>سيتم عرض الإشعارات هنا عند توفرها</p>
                    </div>
                </div>
            `;
            showModal(modalContent);
        }

        function showUserMenu() {
            const modalContent = `
                <div class="modal-header">
                    <h3 class="modal-title">قائمة المستخدم</h3>
                    <button class="modal-close" onclick="closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <div style="width: 80px; height: 80px; background: var(--primary-gradient); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; margin: 0 auto 15px;">
                            ${document.getElementById('userInitials').textContent}
                        </div>
                        <h3 style="color: var(--dark-color); margin-bottom: 5px;">${document.getElementById('userName').textContent}</h3>
                        <p style="color: #7f8c8d;">مدير النظام</p>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 15px;">
                        <button class="enhanced-btn" onclick="closeModal()" style="width: 100%; padding: 15px; background: rgba(102, 126, 234, 0.1); color: #667eea;">
                            <i class="fas fa-user"></i>
                            <span>الملف الشخصي</span>
                        </button>
                        <button class="enhanced-btn" onclick="closeModal()" style="width: 100%; padding: 15px; background: rgba(102, 126, 234, 0.1); color: #667eea;">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </button>
                        <button class="enhanced-btn" onclick="logout(); closeModal();" style="width: 100%; padding: 15px; background: var(--danger-gradient); color: white;">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </button>
                    </div>
                </div>
            `;
            showModal(modalContent);
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('adminAuth');
                isLoggedIn = false;
                showLoginScreen();
                showToast('تم تسجيل الخروج بنجاح', 'success', 'وداعاً');
            }
        }

        // وظائف إضافية
        function updateChart(period) {
            // تحديث الرسم البياني حسب الفترة المحددة
            document.querySelectorAll('.period-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // هنا يمكن إضافة منطق تحديث البيانات حسب الفترة
            showToast(`تم تحديث الرسم البياني لفترة: ${period}`, 'info', 'تحديث الرسم البياني');
        }

        function toggleFilter() {
            showToast('خاصية التصفية قيد التطوير', 'info', 'قريباً');
        }

        // إظهار رسائل التنبيه المحسنة
        function showToast(message, type = 'info', title = '') {
            const container = document.getElementById('toastContainer');
            const toastId = 'toast-' + Date.now();

            const iconMap = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.id = toastId;

            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="${iconMap[type]}"></i>
                </div>
                <div class="toast-content">
                    ${title ? `<div class="toast-title">${title}</div>` : ''}
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="closeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(toast);

            // إظهار التنبيه
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // إخفاء التنبيه تلقائياً
            setTimeout(() => {
                closeToast(toastId);
            }, 5000);
        }

        // إغلاق رسالة التنبيه
        function closeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.remove('show');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // إنشاء طلبات تجريبية للاختبار
        function createSampleRequests() {
            const sampleRequests = [
                {
                    id: 'FF-TEST001',
                    data: {
                        fullName: 'أحمد بن محمد',
                        email: '<EMAIL>',
                        phone: '+*********** 456',
                        company: 'شركة الغاز الجزائرية',
                        jobTitle: 'مدير المبيعات',
                        wilaya: '16',
                        licenseType: 'professional',
                        businessType: 'gas-station',
                        expectedUsers: '11-25',
                        notes: 'نحتاج النظام لإدارة محطة الوقود الجديدة في الجزائر العاصمة'
                    },
                    systemInfo: {
                        deviceId: 'FF-TEST-DEVICE-001',
                        platform: 'Win32',
                        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        screenResolution: '1920x1080'
                    },
                    status: 'pending',
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'FF-TEST002',
                    data: {
                        fullName: 'فاطمة الزهراء',
                        email: '<EMAIL>',
                        phone: '+*********** 654',
                        company: 'مؤسسة النور للطاقة',
                        jobTitle: 'مديرة العمليات',
                        wilaya: '31',
                        licenseType: 'basic',
                        businessType: 'gas-distributor',
                        expectedUsers: '6-10',
                        notes: 'نريد تجربة النظام قبل الشراء'
                    },
                    systemInfo: {
                        deviceId: 'FF-TEST-DEVICE-002',
                        platform: 'MacIntel',
                        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                        screenResolution: '1440x900'
                    },
                    status: 'approved',
                    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'FF-TEST003',
                    data: {
                        fullName: 'محمد الأمين',
                        email: '<EMAIL>',
                        phone: '+*********** 789',
                        company: 'شركة الطاقة المتجددة',
                        jobTitle: 'مطور أعمال',
                        wilaya: '25',
                        licenseType: 'trial',
                        businessType: 'retail',
                        expectedUsers: '1-5',
                        notes: 'طلب تجريبي لتقييم النظام'
                    },
                    systemInfo: {
                        deviceId: 'FF-TEST-DEVICE-003',
                        platform: 'Linux x86_64',
                        userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
                        screenResolution: '1366x768'
                    },
                    status: 'pending',
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
                    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
                }
            ];

            // حفظ الطلبات التجريبية
            localStorage.setItem('activationRequests', JSON.stringify(sampleRequests));
            console.log('✅ تم إنشاء طلبات تجريبية:', sampleRequests);

            // إعادة تحميل البيانات
            loadData();

            showToast('تم إنشاء 3 طلبات تجريبية للاختبار!', 'success', 'بيانات تجريبية');
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                localStorage.removeItem('activationRequests');
                activationRequests = [];
                loadData();
                showToast('تم مسح جميع البيانات!', 'warning', 'مسح البيانات');
            }
        }

        // إضافة أدوات المطور (للتطوير فقط)
        function addDeveloperTools() {
            if (window.location.hostname === 'localhost' || window.location.protocol === 'file:') {
                // زر إنشاء بيانات تجريبية
                const devButton = document.createElement('button');
                devButton.innerHTML = '🧪 إنشاء بيانات تجريبية';
                devButton.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 20px;
                    background: #ff9800;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    z-index: 10000;
                    font-size: 12px;
                `;
                devButton.onclick = createSampleRequests;
                document.body.appendChild(devButton);

                // زر مسح البيانات
                const clearButton = document.createElement('button');
                clearButton.innerHTML = '🗑️ مسح البيانات';
                clearButton.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 180px;
                    background: #f44336;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    z-index: 10000;
                    font-size: 12px;
                `;
                clearButton.onclick = clearAllData;
                document.body.appendChild(clearButton);

                // زر إعادة تحميل البيانات
                const reloadButton = document.createElement('button');
                reloadButton.innerHTML = '🔄 إعادة تحميل';
                reloadButton.style.cssText = `
                    position: fixed;
                    bottom: 20px;
                    left: 310px;
                    background: #2196f3;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    cursor: pointer;
                    z-index: 10000;
                    font-size: 12px;
                `;
                reloadButton.onclick = () => {
                    loadData();
                    showToast('تم إعادة تحميل البيانات!', 'info', 'إعادة تحميل');
                };
                document.body.appendChild(reloadButton);
            }
        }

        // تحديث البيانات كل دقيقة
        setInterval(() => {
            if (isLoggedIn) {
                loadData();
            }
        }, 60000);

        // إضافة تأثيرات الحركة عند التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // مراقبة العناصر للتأثيرات
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.querySelectorAll('.stat-card, .chart-card, .requests-section').forEach(el => {
                    el.style.opacity = '0';
                    el.style.transform = 'translateY(20px)';
                    el.style.transition = 'all 0.6s ease-out';
                    observer.observe(el);
                });
            }, 500);
        });

    </script>
</body>
</html>
