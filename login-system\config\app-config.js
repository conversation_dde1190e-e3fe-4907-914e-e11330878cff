// إعدادات التطبيق
const AppConfig = {
    // معلومات التطبيق
    app: {
        name: 'مؤسسة وقود المستقبل',
        version: '2.2.0',
        description: 'نظام إدارة شامل ومتطور',
        developer: 'Future Fuel Corporation',
        website: 'https://futurefuel.sa',
        supportEmail: '<EMAIL>',
        supportPhone: '+966 50 123 4567'
    },

    // إعدادات المصادقة
    auth: {
        // مدة انتهاء الجلسة (بالدقائق)
        sessionTimeout: 120,
        
        // عدد محاولات تسجيل الدخول المسموحة
        maxLoginAttempts: 5,
        
        // مدة الحظر بعد تجاوز المحاولات (بالدقائق)
        lockoutDuration: 30,
        
        // تفعيل المصادقة الثنائية
        twoFactorEnabled: false,
        
        // تفعيل تذكر بيانات الدخول
        rememberMeEnabled: true,
        
        // مدة تذكر بيانات الدخول (بالأيام)
        rememberMeDuration: 30
    },

    // إعدادات طلبات التفعيل
    activation: {
        // أنواع التراخيص المتاحة
        licenseTypes: [
            {
                id: 'trial',
                name: 'تجريبي (30 يوم)',
                duration: 30,
                price: 0,
                currency: 'مجاني',
                features: [
                    'جميع المميزات الأساسية',
                    'حد أقصى 50 زبون',
                    'حد أقصى 100 بطاقة غاز',
                    'دعم فني محدود'
                ],
                limitations: [
                    'مدة محدودة 30 يوم',
                    'عدد محدود من السجلات',
                    'لا يشمل التحديثات المتقدمة'
                ]
            },
            {
                id: 'basic',
                name: 'أساسي (شهري)',
                duration: 30,
                price: 50,
                currency: 'دولار/شهر',
                features: [
                    'جميع المميزات الأساسية',
                    'حد أقصى 200 زبون',
                    'حد أقصى 500 بطاقة غاز',
                    'دعم فني عبر البريد الإلكتروني',
                    'نسخ احتياطية يومية'
                ],
                limitations: [
                    'لا يشمل المميزات المتقدمة',
                    'دعم فني محدود'
                ]
            },
            {
                id: 'professional',
                name: 'احترافي (سنوي)',
                duration: 365,
                price: 500,
                currency: 'دولار/سنة',
                features: [
                    'جميع المميزات',
                    'عدد غير محدود من السجلات',
                    'دعم فني على مدار الساعة',
                    'تحديثات مجانية',
                    'نسخ احتياطية متقدمة',
                    'تقارير مخصصة',
                    'تكامل مع الأنظمة الخارجية'
                ],
                limitations: []
            },
            {
                id: 'enterprise',
                name: 'مؤسسي (حسب الطلب)',
                duration: 365,
                price: 'حسب الطلب',
                currency: 'حسب الطلب',
                features: [
                    'جميع مميزات الإصدار الاحترافي',
                    'حلول مخصصة',
                    'تدريب مخصص',
                    'دعم فني مخصص',
                    'استضافة خاصة',
                    'تخصيص كامل للواجهة'
                ],
                limitations: []
            }
        ],

        // حقول طلب التفعيل المطلوبة
        requiredFields: [
            'fullName',
            'email',
            'phone',
            'licenseType'
        ],

        // حقول طلب التفعيل الاختيارية
        optionalFields: [
            'company',
            'notes'
        ],

        // إعدادات التحقق
        validation: {
            nameMinLength: 3,
            nameMaxLength: 50,
            phoneMinLength: 10,
            phoneMaxLength: 15,
            notesMaxLength: 500
        }
    },

    // إعدادات النظام
    system: {
        // اللغة الافتراضية
        defaultLanguage: 'ar',
        
        // اللغات المدعومة
        supportedLanguages: ['ar', 'en'],
        
        // المنطقة الزمنية
        timezone: 'Asia/Riyadh',
        
        // تنسيق التاريخ
        dateFormat: 'DD/MM/YYYY',
        
        // تنسيق الوقت
        timeFormat: 'HH:mm',
        
        // العملة الافتراضية
        defaultCurrency: 'SAR',
        
        // تفعيل الوضع المظلم
        darkModeEnabled: true,
        
        // تفعيل الإشعارات
        notificationsEnabled: true,
        
        // تفعيل الأصوات
        soundsEnabled: true
    },

    // إعدادات الواجهة
    ui: {
        // ألوان النظام
        colors: {
            primary: '#667eea',
            secondary: '#764ba2',
            accent: '#f093fb',
            success: '#4facfe',
            danger: '#ff6b6b',
            warning: '#feca57',
            dark: '#2c3e50',
            light: '#ffffff',
            gray: '#95a5a6'
        },

        // خطوط النظام
        fonts: {
            primary: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
            secondary: "'Arial', sans-serif",
            monospace: "'Courier New', monospace"
        },

        // أحجام الشاشة
        breakpoints: {
            mobile: '768px',
            tablet: '1024px',
            desktop: '1200px'
        },

        // مدة الانتقالات
        transitions: {
            fast: '0.2s',
            normal: '0.3s',
            slow: '0.5s'
        }
    },

    // إعدادات الأمان
    security: {
        // تفعيل HTTPS
        httpsOnly: true,
        
        // تفعيل تشفير البيانات المحلية
        encryptLocalData: true,
        
        // مفتاح التشفير (يجب تغييره في الإنتاج)
        encryptionKey: 'FutureFuel2024SecretKey',
        
        // تفعيل سجل الأنشطة
        activityLogging: true,
        
        // مدة الاحتفاظ بسجل الأنشطة (بالأيام)
        logRetentionDays: 90,
        
        // تفعيل فحص سلامة الملفات
        integrityCheck: true
    },

    // إعدادات الشبكة
    network: {
        // عنوان خادم المصادقة
        authServerUrl: 'https://api.futurefuel.sa/auth',
        
        // عنوان خادم التراخيص
        licenseServerUrl: 'https://api.futurefuel.sa/licenses',
        
        // مهلة انتظار الطلبات (بالثواني)
        requestTimeout: 30,
        
        // عدد محاولات إعادة الإرسال
        retryAttempts: 3,
        
        // تفعيل وضع عدم الاتصال
        offlineMode: true,
        
        // مدة التخزين المؤقت (بالدقائق)
        cacheTimeout: 60
    },

    // إعدادات التطوير
    development: {
        // تفعيل وضع التطوير
        debugMode: false,
        
        // تفعيل سجل وحدة التحكم
        consoleLogging: true,
        
        // مستوى السجل
        logLevel: 'info', // 'debug', 'info', 'warn', 'error'
        
        // تفعيل بيانات الاختبار
        mockData: false,
        
        // تفعيل أدوات التطوير
        devTools: false
    },

    // رسائل النظام
    messages: {
        // رسائل النجاح
        success: {
            loginSuccess: 'تم تسجيل الدخول بنجاح!',
            activationRequestSent: 'تم إرسال طلب التفعيل بنجاح!',
            passwordResetSent: 'تم إرسال رابط استعادة كلمة المرور',
            dataSaved: 'تم حفظ البيانات بنجاح'
        },

        // رسائل الخطأ
        error: {
            loginFailed: 'فشل في تسجيل الدخول',
            invalidCredentials: 'اسم المستخدم أو كلمة المرور غير صحيحة',
            networkError: 'خطأ في الاتصال بالشبكة',
            validationError: 'يرجى التحقق من البيانات المدخلة',
            serverError: 'خطأ في الخادم، يرجى المحاولة لاحقاً',
            sessionExpired: 'انتهت صلاحية الجلسة، يرجى تسجيل الدخول مرة أخرى'
        },

        // رسائل التحذير
        warning: {
            sessionExpiring: 'ستنتهي صلاحية الجلسة خلال 5 دقائق',
            unsavedChanges: 'لديك تغييرات غير محفوظة',
            weakPassword: 'كلمة المرور ضعيفة، يرجى اختيار كلمة مرور أقوى'
        },

        // رسائل المعلومات
        info: {
            loading: 'جاري التحميل...',
            processing: 'جاري المعالجة...',
            saving: 'جاري الحفظ...',
            connecting: 'جاري الاتصال...'
        }
    }
};

// تصدير الإعدادات
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppConfig;
} else if (typeof window !== 'undefined') {
    window.AppConfig = AppConfig;
}
