<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول مبسط - لوحة التحكم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 400px;
            width: 90%;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #7f8c8d;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .login-btn:hover {
            transform: translateY(-2px);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .credentials {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .credentials h3 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .credentials p {
            margin: 5px 0;
            font-family: monospace;
            font-size: 13px;
            color: #6c757d;
        }

        .message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .auto-fill {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            cursor: pointer;
            text-align: center;
            transition: background-color 0.3s;
        }

        .auto-fill:hover {
            background: #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🛡️ لوحة التحكم الإدارية</h1>
            <p>مؤسسة وقود المستقبل</p>
        </div>

        <div class="credentials">
            <h3>📋 بيانات تسجيل الدخول:</h3>
            <p><strong>اسم المدير:</strong> developer</p>
            <p><strong>كلمة المرور:</strong> dev123456</p>
            <p><strong>المفتاح السري:</strong> FUTUREFUEL2024ADMIN</p>
        </div>

        <div class="auto-fill" onclick="autoFill()">
            📝 اضغط هنا لملء البيانات تلقائياً
        </div>

        <div id="message" class="message" style="display: none;"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المدير:</label>
                <input type="text" id="username" required>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" required>
            </div>

            <div class="form-group">
                <label for="secretKey">المفتاح السري:</label>
                <input type="text" id="secretKey" required>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                🔐 تسجيل الدخول
            </button>
        </form>
    </div>

    <script>
        // بيانات المدير الصحيحة
        const adminCredentials = {
            username: 'developer',
            password: 'dev123456',
            secretKey: 'FUTUREFUEL2024ADMIN'
        };

        // ملء البيانات تلقائياً
        function autoFill() {
            document.getElementById('username').value = adminCredentials.username;
            document.getElementById('password').value = adminCredentials.password;
            document.getElementById('secretKey').value = adminCredentials.secretKey;
            showMessage('تم ملء البيانات تلقائياً!', 'success');
        }

        // إظهار الرسائل
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // معالجة تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const secretKey = document.getElementById('secretKey').value.trim();
            
            const loginBtn = document.getElementById('loginBtn');
            
            // التحقق من البيانات
            if (!username || !password || !secretKey) {
                showMessage('يرجى ملء جميع الحقول!', 'error');
                return;
            }
            
            // إظهار حالة التحميل
            loginBtn.disabled = true;
            loginBtn.textContent = '🔄 جاري التحقق...';
            
            // محاكاة تأخير الشبكة
            setTimeout(() => {
                if (username === adminCredentials.username && 
                    password === adminCredentials.password && 
                    secretKey === adminCredentials.secretKey) {
                    
                    // نجح تسجيل الدخول
                    showMessage('✅ نجح تسجيل الدخول! جاري التوجيه...', 'success');
                    
                    // حفظ بيانات المصادقة
                    const authData = {
                        username: username,
                        role: 'admin',
                        loginTime: new Date().getTime()
                    };
                    localStorage.setItem('adminAuth', JSON.stringify(authData));
                    
                    // التوجيه للوحة التحكم
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                    
                } else {
                    // فشل تسجيل الدخول
                    let errorMsg = '❌ بيانات غير صحيحة! ';
                    
                    if (username !== adminCredentials.username) {
                        errorMsg += 'اسم المدير خطأ. ';
                    }
                    if (password !== adminCredentials.password) {
                        errorMsg += 'كلمة المرور خطأ. ';
                    }
                    if (secretKey !== adminCredentials.secretKey) {
                        errorMsg += 'المفتاح السري خطأ.';
                    }
                    
                    showMessage(errorMsg, 'error');
                }
                
                // إعادة تعيين الزر
                loginBtn.disabled = false;
                loginBtn.textContent = '🔐 تسجيل الدخول';
                
            }, 1000);
        });

        // فحص الجلسة المحفوظة
        window.addEventListener('load', function() {
            const savedAuth = localStorage.getItem('adminAuth');
            if (savedAuth) {
                try {
                    const authData = JSON.parse(savedAuth);
                    const now = new Date().getTime();
                    
                    // فحص انتهاء الجلسة (4 ساعات)
                    if (now - authData.loginTime < 4 * 60 * 60 * 1000) {
                        showMessage('🎉 لديك جلسة نشطة! جاري التوجيه...', 'success');
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1500);
                        return;
                    } else {
                        localStorage.removeItem('adminAuth');
                    }
                } catch (e) {
                    localStorage.removeItem('adminAuth');
                }
            }
        });
    </script>
</body>
</html>
