<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - مؤسسة وقود المستقبل</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            direction: rtl;
        }

        /* شاشة تسجيل الدخول */
        .admin-login-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .admin-login-card {
            background: white;
            border-radius: 15px;
            padding: 40px;
            max-width: 400px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h2 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #7f8c8d;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .admin-login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .admin-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .admin-login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .credentials-hint {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .credentials-hint h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .credentials-hint p {
            margin: 5px 0;
            font-family: monospace;
            color: #424242;
        }

        /* لوحة التحكم */
        .dashboard-container {
            display: none;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .dashboard-header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dashboard-title {
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .logout-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: #c82333;
        }

        .dashboard-content {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #667eea;
        }

        .stat-card h3 {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #7f8c8d;
            font-size: 1rem;
        }

        .requests-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            color: #2c3e50;
            font-size: 1.3rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .request-item {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .request-info h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .request-info p {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .request-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .hidden {
            display: none !important;
        }

        .message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .auto-fill-btn {
            width: 100%;
            padding: 10px;
            background: #ffc107;
            color: #212529;
            border: none;
            border-radius: 5px;
            margin-bottom: 15px;
            cursor: pointer;
            font-weight: 600;
        }

        .auto-fill-btn:hover {
            background: #e0a800;
        }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div class="admin-login-overlay" id="adminLoginOverlay">
        <div class="admin-login-card">
            <div class="login-header">
                <i class="fas fa-shield-alt" style="font-size: 3rem; color: #667eea; margin-bottom: 15px;"></i>
                <h2>لوحة التحكم الإدارية</h2>
                <p>مؤسسة وقود المستقبل</p>
            </div>

            <div class="credentials-hint">
                <h4>📋 بيانات تسجيل الدخول:</h4>
                <p><strong>اسم المدير:</strong> developer</p>
                <p><strong>كلمة المرور:</strong> dev123456</p>
                <p><strong>المفتاح السري:</strong> FUTUREFUEL2024ADMIN</p>
            </div>

            <button class="auto-fill-btn" onclick="autoFillLogin()">
                📝 ملء البيانات تلقائياً
            </button>

            <div id="loginMessage" class="message" style="display: none;"></div>

            <form id="adminLoginForm">
                <div class="form-group">
                    <label for="adminUsername">اسم المدير:</label>
                    <input type="text" id="adminUsername" required>
                </div>

                <div class="form-group">
                    <label for="adminPassword">كلمة المرور:</label>
                    <input type="password" id="adminPassword" required>
                </div>

                <div class="form-group">
                    <label for="adminSecretKey">المفتاح السري:</label>
                    <input type="text" id="adminSecretKey" required>
                </div>

                <button type="submit" class="admin-login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    دخول لوحة التحكم
                </button>
            </form>
        </div>
    </div>

    <!-- لوحة التحكم -->
    <div class="dashboard-container hidden" id="dashboardContainer">
        <header class="dashboard-header">
            <h1 class="dashboard-title">
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم الإدارية
            </h1>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                تسجيل الخروج
            </button>
        </header>

        <div class="dashboard-content">
            <!-- إحصائيات -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalRequests">0</h3>
                    <p>إجمالي الطلبات</p>
                </div>
                <div class="stat-card">
                    <h3 id="pendingRequests">0</h3>
                    <p>طلبات معلقة</p>
                </div>
                <div class="stat-card">
                    <h3 id="approvedRequests">0</h3>
                    <p>طلبات مقبولة</p>
                </div>
                <div class="stat-card">
                    <h3 id="rejectedRequests">0</h3>
                    <p>طلبات مرفوضة</p>
                </div>
            </div>

            <!-- قائمة الطلبات -->
            <div class="requests-section">
                <h2 class="section-title">
                    <i class="fas fa-file-alt"></i>
                    طلبات التفعيل
                </h2>
                <div id="requestsList">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المدير
        const adminCredentials = {
            username: 'developer',
            password: 'dev123456',
            secretKey: 'FUTUREFUEL2024ADMIN'
        };

        let isLoggedIn = false;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تم تحميل لوحة التحكم');
            checkAuthStatus();
            setupEventListeners();
        });

        // فحص حالة المصادقة
        function checkAuthStatus() {
            const savedAuth = localStorage.getItem('adminAuth');
            if (savedAuth) {
                try {
                    const authData = JSON.parse(savedAuth);
                    const now = new Date().getTime();
                    
                    // فحص انتهاء الجلسة (4 ساعات)
                    if (now - authData.loginTime < 4 * 60 * 60 * 1000) {
                        isLoggedIn = true;
                        showDashboard();
                        return;
                    } else {
                        localStorage.removeItem('adminAuth');
                    }
                } catch (e) {
                    localStorage.removeItem('adminAuth');
                }
            }
            
            showLoginScreen();
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            const loginForm = document.getElementById('adminLoginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }
        }

        // معالجة تسجيل الدخول
        async function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('adminUsername').value.trim();
            const password = document.getElementById('adminPassword').value.trim();
            const secretKey = document.getElementById('adminSecretKey').value.trim();
            
            console.log('🔐 محاولة تسجيل الدخول:', { username, password: '***', secretKey: '***' });
            
            if (!username || !password || !secretKey) {
                showMessage('يرجى ملء جميع الحقول!', 'error');
                return;
            }
            
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحقق...';
            
            try {
                // محاكاة تأخير الشبكة
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (username === adminCredentials.username && 
                    password === adminCredentials.password && 
                    secretKey === adminCredentials.secretKey) {
                    
                    console.log('✅ نجح تسجيل الدخول');
                    
                    // حفظ بيانات المصادقة
                    const authData = {
                        username: username,
                        role: 'admin',
                        loginTime: new Date().getTime()
                    };
                    localStorage.setItem('adminAuth', JSON.stringify(authData));
                    
                    isLoggedIn = true;
                    showMessage('تم تسجيل الدخول بنجاح!', 'success');
                    
                    setTimeout(() => {
                        showDashboard();
                    }, 1000);
                    
                } else {
                    console.log('❌ فشل تسجيل الدخول');
                    throw new Error('بيانات المصادقة غير صحيحة');
                }
                
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showMessage(error.message || 'حدث خطأ في تسجيل الدخول', 'error');
            } finally {
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> دخول لوحة التحكم';
            }
        }

        // إظهار شاشة تسجيل الدخول
        function showLoginScreen() {
            document.getElementById('adminLoginOverlay').classList.remove('hidden');
            document.getElementById('dashboardContainer').classList.add('hidden');
        }

        // إظهار لوحة التحكم
        function showDashboard() {
            document.getElementById('adminLoginOverlay').classList.add('hidden');
            document.getElementById('dashboardContainer').classList.remove('hidden');
            
            // تحديث البيانات
            updateDashboardData();
        }

        // تحديث بيانات لوحة التحكم
        function updateDashboardData() {
            console.log('📊 تحديث بيانات لوحة التحكم');
            
            // تحميل طلبات التفعيل
            const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            
            // حساب الإحصائيات
            const stats = {
                total: activationRequests.length,
                pending: activationRequests.filter(req => req.status === 'pending').length,
                approved: activationRequests.filter(req => req.status === 'approved').length,
                rejected: activationRequests.filter(req => req.status === 'rejected').length
            };
            
            // تحديث الإحصائيات
            document.getElementById('totalRequests').textContent = stats.total;
            document.getElementById('pendingRequests').textContent = stats.pending;
            document.getElementById('approvedRequests').textContent = stats.approved;
            document.getElementById('rejectedRequests').textContent = stats.rejected;
            
            // عرض الطلبات
            displayRequests(activationRequests);
        }

        // عرض الطلبات
        function displayRequests(requests) {
            const container = document.getElementById('requestsList');
            
            if (requests.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #6c757d;">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 15px;"></i>
                        <p>لا توجد طلبات تفعيل حالياً</p>
                        <p style="font-size: 0.9rem;">يمكنك إرسال طلب تفعيل من نظام تسجيل الدخول</p>
                    </div>
                `;
                return;
            }
            
            container.innerHTML = requests.map(request => `
                <div class="request-item">
                    <div class="request-info">
                        <h4>${request.data.fullName}</h4>
                        <p>${request.data.email} • ${request.data.licenseType} • ${new Date(request.createdAt).toLocaleDateString('ar-DZ')}</p>
                    </div>
                    <span class="request-status status-${request.status}">
                        ${getStatusText(request.status)}
                    </span>
                </div>
            `).join('');
        }

        // الحصول على نص الحالة
        function getStatusText(status) {
            const statusMap = {
                'pending': 'معلق',
                'approved': 'مقبول',
                'rejected': 'مرفوض',
                'processing': 'قيد المعالجة'
            };
            return statusMap[status] || status;
        }

        // ملء البيانات تلقائياً
        function autoFillLogin() {
            document.getElementById('adminUsername').value = adminCredentials.username;
            document.getElementById('adminPassword').value = adminCredentials.password;
            document.getElementById('adminSecretKey').value = adminCredentials.secretKey;
            showMessage('تم ملء البيانات تلقائياً!', 'success');
        }

        // إظهار الرسائل
        function showMessage(text, type) {
            const messageDiv = document.getElementById('loginMessage');
            messageDiv.textContent = text;
            messageDiv.className = `message ${type}`;
            messageDiv.style.display = 'block';
            
            setTimeout(() => {
                messageDiv.style.display = 'none';
            }, 5000);
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('adminAuth');
                isLoggedIn = false;
                showLoginScreen();
                console.log('🚪 تم تسجيل الخروج');
            }
        }

        // تحديث البيانات كل 30 ثانية
        setInterval(() => {
            if (isLoggedIn) {
                updateDashboardData();
            }
        }, 30000);
    </script>
</body>
</html>
