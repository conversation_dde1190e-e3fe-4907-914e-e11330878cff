// بيانات التطبيق
let appData = {
    customers: [],
    gasCards: [],
    appointments: [],
    transmissions: [],
    notifications: [],
    inventory: [],
    certificates: [],
    suppliers: [],
    sales: [],
    purchases: [],
    debts: [],
    employees: [],
    users: [],
    workShifts: [],
    salaries: [],
    settings: {
        companyName: 'مؤسسة وقود المستقبل',
        companyAddress: '',
        companyPhone: '',
        autoBackup: false,
        notificationsEnabled: true,
        multiUser: false,
        currentUser: {
            id: 'admin',
            name: 'المدير العام',
            role: 'admin',
            permissions: ['all']
        }
    }
};

// تحميل البيانات من التخزين المحلي
function loadData() {
    const savedData = localStorage.getItem('gasShopData');
    if (savedData) {
        appData = { ...appData, ...JSON.parse(savedData) };
    }
    console.log('✅ تم تحميل البيانات');
}

// حفظ البيانات في التخزين المحلي
function saveData() {
    localStorage.setItem('gasShopData', JSON.stringify(appData));

    // النسخ الاحتياطي التلقائي
    if (appData.settings.autoBackup) {
        createAutoBackup();
    }

    console.log('💾 تم حفظ البيانات');
}

// إنشاء نسخة احتياطية تلقائية
function createAutoBackup() {
    try {
        const backupData = {
            ...appData,
            backupDate: new Date().toISOString(),
            version: '1.0'
        };

        const backupKey = `gasShopBackup_${new Date().toISOString().split('T')[0]}`;
        localStorage.setItem(backupKey, JSON.stringify(backupData));

        // الاحتفاظ بآخر 7 نسخ احتياطية فقط
        cleanOldBackups();

        console.log('🔄 تم إنشاء نسخة احتياطية تلقائية');
    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
    }
}

// تنظيف النسخ الاحتياطية القديمة
function cleanOldBackups() {
    const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('gasShopBackup_'));

    if (backupKeys.length > 7) {
        // ترتيب المفاتيح حسب التاريخ
        backupKeys.sort();

        // حذف النسخ الأقدم
        const keysToDelete = backupKeys.slice(0, backupKeys.length - 7);
        keysToDelete.forEach(key => {
            localStorage.removeItem(key);
        });

        console.log(`🗑️ تم حذف ${keysToDelete.length} نسخة احتياطية قديمة`);
    }
}

// استعادة نسخة احتياطية
function restoreBackup(backupKey) {
    try {
        const backupData = localStorage.getItem(backupKey);
        if (!backupData) {
            throw new Error('النسخة الاحتياطية غير موجودة');
        }

        const parsedData = JSON.parse(backupData);

        // إزالة معلومات النسخة الاحتياطية
        delete parsedData.backupDate;
        delete parsedData.version;

        appData = parsedData;
        saveData();
        updateAllTables();
        updateStats();

        showToast('تم استعادة النسخة الاحتياطية بنجاح', true);
        console.log('✅ تم استعادة النسخة الاحتياطية');

    } catch (error) {
        console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
        showToast('خطأ في استعادة النسخة الاحتياطية', false);
    }
}

// عرض قائمة النسخ الاحتياطية
function showBackupsList() {
    const backupKeys = Object.keys(localStorage).filter(key => key.startsWith('gasShopBackup_'));

    let backupsHTML = `
        <div class="backups-list">
            <h3>النسخ الاحتياطية المتاحة</h3>
            <div class="backups-grid">
    `;

    if (backupKeys.length === 0) {
        backupsHTML += '<div class="no-backups">لا توجد نسخ احتياطية</div>';
    } else {
        backupKeys.sort().reverse().forEach(key => {
            const backupDate = key.replace('gasShopBackup_', '');
            const backupData = JSON.parse(localStorage.getItem(key));

            backupsHTML += `
                <div class="backup-item">
                    <div class="backup-info">
                        <h4>نسخة احتياطية</h4>
                        <p>التاريخ: ${formatDate(backupDate)}</p>
                        <p>الوقت: ${new Date(backupData.backupDate).toLocaleTimeString('ar-DZ')}</p>
                        <p>الحجم: ${(JSON.stringify(backupData).length / 1024).toFixed(2)} KB</p>
                    </div>
                    <div class="backup-actions">
                        <button class="btn btn-sm btn-primary" onclick="restoreBackup('${key}')">
                            <i class="fas fa-undo"></i> استعادة
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="downloadBackup('${key}')">
                            <i class="fas fa-download"></i> تحميل
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteBackup('${key}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            `;
        });
    }

    backupsHTML += `
            </div>
            <div class="backup-actions-footer">
                <button class="btn btn-primary" onclick="createManualBackup()">
                    <i class="fas fa-save"></i> إنشاء نسخة احتياطية يدوية
                </button>
                <button class="btn btn-secondary" onclick="exportAllData()">
                    <i class="fas fa-file-export"></i> تصدير جميع البيانات
                </button>
            </div>
        </div>
    `;

    showModal('إدارة النسخ الاحتياطية', backupsHTML);
}

// إنشاء نسخة احتياطية يدوية
function createManualBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupKey = `gasShopBackup_manual_${timestamp}`;

    const backupData = {
        ...appData,
        backupDate: new Date().toISOString(),
        version: '1.0',
        type: 'manual'
    };

    localStorage.setItem(backupKey, JSON.stringify(backupData));
    showToast('تم إنشاء نسخة احتياطية يدوية', true);

    // تحديث قائمة النسخ الاحتياطية
    showBackupsList();
}

// تحميل نسخة احتياطية
function downloadBackup(backupKey) {
    const backupData = localStorage.getItem(backupKey);
    const blob = new Blob([backupData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${backupKey}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showToast('تم تحميل النسخة الاحتياطية', true);
}

// حذف نسخة احتياطية
function deleteBackup(backupKey) {
    if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
        localStorage.removeItem(backupKey);
        showToast('تم حذف النسخة الاحتياطية', true);
        showBackupsList();
    }
}

// إنشاء معرف فريد
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}

// تحديد فئة CSS للحالة
function getStatusClass(status) {
    switch (status) {
        case 'مكتملة':
        case 'مسدد':
        case 'نشط':
            return 'status-active';
        case 'معلقة':
        case 'مستحق':
            return 'status-pending';
        case 'ملغية':
        case 'متأخر':
            return 'status-cancelled';
        case 'منتهية الصلاحية':
        case 'غير نشط':
            return 'status-expired';
        default:
            return 'status-default';
    }
}

// تحديد فئة CSS لحالة الدين
function getDebtStatusClass(debt) {
    const daysRemaining = calculateDaysRemaining(debt.dueDate);

    if (debt.status === 'مسدد') return 'status-paid';
    if (daysRemaining < 0) return 'status-overdue';
    if (daysRemaining < 7) return 'status-due-soon';
    return 'status-pending';
}

// حساب الأيام المتبقية
function calculateDaysRemaining(dueDate) {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

// تنسيق المبلغ
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-DZ', {
        style: 'currency',
        currency: 'DZD',
        minimumFractionDigits: 0
    }).format(amount);
}

// حساب إجمالي المبلغ
function calculateTotal(items) {
    return items.reduce((total, item) => total + (item.quantity * item.price), 0);
}

// إظهار رسالة
function showToast(message, isSuccess = true) {
    const toast = document.getElementById('toast');
    const toastMessage = document.getElementById('toast-message');
    
    toastMessage.textContent = message;
    toast.className = `toast ${isSuccess ? '' : 'error'} show`;
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// إضافة إشعار
function addNotification(title, message, type = 'info') {
    const notification = {
        id: generateId(),
        title,
        message,
        type,
        timestamp: new Date().toISOString(),
        read: false
    };
    
    appData.notifications.unshift(notification);
    saveData();
    updateNotificationsBadge();
    
    console.log('🔔 إشعار جديد:', notification);
}

// تحديث شارة الإشعارات
function updateNotificationsBadge() {
    const unreadCount = appData.notifications.filter(n => !n.read).length;
    const badge = document.getElementById('notifications-count');
    
    if (unreadCount > 0) {
        badge.textContent = unreadCount;
        badge.classList.remove('hidden');
    } else {
        badge.classList.add('hidden');
    }
}

// تحديث الإحصائيات
function updateStats() {
    document.getElementById('total-customers').textContent = appData.customers.length;
    document.getElementById('total-cards').textContent = appData.gasCards.length;
    document.getElementById('total-appointments').textContent = appData.appointments.length;
    document.getElementById('total-transmissions').textContent = transmissionTableData.length; // استخدام بيانات جدول الإرسال الأصلي

    // تحديث إحصائيات المخزون
    const totalItems = appData.inventory.length;
    const lowStockItems = appData.inventory.filter(item => item.quantity <= item.minQuantity).length;

    const totalItemsElement = document.getElementById('total-items');
    const lowStockElement = document.getElementById('low-stock-items');

    if (totalItemsElement) totalItemsElement.textContent = totalItems;
    if (lowStockElement) lowStockElement.textContent = lowStockItems;
}

// تحديث جدول الزبائن
function updateCustomersTable() {
    const tbody = document.querySelector('#customers-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.customers.forEach(customer => {
        const row = tbody.insertRow();

        // حساب عدد السيارات للزبون
        const vehicleCount = appData.vehicles ? appData.vehicles.filter(v => v.customerId === customer.id).length : 0;

        // حساب آخر زيارة
        const customerAppointments = appData.appointments.filter(a => a.customerName === customer.name);
        const lastVisit = customerAppointments.length > 0 ?
            customerAppointments.sort((a, b) => new Date(b.date) - new Date(a.date))[0].date : null;

        // تحديد الحالة
        const status = customer.status || 'نشط';
        const statusClass = status === 'نشط' ? 'status-active' : 'status-inactive';

        row.innerHTML = `
            <td><input type="checkbox" class="customer-checkbox" value="${customer.id}"></td>
            <td>
                <div class="customer-info">
                    <strong>${customer.name}</strong>
                    ${customer.email ? `<br><small><i class="fas fa-envelope"></i> ${customer.email}</small>` : ''}
                </div>
            </td>
            <td>
                <div class="phone-info">
                    <span><i class="fas fa-phone"></i> ${customer.phone}</span>
                    ${customer.phone2 ? `<br><small><i class="fas fa-mobile-alt"></i> ${customer.phone2}</small>` : ''}
                </div>
            </td>
            <td>${customer.address || 'غير محدد'}</td>
            <td class="text-center">
                <span class="badge badge-info">${vehicleCount}</span>
            </td>
            <td>${lastVisit ? formatDate(lastVisit) : 'لا يوجد'}</td>
            <td><span class="${statusClass}">${status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" onclick="editCustomer('${customer.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCustomer('${customer.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="viewCustomerDetails('${customer.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="addServiceForCustomer('${customer.id}')" title="إضافة خدمة">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </td>
        `;
    });
}

// تحديث جدول بطاقات الغاز
function updateCardsTable() {
    const tbody = document.querySelector('#cards-table tbody');
    tbody.innerHTML = '';
    
    appData.gasCards.forEach(card => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${card.customerName}</td>
            <td>${card.vehicleNumber}</td>
            <td>${card.cardNumber}</td>
            <td>${formatDate(card.expiryDate)}</td>
            <td>
                <button class="btn btn-sm btn-secondary" onclick="editCard('${card.id}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteCard('${card.id}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
    });
}

// تحديث جدول المواعيد
function updateAppointmentsTable() {
    const tbody = document.querySelector('#appointments-table tbody');
    tbody.innerHTML = '';
    
    appData.appointments.forEach(appointment => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${formatDate(appointment.date)}</td>
            <td>${appointment.time}</td>
            <td>${appointment.customerName}</td>
            <td>${appointment.serviceType}</td>
            <td>
                <button class="btn btn-sm btn-secondary" onclick="editAppointment('${appointment.id}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteAppointment('${appointment.id}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
    });
}

// تحديث جدول الإرسال
function updateTransmissionTable() {
    const tbody = document.querySelector('#transmission-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.transmissions.forEach(transmission => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${transmission.type}</td>
            <td>${transmission.tankNumber}</td>
            <td>${transmission.carType || 'غير محدد'}</td>
            <td>${transmission.serialNumber || 'غير محدد'}</td>
            <td>${transmission.registrationNumber || 'غير محدد'}</td>
            <td>${transmission.ownerName}</td>
            <td>${formatDate(transmission.date)}</td>
            <td>
                <button class="btn btn-sm btn-secondary" onclick="editTransmission('${transmission.id}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteTransmission('${transmission.id}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </td>
        `;
    });
}

// تحديث جدول المخزون
function updateInventoryTable() {
    const tbody = document.querySelector('#inventory-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.inventory.forEach(item => {
        const row = tbody.insertRow();
        const isLowStock = item.quantity <= item.minQuantity;

        row.innerHTML = `
            <td>${item.code}</td>
            <td>${item.name}</td>
            <td>${item.category}</td>
            <td class="${isLowStock ? 'low-stock' : ''}">${item.quantity}</td>
            <td>${item.minQuantity}</td>
            <td>${item.purchasePrice} د.ج</td>
            <td>${item.salePrice} د.ج</td>
            <td>
                <button class="btn btn-sm btn-secondary" onclick="editInventoryItem('${item.id}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteInventoryItem('${item.id}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
                <button class="btn btn-sm btn-primary" onclick="adjustStock('${item.id}')">
                    <i class="fas fa-plus-minus"></i> تعديل المخزون
                </button>
            </td>
        `;
    });
}

// تحديث جدول الموردين
function updateSuppliersTable() {
    const tbody = document.querySelector('#suppliers-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.suppliers.forEach(supplier => {
        const row = tbody.insertRow();
        const statusClass = supplier.status === 'نشط' ? 'status-active' : 'status-inactive';

        row.innerHTML = `
            <td><input type="checkbox" class="supplier-checkbox" value="${supplier.id}"></td>
            <td>${supplier.name}</td>
            <td>${supplier.company || 'غير محدد'}</td>
            <td>${supplier.phone}</td>
            <td>${supplier.address || 'غير محدد'}</td>
            <td>${supplier.category}</td>
            <td>${supplier.balance || 0} د.ج</td>
            <td>${supplier.lastDeal ? formatDate(supplier.lastDeal) : 'لا يوجد'}</td>
            <td><span class="${statusClass}">${supplier.status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" onclick="editSupplier('${supplier.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSupplier('${supplier.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="viewSupplierDetails('${supplier.id}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                </div>
            </td>
        `;
    });
}

// تحديث جدول المبيعات
function updateSalesTable() {
    const tbody = document.querySelector('#sales-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.sales.forEach(sale => {
        const row = tbody.insertRow();
        const statusClass = getStatusClass(sale.status);

        row.innerHTML = `
            <td>${sale.invoiceNumber}</td>
            <td>${formatDate(sale.date)}</td>
            <td>${sale.customerName}</td>
            <td>${sale.items.length} صنف</td>
            <td>${sale.totalQuantity}</td>
            <td>${sale.totalAmount} د.ج</td>
            <td>${sale.paidAmount} د.ج</td>
            <td>${sale.remainingAmount} د.ج</td>
            <td><span class="${statusClass}">${sale.status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" onclick="editSale('${sale.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSale('${sale.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="printInvoice('${sale.id}')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </td>
        `;
    });
}

// تحديث جدول المشتريات
function updatePurchasesTable() {
    const tbody = document.querySelector('#purchases-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.purchases.forEach(purchase => {
        const row = tbody.insertRow();
        const statusClass = getStatusClass(purchase.status);

        row.innerHTML = `
            <td>${purchase.invoiceNumber}</td>
            <td>${formatDate(purchase.date)}</td>
            <td>${purchase.supplierName}</td>
            <td>${purchase.items.length} صنف</td>
            <td>${purchase.totalQuantity}</td>
            <td>${purchase.totalAmount} د.ج</td>
            <td>${purchase.paidAmount} د.ج</td>
            <td>${purchase.remainingAmount} د.ج</td>
            <td><span class="${statusClass}">${purchase.status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" onclick="editPurchase('${purchase.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deletePurchase('${purchase.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="printPurchaseInvoice('${purchase.id}')">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </td>
        `;
    });
}

// تحديث جدول الديون
function updateDebtsTable() {
    const tbody = document.querySelector('#debts-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.debts.forEach(debt => {
        const row = tbody.insertRow();
        const statusClass = getDebtStatusClass(debt);
        const daysRemaining = calculateDaysRemaining(debt.dueDate);

        row.innerHTML = `
            <td><span class="debt-type ${debt.type === 'لنا' ? 'debt-for-us' : 'debt-on-us'}">${debt.type}</span></td>
            <td>${debt.name}</td>
            <td>${debt.amount} د.ج</td>
            <td>${formatDate(debt.dueDate)}</td>
            <td class="${daysRemaining < 0 ? 'overdue' : daysRemaining < 7 ? 'due-soon' : ''}">${daysRemaining} يوم</td>
            <td><span class="${statusClass}">${debt.status}</span></td>
            <td>${debt.notes || 'لا توجد'}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" onclick="editDebt('${debt.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteDebt('${debt.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                    <button class="btn btn-sm btn-success" onclick="markDebtAsPaid('${debt.id}')">
                        <i class="fas fa-check"></i> تسديد
                    </button>
                </div>
            </td>
        `;
    });
}

// تحديث جميع الجداول
function updateAllTables() {
    updateCustomersTable();
    updateCardsTable();
    updateAppointmentsTable();
    updateTransmissionTable();
    updateInventoryTable();
    updateSuppliersTable();
    updateSalesTable();
    updatePurchasesTable();
    updateDebtsTable();
    updateEmployeesTable();
    updateStats();
}

// إعداد التنقل
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.section');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            const targetSection = link.getAttribute('data-section');
            
            // إزالة الفئة النشطة من جميع الروابط والأقسام
            navLinks.forEach(l => l.classList.remove('active'));
            sections.forEach(s => s.classList.remove('active'));
            
            // إضافة الفئة النشطة للرابط والقسم المحدد
            link.classList.add('active');
            document.getElementById(targetSection).classList.add('active');
            
            console.log(`📍 تم التنقل إلى: ${targetSection}`);
        });
    });
}

// إعداد الوضع المظلم
function setupDarkMode() {
    const darkModeBtn = document.getElementById('dark-mode-btn');
    const isDarkMode = localStorage.getItem('darkMode') === 'true';
    
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
        darkModeBtn.querySelector('i').className = 'fas fa-sun';
    }
    
    darkModeBtn.addEventListener('click', () => {
        document.body.classList.toggle('dark-mode');
        const isDark = document.body.classList.contains('dark-mode');
        
        localStorage.setItem('darkMode', isDark);
        darkModeBtn.querySelector('i').className = isDark ? 'fas fa-sun' : 'fas fa-moon';
        
        showToast(isDark ? 'تم تفعيل الوضع المظلم' : 'تم تفعيل الوضع العادي');
    });
}

// إعداد الإشعارات
function setupNotifications() {
    const notificationsBtn = document.getElementById('notifications-btn');
    const notificationsPanel = document.getElementById('notifications-panel');
    const closeNotifications = document.getElementById('close-notifications');
    
    notificationsBtn.addEventListener('click', () => {
        notificationsPanel.classList.toggle('active');
    });
    
    closeNotifications.addEventListener('click', () => {
        notificationsPanel.classList.remove('active');
    });
}

// إعداد النوافذ المنبثقة
function showModal(title, content) {
    const modalOverlay = document.getElementById('modal-overlay');
    const modalContent = document.getElementById('modal-content');
    
    modalContent.innerHTML = `
        <h2>${title}</h2>
        ${content}
    `;
    
    modalOverlay.classList.add('active');
}

function hideModal() {
    const modalOverlay = document.getElementById('modal-overlay');
    modalOverlay.classList.remove('active');
}

// إضافة بيانات تجريبية
function addSampleData() {
    // إضافة زبون تجريبي
    const sampleCustomer = {
        id: generateId(),
        name: 'أحمد محمد',
        phone: '0555123456',
        address: 'الجزائر العاصمة',
        createdAt: new Date().toISOString()
    };
    
    appData.customers.push(sampleCustomer);
    
    // إضافة بطاقة تجريبية
    const sampleCard = {
        id: generateId(),
        customerName: 'أحمد محمد',
        vehicleNumber: '123456-16',
        cardNumber: 'GAS-001',
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        createdAt: new Date().toISOString()
    };
    
    appData.gasCards.push(sampleCard);
    
    // إضافة موعد تجريبي
    const sampleAppointment = {
        id: generateId(),
        date: new Date().toISOString().split('T')[0],
        time: '10:00',
        customerName: 'أحمد محمد',
        serviceType: 'تركيب غاز',
        createdAt: new Date().toISOString()
    };
    
    appData.appointments.push(sampleAppointment);
    
    // إضافة عملية إرسال تجريبية
    const sampleTransmission = {
        id: generateId(),
        type: 'تركيب',
        tankNumber: 'T-001',
        ownerName: 'أحمد محمد',
        date: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString()
    };
    
    appData.transmissions.push(sampleTransmission);
    
    saveData();
    updateAllTables();
    
    // إضافة موظفين تجريبيين
    const sampleEmployees = [
        {
            id: generateId(),
            name: 'أحمد محمد علي',
            idNumber: '1234567890123',
            phone: '0555123456',
            email: '<EMAIL>',
            address: 'حي النصر، الجزائر العاصمة',
            birthDate: '1985-03-15',
            gender: 'ذكر',
            position: 'مدير',
            department: 'الإدارة',
            hireDate: '2020-01-15',
            salary: 80000,
            workHours: 8,
            status: 'نشط',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'فاطمة الزهراء بن علي',
            idNumber: '9876543210987',
            phone: '0666789012',
            email: '<EMAIL>',
            address: 'حي الرياض، وهران',
            birthDate: '1990-07-22',
            gender: 'أنثى',
            position: 'فني تركيب',
            department: 'الفنيين',
            hireDate: '2021-06-01',
            salary: 45000,
            workHours: 8,
            status: 'نشط',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'محمد الأمين قاسمي',
            idNumber: '5555666677778',
            phone: '0777345678',
            email: null,
            address: 'حي السلام، قسنطينة',
            birthDate: '1988-12-10',
            gender: 'ذكر',
            position: 'فني مراقبة',
            department: 'الفنيين',
            hireDate: '2019-09-15',
            salary: 42000,
            workHours: 8,
            status: 'نشط',
            createdAt: new Date().toISOString()
        }
    ];

    appData.employees = sampleEmployees;

    // إضافة حسابات مستخدمين تجريبية
    const sampleUsers = [
        {
            id: generateId(),
            employeeId: sampleEmployees[0].id,
            username: 'admin',
            password: '123456',
            role: 'admin',
            permissions: ['customers', 'cards', 'appointments', 'transmission', 'inventory', 'suppliers', 'sales', 'reports', 'settings'],
            isActive: true,
            lastLogin: null,
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            employeeId: sampleEmployees[1].id,
            username: 'fatima',
            password: '123456',
            role: 'employee',
            permissions: ['customers', 'cards', 'appointments', 'transmission'],
            isActive: true,
            lastLogin: null,
            createdAt: new Date().toISOString()
        }
    ];

    appData.users = sampleUsers;

    // إضافة سجلات حضور تجريبية
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const sampleAttendance = [
        {
            id: generateId(),
            employeeId: sampleEmployees[0].id,
            date: today.toISOString().split('T')[0],
            checkIn: '08:00',
            checkOut: null,
            notes: '',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            employeeId: sampleEmployees[1].id,
            date: today.toISOString().split('T')[0],
            checkIn: '08:15',
            checkOut: null,
            notes: '',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            employeeId: sampleEmployees[0].id,
            date: yesterday.toISOString().split('T')[0],
            checkIn: '07:45',
            checkOut: '17:30',
            notes: 'يوم عمل مكتمل',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            employeeId: sampleEmployees[1].id,
            date: yesterday.toISOString().split('T')[0],
            checkIn: '08:00',
            checkOut: '17:00',
            notes: '',
            createdAt: new Date().toISOString()
        }
    ];

    appData.attendance = sampleAttendance;

    addNotification('🎉 مرحباً', 'تم إضافة بيانات تجريبية للنظام', 'success');
    showToast('تم إضافة بيانات تجريبية بنجاح');
}

// ==================== دوال الإعدادات ====================

// حفظ إعدادات المؤسسة
function saveCompanySettings() {
    const companyName = document.getElementById('company-name').value;
    const companyAddress = document.getElementById('company-address').value;
    const companyPhone = document.getElementById('company-phone').value;

    appData.settings.companyName = companyName;
    appData.settings.companyAddress = companyAddress;
    appData.settings.companyPhone = companyPhone;

    saveData();
    showToast('تم حفظ إعدادات المؤسسة بنجاح');
}

// مسح جميع البيانات
function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        if (confirm('تأكيد أخير: سيتم مسح جميع الزبائن والبطاقات والمواعيد والمخزون!')) {
            appData = {
                customers: [],
                gasCards: [],
                appointments: [],
                transmissions: [],
                notifications: [],
                inventory: [],
                certificates: [],
                settings: {
                    companyName: 'مؤسسة وقود المستقبل',
                    companyAddress: '',
                    companyPhone: '',
                    autoBackup: false,
                    notificationsEnabled: true
                }
            };

            saveData();
            updateAllTables();

            addNotification('⚠️ تنبيه', 'تم مسح جميع البيانات', 'warning');
            showToast('تم مسح جميع البيانات', false);
        }
    }
}

// تحديث البيانات التجريبية
function updateSampleData() {
    // إضافة بيانات مخزون تجريبية
    const sampleInventory = [
        {
            id: generateId(),
            code: 'GAS-001',
            name: 'خزان غاز 40 لتر',
            category: 'قطع غيار الغاز',
            quantity: 15,
            minQuantity: 5,
            purchasePrice: 8000,
            salePrice: 12000,
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            code: 'TOOL-001',
            name: 'مفتاح ربط',
            category: 'أدوات',
            quantity: 3,
            minQuantity: 2,
            purchasePrice: 1500,
            salePrice: 2500,
            createdAt: new Date().toISOString()
        }
    ];

    appData.inventory.push(...sampleInventory);

    // إضافة موردين تجريبيين
    const sampleSuppliers = [
        {
            id: generateId(),
            name: 'شركة قطع الغيار المتقدمة',
            company: 'شركة قطع الغيار المتقدمة ش.م.م',
            phone: '0555987654',
            address: 'الجزائر العاصمة',
            category: 'قطع غيار',
            balance: 0,
            status: 'نشط',
            createdAt: new Date().toISOString(),
            lastDeal: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'مؤسسة الأدوات الصناعية',
            company: 'مؤسسة الأدوات الصناعية',
            phone: '0555123789',
            address: 'وهران',
            category: 'أدوات',
            balance: 15000,
            status: 'نشط',
            createdAt: new Date().toISOString(),
            lastDeal: null
        }
    ];

    appData.suppliers.push(...sampleSuppliers);

    // إضافة مبيعات تجريبية
    const sampleSales = [
        {
            id: generateId(),
            invoiceNumber: 'INV-001',
            date: new Date().toISOString().split('T')[0],
            customerName: 'أحمد محمد',
            items: [
                { name: 'خزان غاز 40 لتر', quantity: 1, price: 12000 }
            ],
            totalQuantity: 1,
            totalAmount: 12000,
            paidAmount: 12000,
            remainingAmount: 0,
            status: 'مكتملة',
            createdAt: new Date().toISOString()
        }
    ];

    appData.sales.push(...sampleSales);

    // إضافة مشتريات تجريبية
    const samplePurchases = [
        {
            id: generateId(),
            invoiceNumber: 'PUR-001',
            date: new Date().toISOString().split('T')[0],
            supplierName: 'شركة قطع الغيار المتقدمة',
            items: [
                { name: 'خزان غاز 40 لتر', quantity: 10, price: 8000 }
            ],
            totalQuantity: 10,
            totalAmount: 80000,
            paidAmount: 65000,
            remainingAmount: 15000,
            status: 'معلقة',
            createdAt: new Date().toISOString()
        }
    ];

    appData.purchases.push(...samplePurchases);

    // إضافة ديون تجريبية
    const sampleDebts = [
        {
            id: generateId(),
            type: 'علينا',
            name: 'شركة قطع الغيار المتقدمة',
            amount: 15000,
            dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // خلال 30 يوم
            status: 'مستحق',
            notes: 'باقي مبلغ فاتورة المشتريات',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            type: 'لنا',
            name: 'محمد علي',
            amount: 5000,
            dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // خلال 15 يوم
            status: 'مستحق',
            notes: 'باقي مبلغ خدمة التركيب',
            createdAt: new Date().toISOString()
        }
    ];

    appData.debts.push(...sampleDebts);

    // تحديث بيانات جدول الإرسال
    const sampleTransmission = appData.transmissions[0];
    if (sampleTransmission) {
        sampleTransmission.carType = 'سيارة خاصة';
        sampleTransmission.serialNumber = 'SN-123456';
        sampleTransmission.registrationNumber = '123456-16';
    }
}

// ربط الأزرار بالدوال
function setupButtons() {
    // أزرار الإضافة - مع فحص الوجود
    const addCustomerBtn = document.getElementById('add-customer-btn');
    if (addCustomerBtn) {
        addCustomerBtn.addEventListener('click', addCustomer);
    }

    const addCardBtn = document.getElementById('add-card-btn');
    if (addCardBtn) {
        addCardBtn.addEventListener('click', addCard);
    }

    const addAppointmentBtn = document.getElementById('add-appointment-btn');
    if (addAppointmentBtn) {
        addAppointmentBtn.addEventListener('click', addAppointment);
    }

    const addTransmissionBtn = document.getElementById('add-transmission-btn');
    if (addTransmissionBtn) {
        addTransmissionBtn.addEventListener('click', addTransmission);
    }

    // زر إضافة صنف للمخزون
    const addItemBtn = document.getElementById('add-item-btn');
    if (addItemBtn) {
        addItemBtn.addEventListener('click', addInventoryItem);
    }

    // أزرار الموردين
    const addSupplierBtn = document.getElementById('add-supplier-btn');
    if (addSupplierBtn) {
        addSupplierBtn.addEventListener('click', addSupplier);
    }

    // أزرار المبيعات
    const addSaleBtn = document.getElementById('add-sale-btn');
    if (addSaleBtn) {
        addSaleBtn.addEventListener('click', () => {
            showToast('قريباً: إضافة عملية بيع', true);
        });
    }

    // أزرار المشتريات
    const addPurchaseBtn = document.getElementById('add-purchase-btn');
    if (addPurchaseBtn) {
        addPurchaseBtn.addEventListener('click', () => {
            showToast('قريباً: إضافة عملية شراء', true);
        });
    }

    // أزرار الديون
    const addDebtBtn = document.getElementById('add-debt-btn');
    if (addDebtBtn) {
        addDebtBtn.addEventListener('click', () => {
            showToast('قريباً: إضافة دين', true);
        });
    }

    // أزرار جدول الإرسال
    const addTransmissionEntryBtn = document.getElementById('add-transmission-entry-btn');
    if (addTransmissionEntryBtn) {
        addTransmissionEntryBtn.addEventListener('click', showAddTransmissionModal);
    }

    const printTransmissionTableBtn = document.getElementById('print-transmission-table-btn');
    if (printTransmissionTableBtn) {
        printTransmissionTableBtn.addEventListener('click', printTransmissionTable);
    }

    const exportTransmissionPdfBtn = document.getElementById('export-transmission-pdf-btn');
    if (exportTransmissionPdfBtn) {
        exportTransmissionPdfBtn.addEventListener('click', exportTransmissionToPDF);
    }

    const clearTransmissionBtn = document.getElementById('clear-transmission-btn');
    if (clearTransmissionBtn) {
        clearTransmissionBtn.addEventListener('click', clearTransmissionTable);
    }

    // أزرار الطباعة والتصدير العامة
    const printTransmissionBtn = document.getElementById('print-transmission-btn');
    const exportTransmissionBtn = document.getElementById('export-transmission-btn');

    if (printTransmissionBtn) {
        printTransmissionBtn.addEventListener('click', () => {
            generateReport('transmission');
        });
    }

    if (exportTransmissionBtn) {
        exportTransmissionBtn.addEventListener('click', () => {
            generateReport('transmission');
        });
    }

    // إعدادات المؤسسة
    const companySettingsForm = document.getElementById('company-settings-form');
    if (companySettingsForm) {
        companySettingsForm.addEventListener('submit', (e) => {
            e.preventDefault();
            saveCompanySettings();
        });
    }

    // إعدادات النظام
    const autoBackupToggle = document.getElementById('auto-backup');
    const notificationsToggle = document.getElementById('notifications-enabled');

    if (autoBackupToggle) {
        autoBackupToggle.checked = appData.settings.autoBackup;
        autoBackupToggle.addEventListener('change', (e) => {
            appData.settings.autoBackup = e.target.checked;
            saveData();
            showToast(`تم ${e.target.checked ? 'تفعيل' : 'إلغاء'} النسخ الاحتياطي التلقائي`);
        });
    }

    if (notificationsToggle) {
        notificationsToggle.checked = appData.settings.notificationsEnabled;
        notificationsToggle.addEventListener('change', (e) => {
            appData.settings.notificationsEnabled = e.target.checked;
            saveData();
            showToast(`تم ${e.target.checked ? 'تفعيل' : 'إلغاء'} الإشعارات`);
        });
    }

    // إعداد نموذج جدول الإرسال
    const transmissionForm = document.getElementById('transmission-entry-form');
    if (transmissionForm) {
        transmissionForm.addEventListener('submit', (e) => {
            e.preventDefault();

            const entryData = {
                type: document.getElementById('transmission-entry-type').value,
                tankNumber: document.getElementById('transmission-entry-tank-number').value,
                carType: document.getElementById('transmission-entry-car-type').value,
                serialNumber: document.getElementById('transmission-entry-serial-number').value,
                registrationNumber: document.getElementById('transmission-entry-registration-number').value,
                ownerName: document.getElementById('transmission-entry-owner-name').value,
                operationDate: document.getElementById('transmission-entry-operation-date').value,
                createdAt: new Date().toISOString()
            };

            if (editingTransmissionIndex >= 0) {
                // تعديل عملية موجودة
                transmissionTableData[editingTransmissionIndex] = entryData;
                showToast('تم تحديث العملية بنجاح');
            } else {
                // إضافة عملية جديدة
                transmissionTableData.push(entryData);
                showToast('تم إضافة العملية بنجاح');

                // إضافة إشعار
                addNotification('📋 عملية جديدة', `تم إضافة عملية ${entryData.type} للمالك ${entryData.ownerName}`, 'success');
            }

            saveTransmissionData();
            updateTransmissionMainTable();
            updateStats(); // تحديث الإحصائيات العامة
            closeTransmissionModal();
        });
    }

    // إعداد البحث في جدول الإرسال
    const transmissionSearchInput = document.getElementById('transmission-search-input');
    if (transmissionSearchInput) {
        transmissionSearchInput.addEventListener('input', () => {
            updateTransmissionMainTable();
        });

        transmissionSearchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                updateTransmissionMainTable();
            }
        });
    }

    // إغلاق نوافذ منبثقة عند النقر خارجها
    document.getElementById('modal-overlay').addEventListener('click', (e) => {
        if (e.target.id === 'modal-overlay') {
            hideModal();
        }
    });

    const transmissionModal = document.getElementById('transmission-entry-modal');
    if (transmissionModal) {
        transmissionModal.addEventListener('click', (e) => {
            if (e.target.id === 'transmission-entry-modal') {
                closeTransmissionModal();
            }
        });
    }

    // تشخيص الأزرار
    const buttonIds = [
        'add-customer-btn', 'add-card-btn', 'add-appointment-btn', 'add-transmission-btn',
        'add-item-btn', 'add-supplier-btn', 'add-sale-btn', 'add-purchase-btn', 'add-debt-btn',
        'add-transmission-entry-btn', 'print-transmission-table-btn', 'export-transmission-pdf-btn'
    ];

    let foundButtons = 0;
    buttonIds.forEach(id => {
        const btn = document.getElementById(id);
        if (btn) {
            foundButtons++;
        } else {
            console.warn(`⚠️ لم يتم العثور على الزر: ${id}`);
        }
    });

    console.log(`✅ تم ربط ${foundButtons} من ${buttonIds.length} أزرار`);
}

// تهيئة التطبيق
function initApp() {
    console.log('🚀 بدء تحميل النظام...');

    loadData();
    setupNavigation();
    setupDarkMode();
    setupNotifications();

    // تأخير إعداد الأزرار للتأكد من تحميل العناصر
    setTimeout(() => {
        setupButtons();
    }, 100);

    updateAllTables();
    updateNotificationsBadge();

    // تهيئة جدول الإرسال الأصلي
    updateCurrentMonthYear();
    updateTransmissionMainTable();

    // إضافة بيانات تجريبية إذا كان النظام فارغ
    if (appData.customers.length === 0) {
        addSampleData();
        updateSampleData();
        addSampleTransmissionData();
    }

    console.log('✅ تم تحميل النظام بنجاح');
}

// إضافة بيانات تجريبية لجدول الإرسال
function addSampleTransmissionData() {
    if (transmissionTableData.length === 0) {
        const sampleTransmissionEntries = [
            {
                type: 'تركيب',
                tankNumber: 'T-001',
                carType: 'سيارة خاصة',
                serialNumber: 'SN-123456',
                registrationNumber: '123456-16',
                ownerName: 'أحمد محمد',
                operationDate: new Date().toISOString().split('T')[0],
                createdAt: new Date().toISOString()
            },
            {
                type: 'مراقبة',
                tankNumber: 'T-002',
                carType: 'سيارة تجارية',
                serialNumber: 'SN-789012',
                registrationNumber: '789012-16',
                ownerName: 'محمد علي',
                operationDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0], // أمس
                createdAt: new Date().toISOString()
            }
        ];

        transmissionTableData.push(...sampleTransmissionEntries);
        saveTransmissionData();
        updateTransmissionMainTable();

        console.log('✅ تم إضافة بيانات تجريبية لجدول الإرسال');
    }
}

// ==================== دوال إدارة الزبائن ====================

// إضافة زبون جديد
function addCustomer() {
    const customerForm = `
        <div class="comprehensive-form-container">
            <form id="customer-form" class="comprehensive-form">
                <div class="form-header">
                    <h3><i class="fas fa-user"></i> معلومات الزبون الأساسية</h3>
                </div>

                <!-- معلومات الزبون الشخصية -->
                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-name">الاسم الكامل: *</label>
                            <input type="text" id="customer-name" required placeholder="أدخل الاسم الكامل">
                        </div>
                        <div class="form-group">
                            <label for="customer-phone">رقم الهاتف: *</label>
                            <input type="tel" id="customer-phone" required placeholder="0555123456">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-phone2">هاتف إضافي:</label>
                            <input type="tel" id="customer-phone2" placeholder="رقم هاتف احتياطي">
                        </div>
                        <div class="form-group">
                            <label for="customer-email">البريد الإلكتروني:</label>
                            <input type="email" id="customer-email" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="customer-address">العنوان الكامل:</label>
                        <textarea id="customer-address" rows="2" placeholder="أدخل العنوان التفصيلي"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-id-number">رقم الهوية:</label>
                            <input type="text" id="customer-id-number" placeholder="رقم بطاقة الهوية">
                        </div>
                        <div class="form-group">
                            <label for="customer-birth-date">تاريخ الميلاد:</label>
                            <input type="date" id="customer-birth-date">
                        </div>
                    </div>
                </div>

                <div class="form-header">
                    <h3><i class="fas fa-car"></i> معلومات السيارة</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="vehicle-registration">رقم التسجيل: *</label>
                            <input type="text" id="vehicle-registration" required placeholder="123456-16">
                        </div>
                        <div class="form-group">
                            <label for="vehicle-brand">الماركة: *</label>
                            <select id="vehicle-brand" required>
                                <option value="">اختر الماركة</option>
                                <option value="رينو">رينو</option>
                                <option value="بيجو">بيجو</option>
                                <option value="سيتروين">سيتروين</option>
                                <option value="فولكس فاجن">فولكس فاجن</option>
                                <option value="فيات">فيات</option>
                                <option value="هيونداي">هيونداي</option>
                                <option value="كيا">كيا</option>
                                <option value="تويوتا">تويوتا</option>
                                <option value="نيسان">نيسان</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="vehicle-model">الموديل:</label>
                            <input type="text" id="vehicle-model" placeholder="مثال: كليو، 208، سي3">
                        </div>
                        <div class="form-group">
                            <label for="vehicle-year">سنة الصنع:</label>
                            <input type="number" id="vehicle-year" min="1990" max="2024" placeholder="2020">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="vehicle-color">اللون:</label>
                            <input type="text" id="vehicle-color" placeholder="أبيض، أسود، أزرق...">
                        </div>
                        <div class="form-group">
                            <label for="vehicle-type">نوع السيارة:</label>
                            <select id="vehicle-type">
                                <option value="">اختر النوع</option>
                                <option value="سيارة خاصة">سيارة خاصة</option>
                                <option value="سيارة تجارية">سيارة تجارية</option>
                                <option value="شاحنة صغيرة">شاحنة صغيرة</option>
                                <option value="حافلة صغيرة">حافلة صغيرة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="vehicle-chassis">رقم الهيكل (VIN):</label>
                        <input type="text" id="vehicle-chassis" placeholder="رقم هيكل السيارة">
                    </div>
                </div>

                <div class="form-header">
                    <h3><i class="fas fa-gas-pump"></i> معلومات قارورة الغاز</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="tank-serial">الرقم التسلسلي للخزان:</label>
                            <input type="text" id="tank-serial" placeholder="SN-123456">
                        </div>
                        <div class="form-group">
                            <label for="tank-capacity">سعة الخزان (لتر):</label>
                            <select id="tank-capacity">
                                <option value="">اختر السعة</option>
                                <option value="30">30 لتر</option>
                                <option value="40">40 لتر</option>
                                <option value="50">50 لتر</option>
                                <option value="60">60 لتر</option>
                                <option value="80">80 لتر</option>
                                <option value="100">100 لتر</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="tank-brand">ماركة الخزان:</label>
                            <input type="text" id="tank-brand" placeholder="ماركة الخزان">
                        </div>
                        <div class="form-group">
                            <label for="tank-manufacture-date">تاريخ الصنع:</label>
                            <input type="date" id="tank-manufacture-date">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="tank-test-date">تاريخ آخر فحص:</label>
                            <input type="date" id="tank-test-date">
                        </div>
                        <div class="form-group">
                            <label for="tank-next-test">تاريخ الفحص القادم:</label>
                            <input type="date" id="tank-next-test">
                        </div>
                    </div>
                </div>

                <div class="form-header">
                    <h3><i class="fas fa-cogs"></i> نوع الخدمة المطلوبة</h3>
                </div>

                <div class="form-section">
                    <div class="service-options">
                        <div class="service-option">
                            <input type="radio" id="service-installation" name="service-type" value="تركيب">
                            <label for="service-installation" class="service-label">
                                <i class="fas fa-tools"></i>
                                <span>تركيب نظام الغاز</span>
                                <small>تركيب نظام غاز جديد للسيارة</small>
                            </label>
                        </div>

                        <div class="service-option">
                            <input type="radio" id="service-monitoring" name="service-type" value="مراقبة">
                            <label for="service-monitoring" class="service-label">
                                <i class="fas fa-search"></i>
                                <span>مراقبة دورية</span>
                                <small>فحص ومراقبة النظام الموجود</small>
                            </label>
                        </div>

                        <div class="service-option">
                            <input type="radio" id="service-maintenance" name="service-type" value="صيانة">
                            <label for="service-maintenance" class="service-label">
                                <i class="fas fa-wrench"></i>
                                <span>صيانة وإصلاح</span>
                                <small>إصلاح أعطال أو صيانة النظام</small>
                            </label>
                        </div>

                        <div class="service-option">
                            <input type="radio" id="service-renewal" name="service-type" value="تجديد بطاقة">
                            <label for="service-renewal" class="service-label">
                                <i class="fas fa-id-card"></i>
                                <span>تجديد البطاقة</span>
                                <small>تجديد بطاقة الغاز المنتهية</small>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="service-notes">ملاحظات الخدمة:</label>
                        <textarea id="service-notes" rows="3" placeholder="أي ملاحظات إضافية حول الخدمة المطلوبة..."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="service-date">تاريخ الخدمة:</label>
                            <input type="date" id="service-date" value="${new Date().toISOString().split('T')[0]}">
                        </div>
                        <div class="form-group">
                            <label for="service-priority">الأولوية:</label>
                            <select id="service-priority">
                                <option value="عادية">عادية</option>
                                <option value="مهمة">مهمة</option>
                                <option value="عاجلة">عاجلة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-large">
                        <i class="fas fa-save"></i> حفظ جميع البيانات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;

    showModal('إضافة زبون جديد - معلومات شاملة', customerForm);

    // إعداد معالج النموذج
    setupComprehensiveCustomerForm();
}

// إعداد معالج النموذج الشامل للزبون
function setupComprehensiveCustomerForm() {
    const form = document.getElementById('customer-form');
    if (!form) return;

    form.addEventListener('submit', (e) => {
        e.preventDefault();

        // جمع بيانات الزبون
        const customerData = {
            id: generateId(),
            name: document.getElementById('customer-name').value,
            phone: document.getElementById('customer-phone').value,
            phone2: document.getElementById('customer-phone2').value || null,
            email: document.getElementById('customer-email').value || null,
            address: document.getElementById('customer-address').value || null,
            idNumber: document.getElementById('customer-id-number').value || null,
            birthDate: document.getElementById('customer-birth-date').value || null,
            createdAt: new Date().toISOString(),
            status: 'نشط'
        };

        // جمع بيانات السيارة
        const vehicleData = {
            id: generateId(),
            customerId: customerData.id,
            registrationNumber: document.getElementById('vehicle-registration').value,
            brand: document.getElementById('vehicle-brand').value,
            model: document.getElementById('vehicle-model').value || null,
            year: document.getElementById('vehicle-year').value || null,
            color: document.getElementById('vehicle-color').value || null,
            type: document.getElementById('vehicle-type').value || 'سيارة خاصة',
            chassisNumber: document.getElementById('vehicle-chassis').value || null,
            createdAt: new Date().toISOString()
        };

        // جمع بيانات الخزان
        const tankData = {
            id: generateId(),
            customerId: customerData.id,
            vehicleId: vehicleData.id,
            serialNumber: document.getElementById('tank-serial').value || null,
            capacity: document.getElementById('tank-capacity').value || null,
            brand: document.getElementById('tank-brand').value || null,
            manufactureDate: document.getElementById('tank-manufacture-date').value || null,
            lastTestDate: document.getElementById('tank-test-date').value || null,
            nextTestDate: document.getElementById('tank-next-test').value || null,
            createdAt: new Date().toISOString()
        };

        // جمع بيانات الخدمة
        const serviceType = document.querySelector('input[name="service-type"]:checked');
        const serviceData = {
            id: generateId(),
            customerId: customerData.id,
            vehicleId: vehicleData.id,
            type: serviceType ? serviceType.value : null,
            notes: document.getElementById('service-notes').value || null,
            date: document.getElementById('service-date').value,
            priority: document.getElementById('service-priority').value,
            status: 'مجدولة',
            createdAt: new Date().toISOString()
        };

        // إضافة البيانات للنظام
        appData.customers.push(customerData);

        // إضافة السيارة إلى قائمة منفصلة إذا لم تكن موجودة
        if (!appData.vehicles) appData.vehicles = [];
        appData.vehicles.push(vehicleData);

        // إضافة الخزان إلى قائمة منفصلة إذا لم تكن موجودة
        if (!appData.tanks) appData.tanks = [];
        appData.tanks.push(tankData);

        // إضافة الخدمة كموعد
        if (serviceData.type) {
            appData.appointments.push({
                id: serviceData.id,
                customerName: customerData.name,
                vehicleNumber: vehicleData.registrationNumber,
                serviceType: serviceData.type,
                date: serviceData.date,
                time: '09:00', // وقت افتراضي
                status: serviceData.status,
                priority: serviceData.priority,
                notes: serviceData.notes,
                createdAt: serviceData.createdAt
            });
        }

        // إنشاء بطاقة غاز إذا كان النوع تركيب أو تجديد
        if (serviceData.type === 'تركيب' || serviceData.type === 'تجديد بطاقة') {
            const cardNumber = generateCardNumber();
            const issueDate = new Date().toISOString().split('T')[0];
            const expiryDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // سنة من الآن

            appData.gasCards.push({
                id: generateId(),
                customerId: customerData.id,
                customerName: customerData.name,
                vehicleNumber: vehicleData.registrationNumber,
                cardNumber: cardNumber,
                issueDate: issueDate,
                expiryDate: expiryDate,
                status: 'نشطة',
                createdAt: new Date().toISOString()
            });

            // إضافة إشعار للبطاقة الجديدة
            addNotification('🆔 بطاقة جديدة', `تم إنشاء بطاقة غاز رقم ${cardNumber} للزبون ${customerData.name}`, 'success');
        }

        // إضافة عملية لجدول الإرسال إذا كان النوع تركيب أو مراقبة
        if (serviceData.type === 'تركيب' || serviceData.type === 'مراقبة') {
            transmissionTableData.push({
                type: serviceData.type,
                tankNumber: tankData.serialNumber || 'غير محدد',
                carType: vehicleData.type || 'سيارة خاصة',
                serialNumber: tankData.serialNumber || 'غير محدد',
                registrationNumber: vehicleData.registrationNumber,
                ownerName: customerData.name,
                operationDate: serviceData.date,
                createdAt: new Date().toISOString()
            });

            saveTransmissionData();
            updateTransmissionMainTable();
        }

        // حفظ البيانات وتحديث الواجهة
        saveData();
        updateAllTables();
        updateStats();
        hideModal();

        // إشعارات النجاح
        addNotification('👤 زبون جديد', `تم إضافة الزبون ${customerData.name} مع جميع البيانات`, 'success');

        if (serviceData.type) {
            addNotification('📅 موعد جديد', `تم جدولة ${serviceData.type} للزبون ${customerData.name} في ${formatDate(serviceData.date)}`, 'info');
        }

        showToast('تم حفظ جميع البيانات بنجاح!');

        // عرض ملخص البيانات المحفوظة
        showDataSummary(customerData, vehicleData, tankData, serviceData);
    });
}

// عرض ملخص البيانات المحفوظة
function showDataSummary(customer, vehicle, tank, service) {
    const summaryHTML = `
        <div class="data-summary">
            <h3><i class="fas fa-check-circle text-success"></i> تم حفظ البيانات بنجاح</h3>

            <div class="summary-section">
                <h4><i class="fas fa-user"></i> بيانات الزبون:</h4>
                <ul>
                    <li><strong>الاسم:</strong> ${customer.name}</li>
                    <li><strong>الهاتف:</strong> ${customer.phone}</li>
                    ${customer.email ? `<li><strong>البريد:</strong> ${customer.email}</li>` : ''}
                    ${customer.address ? `<li><strong>العنوان:</strong> ${customer.address}</li>` : ''}
                </ul>
            </div>

            <div class="summary-section">
                <h4><i class="fas fa-car"></i> بيانات السيارة:</h4>
                <ul>
                    <li><strong>رقم التسجيل:</strong> ${vehicle.registrationNumber}</li>
                    <li><strong>الماركة:</strong> ${vehicle.brand}</li>
                    ${vehicle.model ? `<li><strong>الموديل:</strong> ${vehicle.model}</li>` : ''}
                    ${vehicle.year ? `<li><strong>السنة:</strong> ${vehicle.year}</li>` : ''}
                </ul>
            </div>

            ${tank.serialNumber ? `
            <div class="summary-section">
                <h4><i class="fas fa-gas-pump"></i> بيانات الخزان:</h4>
                <ul>
                    <li><strong>الرقم التسلسلي:</strong> ${tank.serialNumber}</li>
                    ${tank.capacity ? `<li><strong>السعة:</strong> ${tank.capacity} لتر</li>` : ''}
                    ${tank.brand ? `<li><strong>الماركة:</strong> ${tank.brand}</li>` : ''}
                </ul>
            </div>
            ` : ''}

            ${service.type ? `
            <div class="summary-section">
                <h4><i class="fas fa-cogs"></i> الخدمة المطلوبة:</h4>
                <ul>
                    <li><strong>النوع:</strong> ${service.type}</li>
                    <li><strong>التاريخ:</strong> ${formatDate(service.date)}</li>
                    <li><strong>الأولوية:</strong> ${service.priority}</li>
                    ${service.notes ? `<li><strong>الملاحظات:</strong> ${service.notes}</li>` : ''}
                </ul>
            </div>
            ` : ''}

            <div class="summary-actions">
                <button class="btn btn-primary" onclick="hideModal()">
                    <i class="fas fa-check"></i> موافق
                </button>
                <button class="btn btn-secondary" onclick="printCustomerData('${customer.id}')">
                    <i class="fas fa-print"></i> طباعة البيانات
                </button>
            </div>
        </div>
    `;

    showModal('ملخص البيانات المحفوظة', summaryHTML);
}

// توليد رقم بطاقة
function generateCardNumber() {
    const prefix = 'GC';
    const year = new Date().getFullYear().toString().slice(-2);
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `${prefix}${year}${random}`;
}

// تعديل زبون
function editCustomer(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    const customerForm = `
        <form id="edit-customer-form">
            <div class="form-group">
                <label for="edit-customer-name">اسم الزبون:</label>
                <input type="text" id="edit-customer-name" value="${customer.name}" required>
            </div>
            <div class="form-group">
                <label for="edit-customer-phone">رقم الهاتف:</label>
                <input type="tel" id="edit-customer-phone" value="${customer.phone}" required>
            </div>
            <div class="form-group">
                <label for="edit-customer-address">العنوان:</label>
                <input type="text" id="edit-customer-address" value="${customer.address || ''}">
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('تعديل الزبون', customerForm);

    document.getElementById('edit-customer-form').addEventListener('submit', (e) => {
        e.preventDefault();

        customer.name = document.getElementById('edit-customer-name').value;
        customer.phone = document.getElementById('edit-customer-phone').value;
        customer.address = document.getElementById('edit-customer-address').value;
        customer.updatedAt = new Date().toISOString();

        saveData();
        updateCustomersTable();
        hideModal();

        showToast('تم تحديث بيانات الزبون بنجاح');
    });
}

// حذف زبون
function deleteCustomer(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    if (confirm(`هل أنت متأكد من حذف الزبون "${customer.name}"؟`)) {
        appData.customers = appData.customers.filter(c => c.id !== customerId);
        saveData();
        updateCustomersTable();
        updateStats();

        showToast('تم حذف الزبون بنجاح');
    }
}

// ==================== دوال إدارة بطاقات الغاز ====================

// إضافة بطاقة غاز
function addCard() {
    const cardForm = `
        <form id="card-form">
            <div class="form-group">
                <label for="card-customer">اسم الزبون:</label>
                <select id="card-customer" required>
                    <option value="">اختر الزبون</option>
                    ${appData.customers.map(c => `<option value="${c.name}">${c.name}</option>`).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="card-vehicle">رقم السيارة:</label>
                <input type="text" id="card-vehicle" required>
            </div>
            <div class="form-group">
                <label for="card-number">رقم البطاقة:</label>
                <input type="text" id="card-number" required>
            </div>
            <div class="form-group">
                <label for="card-expiry">تاريخ الانتهاء:</label>
                <input type="date" id="card-expiry" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">إضافة البطاقة</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('إضافة بطاقة غاز جديدة', cardForm);

    document.getElementById('card-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const card = {
            id: generateId(),
            customerName: document.getElementById('card-customer').value,
            vehicleNumber: document.getElementById('card-vehicle').value,
            cardNumber: document.getElementById('card-number').value,
            expiryDate: document.getElementById('card-expiry').value,
            issueDate: new Date().toISOString().split('T')[0],
            status: 'نشطة',
            createdAt: new Date().toISOString()
        };

        appData.gasCards.push(card);
        saveData();
        updateCardsTable();
        updateStats();
        hideModal();

        addNotification('🆔 بطاقة جديدة', `تم إضافة بطاقة رقم ${card.cardNumber}`, 'success');
        showToast('تم إضافة البطاقة بنجاح');
    });
}



// تعديل بطاقة غاز
function editCard(cardId) {
    const card = appData.gasCards.find(c => c.id === cardId);
    if (!card) return;

    const cardForm = `
        <form id="edit-card-form">
            <div class="form-group">
                <label for="edit-card-customer">اسم الزبون:</label>
                <select id="edit-card-customer" required>
                    ${appData.customers.map(c =>
                        `<option value="${c.name}" ${c.name === card.customerName ? 'selected' : ''}>${c.name}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="edit-card-vehicle">رقم السيارة:</label>
                <input type="text" id="edit-card-vehicle" value="${card.vehicleNumber}" required>
            </div>
            <div class="form-group">
                <label for="edit-card-number">رقم البطاقة:</label>
                <input type="text" id="edit-card-number" value="${card.cardNumber}" required>
            </div>
            <div class="form-group">
                <label for="edit-card-expiry">تاريخ الانتهاء:</label>
                <input type="date" id="edit-card-expiry" value="${card.expiryDate}" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('تعديل بطاقة الغاز', cardForm);

    document.getElementById('edit-card-form').addEventListener('submit', (e) => {
        e.preventDefault();

        card.customerName = document.getElementById('edit-card-customer').value;
        card.vehicleNumber = document.getElementById('edit-card-vehicle').value;
        card.cardNumber = document.getElementById('edit-card-number').value;
        card.expiryDate = document.getElementById('edit-card-expiry').value;
        card.updatedAt = new Date().toISOString();

        saveData();
        updateCardsTable();
        hideModal();

        showToast('تم تحديث بيانات البطاقة بنجاح');
    });
}

// حذف بطاقة غاز
function deleteCard(cardId) {
    const card = appData.gasCards.find(c => c.id === cardId);
    if (!card) return;

    if (confirm(`هل أنت متأكد من حذف البطاقة "${card.cardNumber}"؟`)) {
        appData.gasCards = appData.gasCards.filter(c => c.id !== cardId);
        saveData();
        updateCardsTable();
        updateStats();

        showToast('تم حذف البطاقة بنجاح');
    }
}

// ==================== دوال إدارة المواعيد ====================

// إضافة موعد
function addAppointment() {
    const appointmentForm = `
        <form id="appointment-form">
            <div class="form-group">
                <label for="appointment-date">التاريخ:</label>
                <input type="date" id="appointment-date" value="${new Date().toISOString().split('T')[0]}" required>
            </div>
            <div class="form-group">
                <label for="appointment-time">الوقت:</label>
                <input type="time" id="appointment-time" required>
            </div>
            <div class="form-group">
                <label for="appointment-customer">اسم الزبون:</label>
                <select id="appointment-customer" required>
                    <option value="">اختر الزبون</option>
                    ${appData.customers.map(c => `<option value="${c.name}">${c.name}</option>`).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="appointment-service">نوع الخدمة:</label>
                <select id="appointment-service" required>
                    <option value="">اختر نوع الخدمة</option>
                    <option value="تركيب غاز">تركيب غاز</option>
                    <option value="صيانة">صيانة</option>
                    <option value="مراقبة دورية">مراقبة دورية</option>
                    <option value="إصلاح">إصلاح</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">إضافة الموعد</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('إضافة موعد جديد', appointmentForm);

    document.getElementById('appointment-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const appointment = {
            id: generateId(),
            date: document.getElementById('appointment-date').value,
            time: document.getElementById('appointment-time').value,
            customerName: document.getElementById('appointment-customer').value,
            serviceType: document.getElementById('appointment-service').value,
            createdAt: new Date().toISOString()
        };

        appData.appointments.push(appointment);
        saveData();
        updateAppointmentsTable();
        updateStats();
        hideModal();

        addNotification('📅 موعد جديد', `تم إضافة موعد ${appointment.customerName}`, 'success');
        showToast('تم إضافة الموعد بنجاح');
    });
}

// تعديل موعد
function editAppointment(appointmentId) {
    const appointment = appData.appointments.find(a => a.id === appointmentId);
    if (!appointment) return;

    const appointmentForm = `
        <form id="edit-appointment-form">
            <div class="form-group">
                <label for="edit-appointment-date">التاريخ:</label>
                <input type="date" id="edit-appointment-date" value="${appointment.date}" required>
            </div>
            <div class="form-group">
                <label for="edit-appointment-time">الوقت:</label>
                <input type="time" id="edit-appointment-time" value="${appointment.time}" required>
            </div>
            <div class="form-group">
                <label for="edit-appointment-customer">اسم الزبون:</label>
                <select id="edit-appointment-customer" required>
                    ${appData.customers.map(c =>
                        `<option value="${c.name}" ${c.name === appointment.customerName ? 'selected' : ''}>${c.name}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="edit-appointment-service">نوع الخدمة:</label>
                <select id="edit-appointment-service" required>
                    <option value="تركيب غاز" ${appointment.serviceType === 'تركيب غاز' ? 'selected' : ''}>تركيب غاز</option>
                    <option value="صيانة" ${appointment.serviceType === 'صيانة' ? 'selected' : ''}>صيانة</option>
                    <option value="مراقبة دورية" ${appointment.serviceType === 'مراقبة دورية' ? 'selected' : ''}>مراقبة دورية</option>
                    <option value="إصلاح" ${appointment.serviceType === 'إصلاح' ? 'selected' : ''}>إصلاح</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('تعديل الموعد', appointmentForm);

    document.getElementById('edit-appointment-form').addEventListener('submit', (e) => {
        e.preventDefault();

        appointment.date = document.getElementById('edit-appointment-date').value;
        appointment.time = document.getElementById('edit-appointment-time').value;
        appointment.customerName = document.getElementById('edit-appointment-customer').value;
        appointment.serviceType = document.getElementById('edit-appointment-service').value;
        appointment.updatedAt = new Date().toISOString();

        saveData();
        updateAppointmentsTable();
        hideModal();

        showToast('تم تحديث بيانات الموعد بنجاح');
    });
}

// حذف موعد
function deleteAppointment(appointmentId) {
    const appointment = appData.appointments.find(a => a.id === appointmentId);
    if (!appointment) return;

    if (confirm(`هل أنت متأكد من حذف موعد "${appointment.customerName}"؟`)) {
        appData.appointments = appData.appointments.filter(a => a.id !== appointmentId);
        saveData();
        updateAppointmentsTable();
        updateStats();

        showToast('تم حذف الموعد بنجاح');
    }
}

// ==================== دوال إدارة جدول الإرسال ====================

// إضافة عملية إرسال
function addTransmission() {
    const transmissionForm = `
        <form id="transmission-form">
            <div class="form-group">
                <label for="transmission-type">نوع العملية:</label>
                <select id="transmission-type" required>
                    <option value="">اختر نوع العملية</option>
                    <option value="تركيب">تركيب</option>
                    <option value="مراقبة">مراقبة</option>
                </select>
            </div>
            <div class="form-group">
                <label for="transmission-tank">رقم الخزان:</label>
                <input type="text" id="transmission-tank" required>
            </div>
            <div class="form-group">
                <label for="transmission-owner">اسم المالك:</label>
                <select id="transmission-owner" required>
                    <option value="">اختر المالك</option>
                    ${appData.customers.map(c => `<option value="${c.name}">${c.name}</option>`).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="transmission-date">التاريخ:</label>
                <input type="date" id="transmission-date" value="${new Date().toISOString().split('T')[0]}" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">إضافة العملية</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('إضافة عملية إرسال جديدة', transmissionForm);

    document.getElementById('transmission-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const transmission = {
            id: generateId(),
            type: document.getElementById('transmission-type').value,
            tankNumber: document.getElementById('transmission-tank').value,
            ownerName: document.getElementById('transmission-owner').value,
            date: document.getElementById('transmission-date').value,
            createdAt: new Date().toISOString()
        };

        appData.transmissions.push(transmission);
        saveData();
        updateTransmissionTable();
        updateStats();
        hideModal();

        addNotification('📋 عملية جديدة', `تم إضافة عملية ${transmission.type}`, 'success');
        showToast('تم إضافة العملية بنجاح');
    });
}

// تعديل عملية إرسال
function editTransmission(transmissionId) {
    const transmission = appData.transmissions.find(t => t.id === transmissionId);
    if (!transmission) return;

    const transmissionForm = `
        <form id="edit-transmission-form">
            <div class="form-group">
                <label for="edit-transmission-type">نوع العملية:</label>
                <select id="edit-transmission-type" required>
                    <option value="تركيب" ${transmission.type === 'تركيب' ? 'selected' : ''}>تركيب</option>
                    <option value="مراقبة" ${transmission.type === 'مراقبة' ? 'selected' : ''}>مراقبة</option>
                </select>
            </div>
            <div class="form-group">
                <label for="edit-transmission-tank">رقم الخزان:</label>
                <input type="text" id="edit-transmission-tank" value="${transmission.tankNumber}" required>
            </div>
            <div class="form-group">
                <label for="edit-transmission-owner">اسم المالك:</label>
                <select id="edit-transmission-owner" required>
                    ${appData.customers.map(c =>
                        `<option value="${c.name}" ${c.name === transmission.ownerName ? 'selected' : ''}>${c.name}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="edit-transmission-date">التاريخ:</label>
                <input type="date" id="edit-transmission-date" value="${transmission.date}" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('تعديل عملية الإرسال', transmissionForm);

    document.getElementById('edit-transmission-form').addEventListener('submit', (e) => {
        e.preventDefault();

        transmission.type = document.getElementById('edit-transmission-type').value;
        transmission.tankNumber = document.getElementById('edit-transmission-tank').value;
        transmission.ownerName = document.getElementById('edit-transmission-owner').value;
        transmission.date = document.getElementById('edit-transmission-date').value;
        transmission.updatedAt = new Date().toISOString();

        saveData();
        updateTransmissionTable();
        hideModal();

        showToast('تم تحديث بيانات العملية بنجاح');
    });
}

// حذف عملية إرسال
function deleteTransmission(transmissionId) {
    const transmission = appData.transmissions.find(t => t.id === transmissionId);
    if (!transmission) return;

    if (confirm(`هل أنت متأكد من حذف العملية "${transmission.tankNumber}"؟`)) {
        appData.transmissions = appData.transmissions.filter(t => t.id !== transmissionId);
        saveData();
        updateTransmissionTable();
        updateStats();

        showToast('تم حذف العملية بنجاح');
    }
}

// ==================== دوال جدول الإرسال الأصلي ====================

// بيانات جدول الإرسال
let transmissionTableData = JSON.parse(localStorage.getItem('transmissionTableData')) || [];
let editingTransmissionIndex = -1;

// تحديث الشهر والسنة الحالية
function updateCurrentMonthYear() {
    const now = new Date();
    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    const currentMonthYear = `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
    const element = document.getElementById('current-month-year');
    if (element) {
        element.textContent = currentMonthYear;
    }
}

// حفظ بيانات جدول الإرسال
function saveTransmissionData() {
    localStorage.setItem('transmissionTableData', JSON.stringify(transmissionTableData));
}

// تحديث جدول الإرسال الأصلي
function updateTransmissionMainTable() {
    const tableBody = document.getElementById('transmission-table-body');
    const emptyState = document.getElementById('transmission-empty-state');

    if (!tableBody) return;

    // تطبيق الفلاتر
    let filteredData = [...transmissionTableData];

    // فلتر النوع
    const typeFilter = document.getElementById('transmission-type-filter');
    if (typeFilter && typeFilter.value !== 'all') {
        filteredData = filteredData.filter(entry => entry.type === typeFilter.value);
    }

    // فلتر الشهر
    const monthFilter = document.getElementById('transmission-month-filter');
    if (monthFilter && monthFilter.value !== 'all') {
        const now = new Date();
        const currentMonth = now.getMonth();
        const currentYear = now.getFullYear();

        if (monthFilter.value === 'current') {
            filteredData = filteredData.filter(entry => {
                const entryDate = new Date(entry.operationDate);
                return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
            });
        } else if (monthFilter.value === 'last') {
            const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
            const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
            filteredData = filteredData.filter(entry => {
                const entryDate = new Date(entry.operationDate);
                return entryDate.getMonth() === lastMonth && entryDate.getFullYear() === lastMonthYear;
            });
        }
    }

    // فلتر البحث
    const searchInput = document.getElementById('transmission-search-input');
    if (searchInput && searchInput.value) {
        const searchTerm = searchInput.value.toLowerCase();
        filteredData = filteredData.filter(entry =>
            entry.ownerName.toLowerCase().includes(searchTerm) ||
            entry.registrationNumber.toLowerCase().includes(searchTerm) ||
            entry.tankNumber.toLowerCase().includes(searchTerm) ||
            entry.carType.toLowerCase().includes(searchTerm)
        );
    }

    if (filteredData.length === 0) {
        tableBody.innerHTML = '';
        if (emptyState) emptyState.style.display = 'block';
    } else {
        if (emptyState) emptyState.style.display = 'none';

        // ترتيب البيانات حسب تاريخ العملية (الأحدث أولاً)
        filteredData.sort((a, b) => new Date(b.operationDate) - new Date(a.operationDate));

        tableBody.innerHTML = filteredData.map((entry, index) => `
            <tr>
                <td>${entry.type}</td>
                <td>${entry.tankNumber}</td>
                <td>${entry.carType}</td>
                <td>${entry.serialNumber}</td>
                <td>${entry.registrationNumber}</td>
                <td>${entry.ownerName}</td>
                <td>${index + 1}</td>
                <td>${formatDate(entry.operationDate)}</td>
                <td class="no-print">
                    <div class="action-buttons">
                        <button type="button" class="btn btn-sm btn-secondary" onclick="editTransmissionEntry(${transmissionTableData.indexOf(entry)})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger" onclick="deleteTransmissionEntry(${transmissionTableData.indexOf(entry)})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    updateTransmissionSummary();
}

// تحديث ملخص جدول الإرسال
function updateTransmissionSummary() {
    const totalCount = transmissionTableData.length;
    const installationCount = transmissionTableData.filter(entry => entry.type === 'تركيب').length;
    const monitoringCount = transmissionTableData.filter(entry => entry.type === 'مراقبة').length;

    // عمليات الشهر الحالي
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const currentMonthCount = transmissionTableData.filter(entry => {
        const entryDate = new Date(entry.operationDate);
        return entryDate.getMonth() === currentMonth && entryDate.getFullYear() === currentYear;
    }).length;

    const totalElement = document.getElementById('transmission-total-count');
    const installationElement = document.getElementById('transmission-installation-count');
    const monitoringElement = document.getElementById('transmission-monitoring-count');
    const currentMonthElement = document.getElementById('transmission-current-month-count');

    if (totalElement) totalElement.textContent = totalCount;
    if (installationElement) installationElement.textContent = installationCount;
    if (monitoringElement) monitoringElement.textContent = monitoringCount;
    if (currentMonthElement) currentMonthElement.textContent = currentMonthCount;
}

// عرض نافذة إضافة عملية جدول الإرسال
function showAddTransmissionModal() {
    editingTransmissionIndex = -1;
    const modal = document.getElementById('transmission-entry-modal');
    const title = document.getElementById('transmission-modal-title');
    const form = document.getElementById('transmission-entry-form');
    const dateInput = document.getElementById('transmission-entry-operation-date');

    if (title) title.textContent = 'إضافة عملية جديدة';
    if (form) form.reset();
    if (dateInput) dateInput.value = new Date().toISOString().split('T')[0];
    if (modal) modal.style.display = 'flex';
}

// تعديل عملية جدول الإرسال
function editTransmissionEntry(index) {
    editingTransmissionIndex = index;
    const entry = transmissionTableData[index];
    const modal = document.getElementById('transmission-entry-modal');
    const title = document.getElementById('transmission-modal-title');

    if (title) title.textContent = 'تعديل العملية';

    document.getElementById('transmission-entry-type').value = entry.type;
    document.getElementById('transmission-entry-tank-number').value = entry.tankNumber;
    document.getElementById('transmission-entry-car-type').value = entry.carType;
    document.getElementById('transmission-entry-serial-number').value = entry.serialNumber;
    document.getElementById('transmission-entry-registration-number').value = entry.registrationNumber;
    document.getElementById('transmission-entry-owner-name').value = entry.ownerName;
    document.getElementById('transmission-entry-operation-date').value = entry.operationDate;

    if (modal) modal.style.display = 'flex';
}

// حذف عملية جدول الإرسال
function deleteTransmissionEntry(index) {
    if (confirm('هل أنت متأكد من حذف هذه العملية؟')) {
        transmissionTableData.splice(index, 1);
        saveTransmissionData();
        updateTransmissionMainTable();
        updateStats(); // تحديث الإحصائيات العامة
        showToast('تم حذف العملية بنجاح');
    }
}

// إغلاق نافذة جدول الإرسال
function closeTransmissionModal() {
    const modal = document.getElementById('transmission-entry-modal');
    if (modal) modal.style.display = 'none';
}

// البحث في جدول الإرسال
function searchTransmissionTable() {
    updateTransmissionMainTable();
}

// تصفية جدول الإرسال
function filterTransmissionTable() {
    updateTransmissionMainTable();
}

// طباعة جدول الإرسال
function printTransmissionTable() {
    window.print();
}

// تصدير جدول الإرسال إلى PDF
function exportTransmissionToPDF() {
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4');

        // إعداد الخط
        doc.setFont('Arial', 'normal');
        doc.setFontSize(16);

        // العنوان
        doc.text('الجمهورية الجزائرية الديمقراطية الشعبية', 105, 20, { align: 'center' });
        doc.text('مركز وقود المستقبل - عزيري عبد الله اسحاق', 105, 30, { align: 'center' });
        doc.setFontSize(12);
        doc.text('رقم: 463/2019', 105, 40, { align: 'center' });
        doc.text('إلى السيد: مدير الصناعة و المناجم لولاية المدية', 105, 50, { align: 'center' });
        doc.setFontSize(14);
        doc.text('جدول إرسال', 105, 65, { align: 'center' });

        // تاريخ الشهر
        const now = new Date();
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        const currentMonthYear = `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
        doc.setFontSize(10);
        doc.text(`تجدون طي هذه المراسلة الوثائق الخاصة بالسيارات المجهزة بغاز البترول المميع شهر ${currentMonthYear}.`, 105, 75, { align: 'center' });

        // إعداد الجدول
        const tableData = transmissionTableData.map((entry, index) => [
            entry.type,
            entry.tankNumber,
            entry.carType,
            entry.serialNumber,
            entry.registrationNumber,
            entry.ownerName,
            (index + 1).toString(),
            formatDate(entry.operationDate)
        ]);

        const headers = [
            'تركيب أو مراقبة',
            'رقم خزان الغاز',
            'الصنف',
            'الرقم التسلسلي',
            'رقم التسجيل',
            'الإسم و اللقب',
            'الرقم',
            'تاريخ العملية'
        ];

        // رسم الجدول
        doc.autoTable({
            head: [headers],
            body: tableData,
            startY: 85,
            styles: {
                fontSize: 8,
                cellPadding: 2,
                halign: 'center'
            },
            headStyles: {
                fillColor: [30, 58, 138],
                textColor: 255,
                fontStyle: 'bold'
            },
            alternateRowStyles: {
                fillColor: [248, 250, 252]
            },
            margin: { top: 85, right: 10, bottom: 20, left: 10 }
        });

        // حفظ الملف
        const filename = `جدول_الإرسال_${currentMonthYear.replace(' ', '_')}.pdf`;
        doc.save(filename);

        showToast('تم تصدير جدول الإرسال إلى PDF بنجاح');
    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        showToast('حدث خطأ في تصدير PDF', false);
    }
}

// مسح جدول الإرسال
function clearTransmissionTable() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        if (confirm('تأكيد أخير: سيتم حذف جميع العمليات نهائياً. هل تريد المتابعة؟')) {
            transmissionTableData = [];
            saveTransmissionData();
            updateTransmissionMainTable();
            updateStats(); // تحديث الإحصائيات العامة
            showToast('تم مسح جدول الإرسال بنجاح');
        }
    }
}

// ==================== دوال إدارة المخزون ====================

// إضافة صنف جديد
function addInventoryItem() {
    const itemForm = `
        <form id="item-form">
            <div class="form-group">
                <label for="item-code">كود الصنف:</label>
                <input type="text" id="item-code" required>
            </div>
            <div class="form-group">
                <label for="item-name">اسم الصنف:</label>
                <input type="text" id="item-name" required>
            </div>
            <div class="form-group">
                <label for="item-category">الفئة:</label>
                <select id="item-category" required>
                    <option value="">اختر الفئة</option>
                    <option value="قطع غيار الغاز">قطع غيار الغاز</option>
                    <option value="أدوات">أدوات</option>
                    <option value="إكسسوارات">إكسسوارات</option>
                    <option value="مواد استهلاكية">مواد استهلاكية</option>
                </select>
            </div>
            <div class="form-group">
                <label for="item-quantity">الكمية الحالية:</label>
                <input type="number" id="item-quantity" min="0" required>
            </div>
            <div class="form-group">
                <label for="item-min-quantity">الحد الأدنى:</label>
                <input type="number" id="item-min-quantity" min="1" required>
            </div>
            <div class="form-group">
                <label for="item-purchase-price">سعر الشراء:</label>
                <input type="number" id="item-purchase-price" min="0" step="0.01" required>
            </div>
            <div class="form-group">
                <label for="item-sale-price">سعر البيع:</label>
                <input type="number" id="item-sale-price" min="0" step="0.01" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">إضافة الصنف</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('إضافة صنف جديد', itemForm);

    document.getElementById('item-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const item = {
            id: generateId(),
            code: document.getElementById('item-code').value,
            name: document.getElementById('item-name').value,
            category: document.getElementById('item-category').value,
            quantity: parseInt(document.getElementById('item-quantity').value),
            minQuantity: parseInt(document.getElementById('item-min-quantity').value),
            purchasePrice: parseFloat(document.getElementById('item-purchase-price').value),
            salePrice: parseFloat(document.getElementById('item-sale-price').value),
            createdAt: new Date().toISOString()
        };

        appData.inventory.push(item);
        saveData();
        updateInventoryTable();
        updateStats();
        hideModal();

        addNotification('📦 صنف جديد', `تم إضافة الصنف ${item.name}`, 'success');
        showToast('تم إضافة الصنف بنجاح');
    });
}

// تعديل صنف
function editInventoryItem(itemId) {
    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) return;

    const itemForm = `
        <form id="edit-item-form">
            <div class="form-group">
                <label for="edit-item-code">كود الصنف:</label>
                <input type="text" id="edit-item-code" value="${item.code}" required>
            </div>
            <div class="form-group">
                <label for="edit-item-name">اسم الصنف:</label>
                <input type="text" id="edit-item-name" value="${item.name}" required>
            </div>
            <div class="form-group">
                <label for="edit-item-category">الفئة:</label>
                <select id="edit-item-category" required>
                    <option value="قطع غيار الغاز" ${item.category === 'قطع غيار الغاز' ? 'selected' : ''}>قطع غيار الغاز</option>
                    <option value="أدوات" ${item.category === 'أدوات' ? 'selected' : ''}>أدوات</option>
                    <option value="إكسسوارات" ${item.category === 'إكسسوارات' ? 'selected' : ''}>إكسسوارات</option>
                    <option value="مواد استهلاكية" ${item.category === 'مواد استهلاكية' ? 'selected' : ''}>مواد استهلاكية</option>
                </select>
            </div>
            <div class="form-group">
                <label for="edit-item-min-quantity">الحد الأدنى:</label>
                <input type="number" id="edit-item-min-quantity" value="${item.minQuantity}" min="1" required>
            </div>
            <div class="form-group">
                <label for="edit-item-purchase-price">سعر الشراء:</label>
                <input type="number" id="edit-item-purchase-price" value="${item.purchasePrice}" min="0" step="0.01" required>
            </div>
            <div class="form-group">
                <label for="edit-item-sale-price">سعر البيع:</label>
                <input type="number" id="edit-item-sale-price" value="${item.salePrice}" min="0" step="0.01" required>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('تعديل الصنف', itemForm);

    document.getElementById('edit-item-form').addEventListener('submit', (e) => {
        e.preventDefault();

        item.code = document.getElementById('edit-item-code').value;
        item.name = document.getElementById('edit-item-name').value;
        item.category = document.getElementById('edit-item-category').value;
        item.minQuantity = parseInt(document.getElementById('edit-item-min-quantity').value);
        item.purchasePrice = parseFloat(document.getElementById('edit-item-purchase-price').value);
        item.salePrice = parseFloat(document.getElementById('edit-item-sale-price').value);
        item.updatedAt = new Date().toISOString();

        saveData();
        updateInventoryTable();
        updateStats();
        hideModal();

        showToast('تم تحديث بيانات الصنف بنجاح');
    });
}

// حذف صنف
function deleteInventoryItem(itemId) {
    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) return;

    if (confirm(`هل أنت متأكد من حذف الصنف "${item.name}"؟`)) {
        appData.inventory = appData.inventory.filter(i => i.id !== itemId);
        saveData();
        updateInventoryTable();
        updateStats();

        showToast('تم حذف الصنف بنجاح');
    }
}

// تعديل المخزون
function adjustStock(itemId) {
    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) return;

    const adjustForm = `
        <form id="adjust-stock-form">
            <div class="form-group">
                <label>الصنف: ${item.name}</label>
                <p>الكمية الحالية: <strong>${item.quantity}</strong></p>
            </div>
            <div class="form-group">
                <label for="adjust-type">نوع التعديل:</label>
                <select id="adjust-type" required>
                    <option value="add">إضافة</option>
                    <option value="subtract">خصم</option>
                    <option value="set">تحديد الكمية</option>
                </select>
            </div>
            <div class="form-group">
                <label for="adjust-quantity">الكمية:</label>
                <input type="number" id="adjust-quantity" min="1" required>
            </div>
            <div class="form-group">
                <label for="adjust-reason">السبب:</label>
                <select id="adjust-reason" required>
                    <option value="">اختر السبب</option>
                    <option value="شراء جديد">شراء جديد</option>
                    <option value="بيع">بيع</option>
                    <option value="تلف">تلف</option>
                    <option value="فقدان">فقدان</option>
                    <option value="إرجاع">إرجاع</option>
                    <option value="تصحيح">تصحيح</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">تطبيق التعديل</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('تعديل المخزون', adjustForm);

    document.getElementById('adjust-stock-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const adjustType = document.getElementById('adjust-type').value;
        const adjustQuantity = parseInt(document.getElementById('adjust-quantity').value);
        const adjustReason = document.getElementById('adjust-reason').value;

        let newQuantity = item.quantity;

        switch (adjustType) {
            case 'add':
                newQuantity += adjustQuantity;
                break;
            case 'subtract':
                newQuantity = Math.max(0, newQuantity - adjustQuantity);
                break;
            case 'set':
                newQuantity = adjustQuantity;
                break;
        }

        item.quantity = newQuantity;
        item.lastAdjustment = {
            type: adjustType,
            quantity: adjustQuantity,
            reason: adjustReason,
            date: new Date().toISOString()
        };

        saveData();
        updateInventoryTable();
        updateStats();
        hideModal();

        addNotification('📦 تعديل مخزون', `تم تعديل مخزون ${item.name}`, 'info');
        showToast('تم تعديل المخزون بنجاح');
    });
}

// ==================== دوال الشهادات ====================

// إنشاء شهادة
function createCertificate(type) {
    const certificateForm = `
        <form id="certificate-form">
            <div class="form-group">
                <label for="cert-customer">اسم الزبون:</label>
                <select id="cert-customer" required>
                    <option value="">اختر الزبون</option>
                    ${appData.customers.map(c => `<option value="${c.name}">${c.name}</option>`).join('')}
                </select>
            </div>
            <div class="form-group">
                <label for="cert-vehicle">رقم السيارة:</label>
                <input type="text" id="cert-vehicle" required>
            </div>
            <div class="form-group">
                <label for="cert-date">تاريخ الشهادة:</label>
                <input type="date" id="cert-date" value="${new Date().toISOString().split('T')[0]}" required>
            </div>
            <div class="form-group">
                <label for="cert-notes">ملاحظات:</label>
                <textarea id="cert-notes" rows="3"></textarea>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">إنشاء الشهادة</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    const title = type === 'installation' ? 'شهادة تركيب' : 'شهادة مراقبة';
    showModal(`إنشاء ${title}`, certificateForm);

    document.getElementById('certificate-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const certificate = {
            id: generateId(),
            type: type,
            customerName: document.getElementById('cert-customer').value,
            vehicleNumber: document.getElementById('cert-vehicle').value,
            date: document.getElementById('cert-date').value,
            notes: document.getElementById('cert-notes').value,
            createdAt: new Date().toISOString()
        };

        appData.certificates.push(certificate);
        saveData();
        hideModal();

        // إنشاء الشهادة وطباعتها
        generateCertificatePDF(certificate);

        addNotification('📜 شهادة جديدة', `تم إنشاء ${title} للزبون ${certificate.customerName}`, 'success');
        showToast('تم إنشاء الشهادة بنجاح');
    });
}

// إنشاء PDF للشهادة
function generateCertificatePDF(certificate) {
    const printWindow = window.open('', '_blank');
    const certificateHTML = generateCertificateHTML(certificate);

    printWindow.document.write(certificateHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}

// إنشاء HTML للشهادة
function generateCertificateHTML(certificate) {
    const title = certificate.type === 'installation' ? 'شهادة تركيب نظام الغاز' : 'شهادة مراقبة دورية';
    const currentDate = new Date().toLocaleDateString('ar-DZ');

    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>${title}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; direction: rtl; }
                .header { text-align: center; margin-bottom: 40px; border-bottom: 3px solid #3498db; padding-bottom: 20px; }
                .header h1 { color: #2c3e50; margin-bottom: 10px; }
                .header h2 { color: #3498db; margin-bottom: 5px; }
                .content { margin: 30px 0; line-height: 2; }
                .info-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .info-table td { padding: 10px; border: 1px solid #ddd; }
                .info-table .label { background: #f8f9fa; font-weight: bold; width: 30%; }
                .footer { margin-top: 50px; text-align: center; }
                .signature { margin-top: 60px; display: flex; justify-content: space-between; }
                .signature div { text-align: center; }
                @media print { body { margin: 20px; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>مؤسسة وقود المستقبل</h1>
                <h2>${title}</h2>
                <p>رقم الشهادة: ${certificate.id}</p>
            </div>

            <div class="content">
                <p>نشهد نحن مؤسسة وقود المستقبل بأنه تم ${certificate.type === 'installation' ? 'تركيب نظام الغاز' : 'إجراء المراقبة الدورية'} للمركبة المذكورة أدناه وفقاً للمعايير والمواصفات المعتمدة.</p>

                <table class="info-table">
                    <tr>
                        <td class="label">اسم المالك:</td>
                        <td>${certificate.customerName}</td>
                    </tr>
                    <tr>
                        <td class="label">رقم السيارة:</td>
                        <td>${certificate.vehicleNumber}</td>
                    </tr>
                    <tr>
                        <td class="label">تاريخ ${certificate.type === 'installation' ? 'التركيب' : 'المراقبة'}:</td>
                        <td>${formatDate(certificate.date)}</td>
                    </tr>
                    <tr>
                        <td class="label">ملاحظات:</td>
                        <td>${certificate.notes || 'لا توجد ملاحظات'}</td>
                    </tr>
                </table>

                <p>وقد تم العمل وفقاً للمعايير الفنية المعتمدة وبإشراف فنيين مختصين.</p>
            </div>

            <div class="signature">
                <div>
                    <p>التوقيع</p>
                    <p>_________________</p>
                    <p>الفني المختص</p>
                </div>
                <div>
                    <p>الختم</p>
                    <p>_________________</p>
                    <p>مؤسسة وقود المستقبل</p>
                </div>
            </div>

            <div class="footer">
                <p>تاريخ الإصدار: ${currentDate}</p>
                <p>هذه الشهادة صالحة لمدة سنة واحدة من تاريخ الإصدار</p>
            </div>
        </body>
        </html>
    `;
}

// ==================== دوال التقارير ====================

// إنشاء تقرير
function generateReport(type) {
    switch (type) {
        case 'customers':
            generateCustomersReport();
            break;
        case 'cards':
            generateCardsReport();
            break;
        case 'appointments':
            generateAppointmentsReport();
            break;
        case 'performance':
            generatePerformanceReport();
            break;
        default:
            showToast('نوع التقرير غير مدعوم', false);
    }
}

// تقرير الزبائن
function generateCustomersReport() {
    if (appData.customers.length === 0) {
        showToast('لا توجد بيانات زبائن لإنشاء التقرير', false);
        return;
    }

    const printWindow = window.open('', '_blank');
    const reportHTML = generateCustomersReportHTML();

    printWindow.document.write(reportHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();

    addNotification('📊 تقرير جديد', 'تم إنشاء تقرير الزبائن', 'info');
    showToast('تم إنشاء تقرير الزبائن');
}

// HTML تقرير الزبائن
function generateCustomersReportHTML() {
    const currentDate = new Date().toLocaleDateString('ar-DZ');

    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير الزبائن</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3498db; padding-bottom: 15px; }
                .header h1 { color: #2c3e50; margin-bottom: 5px; }
                .header p { color: #7f8c8d; margin: 5px 0; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 12px; text-align: right; }
                th { background-color: #3498db; color: white; }
                tr:nth-child(even) { background-color: #f2f2f2; }
                .footer { margin-top: 30px; text-align: center; color: #7f8c8d; font-size: 12px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير الزبائن</h1>
                <p>مؤسسة وقود المستقبل</p>
                <p>تاريخ التقرير: ${currentDate}</p>
                <p>عدد الزبائن: ${appData.customers.length}</p>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>رقم الهاتف</th>
                        <th>العنوان</th>
                        <th>تاريخ التسجيل</th>
                    </tr>
                </thead>
                <tbody>
                    ${appData.customers.map((customer, index) => `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${customer.name}</td>
                            <td>${customer.phone}</td>
                            <td>${customer.address || 'غير محدد'}</td>
                            <td>${formatDate(customer.createdAt)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مؤسسة وقود المستقبل</p>
                <p>© ${new Date().getFullYear()} جميع الحقوق محفوظة</p>
            </div>
        </body>
        </html>
    `;
}

// ==================== دوال إدارة الموردين ====================

// إضافة مورد جديد
function addSupplier() {
    const supplierForm = `
        <form id="supplier-form">
            <div class="form-group">
                <label for="supplier-name">اسم المورد:</label>
                <input type="text" id="supplier-name" required>
            </div>
            <div class="form-group">
                <label for="supplier-company">اسم الشركة:</label>
                <input type="text" id="supplier-company">
            </div>
            <div class="form-group">
                <label for="supplier-phone">رقم الهاتف:</label>
                <input type="tel" id="supplier-phone" required>
            </div>
            <div class="form-group">
                <label for="supplier-address">العنوان:</label>
                <input type="text" id="supplier-address">
            </div>
            <div class="form-group">
                <label for="supplier-category">الفئة:</label>
                <select id="supplier-category" required>
                    <option value="">اختر الفئة</option>
                    <option value="قطع غيار">قطع غيار</option>
                    <option value="أدوات">أدوات</option>
                    <option value="مواد خام">مواد خام</option>
                    <option value="خدمات">خدمات</option>
                </select>
            </div>
            <div class="form-group">
                <label for="supplier-balance">الرصيد الابتدائي:</label>
                <input type="number" id="supplier-balance" value="0" step="0.01">
            </div>
            <div class="form-group">
                <label for="supplier-status">الحالة:</label>
                <select id="supplier-status" required>
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">إضافة المورد</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('إضافة مورد جديد', supplierForm);

    document.getElementById('supplier-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const supplier = {
            id: generateId(),
            name: document.getElementById('supplier-name').value,
            company: document.getElementById('supplier-company').value,
            phone: document.getElementById('supplier-phone').value,
            address: document.getElementById('supplier-address').value,
            category: document.getElementById('supplier-category').value,
            balance: parseFloat(document.getElementById('supplier-balance').value) || 0,
            status: document.getElementById('supplier-status').value,
            createdAt: new Date().toISOString(),
            lastDeal: null
        };

        appData.suppliers.push(supplier);
        saveData();
        updateSuppliersTable();
        updateStats();
        hideModal();

        addNotification('🚚 مورد جديد', `تم إضافة المورد ${supplier.name}`, 'success');
        showToast('تم إضافة المورد بنجاح');
    });
}

// تعديل مورد
function editSupplier(supplierId) {
    const supplier = appData.suppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    const supplierForm = `
        <form id="edit-supplier-form">
            <div class="form-group">
                <label for="edit-supplier-name">اسم المورد:</label>
                <input type="text" id="edit-supplier-name" value="${supplier.name}" required>
            </div>
            <div class="form-group">
                <label for="edit-supplier-company">اسم الشركة:</label>
                <input type="text" id="edit-supplier-company" value="${supplier.company || ''}">
            </div>
            <div class="form-group">
                <label for="edit-supplier-phone">رقم الهاتف:</label>
                <input type="tel" id="edit-supplier-phone" value="${supplier.phone}" required>
            </div>
            <div class="form-group">
                <label for="edit-supplier-address">العنوان:</label>
                <input type="text" id="edit-supplier-address" value="${supplier.address || ''}">
            </div>
            <div class="form-group">
                <label for="edit-supplier-category">الفئة:</label>
                <select id="edit-supplier-category" required>
                    <option value="قطع غيار" ${supplier.category === 'قطع غيار' ? 'selected' : ''}>قطع غيار</option>
                    <option value="أدوات" ${supplier.category === 'أدوات' ? 'selected' : ''}>أدوات</option>
                    <option value="مواد خام" ${supplier.category === 'مواد خام' ? 'selected' : ''}>مواد خام</option>
                    <option value="خدمات" ${supplier.category === 'خدمات' ? 'selected' : ''}>خدمات</option>
                </select>
            </div>
            <div class="form-group">
                <label for="edit-supplier-balance">الرصيد:</label>
                <input type="number" id="edit-supplier-balance" value="${supplier.balance}" step="0.01">
            </div>
            <div class="form-group">
                <label for="edit-supplier-status">الحالة:</label>
                <select id="edit-supplier-status" required>
                    <option value="نشط" ${supplier.status === 'نشط' ? 'selected' : ''}>نشط</option>
                    <option value="غير نشط" ${supplier.status === 'غير نشط' ? 'selected' : ''}>غير نشط</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">إلغاء</button>
            </div>
        </form>
    `;

    showModal('تعديل المورد', supplierForm);

    document.getElementById('edit-supplier-form').addEventListener('submit', (e) => {
        e.preventDefault();

        supplier.name = document.getElementById('edit-supplier-name').value;
        supplier.company = document.getElementById('edit-supplier-company').value;
        supplier.phone = document.getElementById('edit-supplier-phone').value;
        supplier.address = document.getElementById('edit-supplier-address').value;
        supplier.category = document.getElementById('edit-supplier-category').value;
        supplier.balance = parseFloat(document.getElementById('edit-supplier-balance').value) || 0;
        supplier.status = document.getElementById('edit-supplier-status').value;
        supplier.updatedAt = new Date().toISOString();

        saveData();
        updateSuppliersTable();
        hideModal();

        showToast('تم تحديث بيانات المورد بنجاح');
    });
}

// حذف مورد
function deleteSupplier(supplierId) {
    const supplier = appData.suppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    if (confirm(`هل أنت متأكد من حذف المورد "${supplier.name}"؟`)) {
        appData.suppliers = appData.suppliers.filter(s => s.id !== supplierId);
        saveData();
        updateSuppliersTable();
        updateStats();

        showToast('تم حذف المورد بنجاح');
    }
}

// عرض تفاصيل المورد
function viewSupplierDetails(supplierId) {
    const supplier = appData.suppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    // حساب إحصائيات المورد
    const supplierPurchases = appData.purchases.filter(p => p.supplierName === supplier.name);
    const totalPurchases = supplierPurchases.reduce((sum, p) => sum + p.totalAmount, 0);
    const totalDebt = appData.debts.filter(d => d.name === supplier.name && d.type === 'علينا').reduce((sum, d) => sum + d.amount, 0);

    const detailsHTML = `
        <div class="supplier-details">
            <h3>تفاصيل المورد: ${supplier.name}</h3>
            <div class="details-grid">
                <div class="detail-item">
                    <strong>اسم الشركة:</strong> ${supplier.company || 'غير محدد'}
                </div>
                <div class="detail-item">
                    <strong>رقم الهاتف:</strong> ${supplier.phone}
                </div>
                <div class="detail-item">
                    <strong>العنوان:</strong> ${supplier.address || 'غير محدد'}
                </div>
                <div class="detail-item">
                    <strong>الفئة:</strong> ${supplier.category}
                </div>
                <div class="detail-item">
                    <strong>الرصيد الحالي:</strong> ${supplier.balance} د.ج
                </div>
                <div class="detail-item">
                    <strong>الحالة:</strong> ${supplier.status}
                </div>
                <div class="detail-item">
                    <strong>إجمالي المشتريات:</strong> ${totalPurchases} د.ج
                </div>
                <div class="detail-item">
                    <strong>إجمالي الديون:</strong> ${totalDebt} د.ج
                </div>
                <div class="detail-item">
                    <strong>عدد المشتريات:</strong> ${supplierPurchases.length}
                </div>
                <div class="detail-item">
                    <strong>آخر تعامل:</strong> ${supplier.lastDeal ? formatDate(supplier.lastDeal) : 'لا يوجد'}
                </div>
                <div class="detail-item">
                    <strong>تاريخ التسجيل:</strong> ${formatDate(supplier.createdAt)}
                </div>
            </div>
        </div>
    `;

    showModal('تفاصيل المورد', detailsHTML);
}

// عرض تفاصيل الزبون
function viewCustomerDetails(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    // جمع بيانات السيارات
    const vehicles = appData.vehicles ? appData.vehicles.filter(v => v.customerId === customerId) : [];

    // جمع بيانات الخزانات
    const tanks = appData.tanks ? appData.tanks.filter(t => t.customerId === customerId) : [];

    // جمع بيانات المواعيد
    const appointments = appData.appointments.filter(a => a.customerName === customer.name);

    // جمع بيانات البطاقات
    const gasCards = appData.gasCards.filter(c => c.customerId === customerId);

    const detailsHTML = `
        <div class="customer-details-view">
            <div class="customer-header">
                <h3><i class="fas fa-user"></i> ${customer.name}</h3>
                <span class="customer-status ${customer.status === 'نشط' ? 'status-active' : 'status-inactive'}">${customer.status || 'نشط'}</span>
            </div>

            <div class="details-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="showTab('personal-info')">المعلومات الشخصية</button>
                    <button class="tab-btn" onclick="showTab('vehicles-info')">السيارات (${vehicles.length})</button>
                    <button class="tab-btn" onclick="showTab('services-info')">الخدمات (${appointments.length})</button>
                    <button class="tab-btn" onclick="showTab('cards-info')">البطاقات (${gasCards.length})</button>
                </div>

                <div id="personal-info" class="tab-content active">
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <div>
                                <strong>الاسم الكامل</strong>
                                <span>${customer.name}</span>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-phone"></i>
                            <div>
                                <strong>الهاتف الأساسي</strong>
                                <span>${customer.phone}</span>
                            </div>
                        </div>
                        ${customer.phone2 ? `
                        <div class="info-item">
                            <i class="fas fa-mobile-alt"></i>
                            <div>
                                <strong>الهاتف الإضافي</strong>
                                <span>${customer.phone2}</span>
                            </div>
                        </div>
                        ` : ''}
                        ${customer.email ? `
                        <div class="info-item">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <strong>البريد الإلكتروني</strong>
                                <span>${customer.email}</span>
                            </div>
                        </div>
                        ` : ''}
                        ${customer.address ? `
                        <div class="info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <strong>العنوان</strong>
                                <span>${customer.address}</span>
                            </div>
                        </div>
                        ` : ''}
                        ${customer.idNumber ? `
                        <div class="info-item">
                            <i class="fas fa-id-card"></i>
                            <div>
                                <strong>رقم الهوية</strong>
                                <span>${customer.idNumber}</span>
                            </div>
                        </div>
                        ` : ''}
                        ${customer.birthDate ? `
                        <div class="info-item">
                            <i class="fas fa-birthday-cake"></i>
                            <div>
                                <strong>تاريخ الميلاد</strong>
                                <span>${formatDate(customer.birthDate)}</span>
                            </div>
                        </div>
                        ` : ''}
                        <div class="info-item">
                            <i class="fas fa-calendar-plus"></i>
                            <div>
                                <strong>تاريخ التسجيل</strong>
                                <span>${formatDate(customer.createdAt)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="vehicles-info" class="tab-content">
                    ${vehicles.length > 0 ? `
                        <div class="vehicles-list">
                            ${vehicles.map(vehicle => `
                                <div class="vehicle-card">
                                    <div class="vehicle-header">
                                        <h4><i class="fas fa-car"></i> ${vehicle.brand} ${vehicle.model || ''}</h4>
                                        <span class="vehicle-number">${vehicle.registrationNumber}</span>
                                    </div>
                                    <div class="vehicle-details">
                                        <div class="detail-row">
                                            <span><strong>النوع:</strong> ${vehicle.type || 'غير محدد'}</span>
                                            <span><strong>السنة:</strong> ${vehicle.year || 'غير محدد'}</span>
                                        </div>
                                        <div class="detail-row">
                                            <span><strong>اللون:</strong> ${vehicle.color || 'غير محدد'}</span>
                                            ${vehicle.chassisNumber ? `<span><strong>رقم الهيكل:</strong> ${vehicle.chassisNumber}</span>` : ''}
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : '<p class="no-data">لا توجد سيارات مسجلة</p>'}
                </div>

                <div id="services-info" class="tab-content">
                    ${appointments.length > 0 ? `
                        <div class="services-list">
                            ${appointments.map(appointment => `
                                <div class="service-card">
                                    <div class="service-header">
                                        <h4><i class="fas fa-cogs"></i> ${appointment.serviceType}</h4>
                                        <span class="service-status ${getStatusClass(appointment.status)}">${appointment.status}</span>
                                    </div>
                                    <div class="service-details">
                                        <div class="detail-row">
                                            <span><strong>التاريخ:</strong> ${formatDate(appointment.date)}</span>
                                            <span><strong>الوقت:</strong> ${appointment.time}</span>
                                        </div>
                                        <div class="detail-row">
                                            <span><strong>السيارة:</strong> ${appointment.vehicleNumber}</span>
                                            <span><strong>الأولوية:</strong> ${appointment.priority || 'عادية'}</span>
                                        </div>
                                        ${appointment.notes ? `<div class="service-notes"><strong>الملاحظات:</strong> ${appointment.notes}</div>` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : '<p class="no-data">لا توجد خدمات مسجلة</p>'}
                </div>

                <div id="cards-info" class="tab-content">
                    ${gasCards.length > 0 ? `
                        <div class="cards-list">
                            ${gasCards.map(card => `
                                <div class="card-item">
                                    <div class="card-header">
                                        <h4><i class="fas fa-id-card"></i> بطاقة رقم ${card.cardNumber}</h4>
                                        <span class="card-status ${getStatusClass(card.status)}">${card.status}</span>
                                    </div>
                                    <div class="card-details">
                                        <div class="detail-row">
                                            <span><strong>تاريخ الإصدار:</strong> ${formatDate(card.issueDate)}</span>
                                            <span><strong>تاريخ الانتهاء:</strong> ${formatDate(card.expiryDate)}</span>
                                        </div>
                                        <div class="detail-row">
                                            <span><strong>السيارة:</strong> ${card.vehicleNumber}</span>
                                            <span><strong>الأيام المتبقية:</strong> ${calculateDaysRemaining(card.expiryDate)} يوم</span>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    ` : '<p class="no-data">لا توجد بطاقات مسجلة</p>'}
                </div>
            </div>

            <div class="customer-actions">
                <button class="btn btn-primary" onclick="editCustomer('${customerId}')">
                    <i class="fas fa-edit"></i> تعديل البيانات
                </button>
                <button class="btn btn-success" onclick="addServiceForCustomer('${customerId}')">
                    <i class="fas fa-plus"></i> إضافة خدمة
                </button>
                <button class="btn btn-info" onclick="printCustomerData('${customerId}')">
                    <i class="fas fa-print"></i> طباعة البيانات
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('تفاصيل الزبون', detailsHTML);
}

// إضافة خدمة لزبون موجود
function addServiceForCustomer(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    const vehicles = appData.vehicles ? appData.vehicles.filter(v => v.customerId === customerId) : [];

    const serviceForm = `
        <form id="add-service-form">
            <div class="form-header">
                <h3><i class="fas fa-cogs"></i> إضافة خدمة للزبون: ${customer.name}</h3>
            </div>

            <div class="form-section">
                <div class="form-group">
                    <label for="service-vehicle">اختر السيارة:</label>
                    <select id="service-vehicle" required>
                        <option value="">اختر السيارة</option>
                        ${vehicles.map(v => `<option value="${v.id}">${v.registrationNumber} - ${v.brand} ${v.model || ''}</option>`).join('')}
                    </select>
                </div>

                <div class="service-options">
                    <div class="service-option">
                        <input type="radio" id="new-service-installation" name="new-service-type" value="تركيب">
                        <label for="new-service-installation" class="service-label">
                            <i class="fas fa-tools"></i>
                            <span>تركيب نظام الغاز</span>
                        </label>
                    </div>

                    <div class="service-option">
                        <input type="radio" id="new-service-monitoring" name="new-service-type" value="مراقبة">
                        <label for="new-service-monitoring" class="service-label">
                            <i class="fas fa-search"></i>
                            <span>مراقبة دورية</span>
                        </label>
                    </div>

                    <div class="service-option">
                        <input type="radio" id="new-service-maintenance" name="new-service-type" value="صيانة">
                        <label for="new-service-maintenance" class="service-label">
                            <i class="fas fa-wrench"></i>
                            <span>صيانة وإصلاح</span>
                        </label>
                    </div>

                    <div class="service-option">
                        <input type="radio" id="new-service-renewal" name="new-service-type" value="تجديد بطاقة">
                        <label for="new-service-renewal" class="service-label">
                            <i class="fas fa-id-card"></i>
                            <span>تجديد البطاقة</span>
                        </label>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="new-service-date">تاريخ الخدمة:</label>
                        <input type="date" id="new-service-date" value="${new Date().toISOString().split('T')[0]}" required>
                    </div>
                    <div class="form-group">
                        <label for="new-service-time">الوقت:</label>
                        <input type="time" id="new-service-time" value="09:00" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="new-service-priority">الأولوية:</label>
                        <select id="new-service-priority">
                            <option value="عادية">عادية</option>
                            <option value="مهمة">مهمة</option>
                            <option value="عاجلة">عاجلة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="new-service-cost">التكلفة المتوقعة:</label>
                        <input type="number" id="new-service-cost" min="0" step="100" placeholder="0">
                    </div>
                </div>

                <div class="form-group">
                    <label for="new-service-notes">ملاحظات:</label>
                    <textarea id="new-service-notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ الخدمة
                </button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </form>
    `;

    showModal('إضافة خدمة جديدة', serviceForm);

    document.getElementById('add-service-form').addEventListener('submit', (e) => {
        e.preventDefault();

        const serviceType = document.querySelector('input[name="new-service-type"]:checked');
        const vehicleId = document.getElementById('service-vehicle').value;
        const vehicle = vehicles.find(v => v.id === vehicleId);

        if (!serviceType) {
            showToast('يرجى اختيار نوع الخدمة', false);
            return;
        }

        if (!vehicle) {
            showToast('يرجى اختيار السيارة', false);
            return;
        }

        const newAppointment = {
            id: generateId(),
            customerName: customer.name,
            vehicleNumber: vehicle.registrationNumber,
            serviceType: serviceType.value,
            date: document.getElementById('new-service-date').value,
            time: document.getElementById('new-service-time').value,
            priority: document.getElementById('new-service-priority').value,
            cost: parseFloat(document.getElementById('new-service-cost').value) || 0,
            notes: document.getElementById('new-service-notes').value,
            status: 'مجدولة',
            createdAt: new Date().toISOString()
        };

        appData.appointments.push(newAppointment);
        saveData();
        updateAllTables();
        updateStats();
        hideModal();

        addNotification('📅 خدمة جديدة', `تم جدولة ${newAppointment.serviceType} للزبون ${customer.name}`, 'success');
        showToast('تم إضافة الخدمة بنجاح');
    });
}

// دالة التبويبات
function showTab(tabId, buttonElement) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // إزالة الفئة النشطة من جميع الأزرار
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إظهار التبويب المحدد
    const targetTab = document.getElementById(tabId);
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // تفعيل الزر المحدد
    if (buttonElement) {
        buttonElement.classList.add('active');
    }
}

// طباعة بيانات الزبون
function printCustomerData(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    const vehicles = appData.vehicles ? appData.vehicles.filter(v => v.customerId === customerId) : [];
    const appointments = appData.appointments.filter(a => a.customerName === customer.name);
    const gasCards = appData.gasCards.filter(c => c.customerId === customerId);

    const printWindow = window.open('', '_blank');
    const printHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>بيانات الزبون - ${customer.name}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #3498db; padding-bottom: 20px; }
                .section { margin-bottom: 25px; }
                .section h3 { color: #2c3e50; border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; }
                .info-table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
                .info-table td { padding: 8px; border: 1px solid #ddd; }
                .info-table .label { background: #f8f9fa; font-weight: bold; width: 30%; }
                .vehicle-item, .service-item, .card-item {
                    background: #f8f9fa; padding: 10px; margin-bottom: 10px; border-radius: 5px;
                }
                @media print { body { margin: 10px; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>مؤسسة وقود المستقبل</h1>
                <h2>بيانات الزبون</h2>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-DZ')}</p>
            </div>

            <div class="section">
                <h3>المعلومات الشخصية</h3>
                <table class="info-table">
                    <tr><td class="label">الاسم الكامل:</td><td>${customer.name}</td></tr>
                    <tr><td class="label">الهاتف الأساسي:</td><td>${customer.phone}</td></tr>
                    ${customer.phone2 ? `<tr><td class="label">الهاتف الإضافي:</td><td>${customer.phone2}</td></tr>` : ''}
                    ${customer.email ? `<tr><td class="label">البريد الإلكتروني:</td><td>${customer.email}</td></tr>` : ''}
                    ${customer.address ? `<tr><td class="label">العنوان:</td><td>${customer.address}</td></tr>` : ''}
                    ${customer.idNumber ? `<tr><td class="label">رقم الهوية:</td><td>${customer.idNumber}</td></tr>` : ''}
                    ${customer.birthDate ? `<tr><td class="label">تاريخ الميلاد:</td><td>${formatDate(customer.birthDate)}</td></tr>` : ''}
                    <tr><td class="label">تاريخ التسجيل:</td><td>${formatDate(customer.createdAt)}</td></tr>
                </table>
            </div>

            ${vehicles.length > 0 ? `
            <div class="section">
                <h3>السيارات المسجلة (${vehicles.length})</h3>
                ${vehicles.map(vehicle => `
                    <div class="vehicle-item">
                        <strong>${vehicle.brand} ${vehicle.model || ''} - ${vehicle.registrationNumber}</strong><br>
                        النوع: ${vehicle.type || 'غير محدد'} | السنة: ${vehicle.year || 'غير محدد'} | اللون: ${vehicle.color || 'غير محدد'}
                        ${vehicle.chassisNumber ? `<br>رقم الهيكل: ${vehicle.chassisNumber}` : ''}
                    </div>
                `).join('')}
            </div>
            ` : ''}

            ${appointments.length > 0 ? `
            <div class="section">
                <h3>الخدمات والمواعيد (${appointments.length})</h3>
                ${appointments.map(appointment => `
                    <div class="service-item">
                        <strong>${appointment.serviceType}</strong> - ${formatDate(appointment.date)} ${appointment.time}<br>
                        السيارة: ${appointment.vehicleNumber} | الحالة: ${appointment.status} | الأولوية: ${appointment.priority || 'عادية'}
                        ${appointment.notes ? `<br>الملاحظات: ${appointment.notes}` : ''}
                    </div>
                `).join('')}
            </div>
            ` : ''}

            ${gasCards.length > 0 ? `
            <div class="section">
                <h3>بطاقات الغاز (${gasCards.length})</h3>
                ${gasCards.map(card => `
                    <div class="card-item">
                        <strong>بطاقة رقم ${card.cardNumber}</strong><br>
                        السيارة: ${card.vehicleNumber} | الحالة: ${card.status}<br>
                        تاريخ الإصدار: ${formatDate(card.issueDate)} | تاريخ الانتهاء: ${formatDate(card.expiryDate)}
                    </div>
                `).join('')}
            </div>
            ` : ''}

            <div style="margin-top: 50px; text-align: center; font-size: 12px; color: #666;">
                تم إنشاء هذا التقرير بواسطة نظام إدارة مؤسسة وقود المستقبل
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(printHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}

// نظام التنبيهات الذكية
function checkSmartAlerts() {
    console.log('🔔 فحص التنبيهات الذكية...');

    const today = new Date();
    const alerts = [];

    // فحص البطاقات منتهية الصلاحية
    const expiringCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
    });

    if (expiringCards.length > 0) {
        alerts.push({
            type: 'warning',
            title: 'بطاقات تنتهي قريباً',
            message: `${expiringCards.length} بطاقة تنتهي خلال 30 يوم`,
            action: () => showExpiringCards()
        });
    }

    // فحص المواعيد اليوم
    const todayAppointments = appData.appointments.filter(appointment => {
        const appointmentDate = new Date(appointment.date);
        return appointmentDate.toDateString() === today.toDateString();
    });

    if (todayAppointments.length > 0) {
        alerts.push({
            type: 'info',
            title: 'مواعيد اليوم',
            message: `لديك ${todayAppointments.length} موعد اليوم`,
            action: () => showTodayAppointments()
        });
    }

    // فحص المخزون المنخفض
    const lowStockItems = appData.inventory.filter(item => item.quantity <= item.minQuantity);

    if (lowStockItems.length > 0) {
        alerts.push({
            type: 'danger',
            title: 'مخزون منخفض',
            message: `${lowStockItems.length} صنف يحتاج إعادة تموين`,
            action: () => showLowStockItems()
        });
    }

    // فحص الديون المستحقة
    const overdueDebts = appData.debts.filter(debt => {
        const dueDate = new Date(debt.dueDate);
        return dueDate < today && debt.status !== 'مسدد';
    });

    if (overdueDebts.length > 0) {
        alerts.push({
            type: 'danger',
            title: 'ديون متأخرة',
            message: `${overdueDebts.length} دين متأخر عن موعد السداد`,
            action: () => showOverdueDebts()
        });
    }

    // عرض التنبيهات
    alerts.forEach(alert => {
        addNotification(alert.title, alert.message, alert.type);
    });

    // تحديث شارة التنبيهات
    updateSmartAlertsDisplay(alerts);

    return alerts;
}

// تحديث عرض التنبيهات الذكية
function updateSmartAlertsDisplay(alerts) {
    const alertsContainer = document.getElementById('smart-alerts-container');
    if (!alertsContainer) return;

    alertsContainer.innerHTML = '';

    if (alerts.length === 0) {
        alertsContainer.innerHTML = '<div class="no-alerts">لا توجد تنبيهات</div>';
        return;
    }

    alerts.forEach(alert => {
        const alertElement = document.createElement('div');
        alertElement.className = `smart-alert alert-${alert.type}`;
        alertElement.innerHTML = `
            <div class="alert-icon">
                <i class="fas fa-${getAlertIcon(alert.type)}"></i>
            </div>
            <div class="alert-content">
                <h4>${alert.title}</h4>
                <p>${alert.message}</p>
            </div>
            <div class="alert-actions">
                <button class="btn btn-sm btn-primary" onclick="handleAlertAction('${alert.type}')">
                    عرض
                </button>
                <button class="btn btn-sm btn-secondary" onclick="dismissAlert(this)">
                    إخفاء
                </button>
            </div>
        `;
        alertsContainer.appendChild(alertElement);
    });
}

// الحصول على أيقونة التنبيه
function getAlertIcon(type) {
    switch (type) {
        case 'warning': return 'exclamation-triangle';
        case 'danger': return 'exclamation-circle';
        case 'info': return 'info-circle';
        case 'success': return 'check-circle';
        default: return 'bell';
    }
}

// معالجة إجراء التنبيه
function handleAlertAction(type) {
    switch (type) {
        case 'warning':
            showExpiringCards();
            break;
        case 'info':
            showTodayAppointments();
            break;
        case 'danger':
            showOverdueDebts();
            break;
    }
}

// إخفاء التنبيه
function dismissAlert(button) {
    const alertElement = button.closest('.smart-alert');
    alertElement.style.animation = 'slideOut 0.3s ease-out';
    setTimeout(() => {
        alertElement.remove();
    }, 300);
}

// عرض البطاقات منتهية الصلاحية
function showExpiringCards() {
    const today = new Date();
    const expiringCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
        return daysUntilExpiry <= 30 && daysUntilExpiry >= 0;
    });

    let cardsHTML = `
        <div class="expiring-cards-list">
            <h3>البطاقات التي تنتهي خلال 30 يوم</h3>
            <div class="cards-grid">
    `;

    expiringCards.forEach(card => {
        const expiryDate = new Date(card.expiryDate);
        const daysUntilExpiry = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

        cardsHTML += `
            <div class="expiring-card-item ${daysUntilExpiry <= 7 ? 'urgent' : 'warning'}">
                <div class="card-info">
                    <h4>${card.customerName}</h4>
                    <p>رقم البطاقة: ${card.cardNumber}</p>
                    <p>رقم السيارة: ${card.vehicleNumber}</p>
                    <p class="expiry-info">تنتهي خلال ${daysUntilExpiry} يوم</p>
                </div>
                <div class="card-actions">
                    <button class="btn btn-sm btn-warning" onclick="renewCard('${card.id}')">
                        <i class="fas fa-sync"></i> تجديد
                    </button>
                    <button class="btn btn-sm btn-info" onclick="contactCustomer('${card.customerId}')">
                        <i class="fas fa-phone"></i> اتصال
                    </button>
                </div>
            </div>
        `;
    });

    cardsHTML += `
            </div>
            <div class="bulk-renewal-actions">
                <button class="btn btn-primary" onclick="renewAllExpiringCards()">
                    <i class="fas fa-sync"></i> تجديد جميع البطاقات
                </button>
                <button class="btn btn-secondary" onclick="exportExpiringCards()">
                    <i class="fas fa-download"></i> تصدير القائمة
                </button>
            </div>
        </div>
    `;

    showModal('البطاقات منتهية الصلاحية', cardsHTML);
}

// عرض مواعيد اليوم
function showTodayAppointments() {
    const today = new Date();
    const todayAppointments = appData.appointments.filter(appointment => {
        const appointmentDate = new Date(appointment.date);
        return appointmentDate.toDateString() === today.toDateString();
    });

    let appointmentsHTML = `
        <div class="today-appointments-list">
            <h3>مواعيد اليوم - ${formatDate(today.toISOString())}</h3>
            <div class="appointments-grid">
    `;

    if (todayAppointments.length === 0) {
        appointmentsHTML += '<div class="no-appointments">لا توجد مواعيد اليوم</div>';
    } else {
        todayAppointments.forEach(appointment => {
            appointmentsHTML += `
                <div class="appointment-item">
                    <div class="appointment-time">
                        <i class="fas fa-clock"></i>
                        ${appointment.time}
                    </div>
                    <div class="appointment-info">
                        <h4>${appointment.customerName}</h4>
                        <p>${appointment.serviceType}</p>
                        <p class="appointment-notes">${appointment.notes || 'لا توجد ملاحظات'}</p>
                    </div>
                    <div class="appointment-actions">
                        <button class="btn btn-sm btn-success" onclick="markAppointmentComplete('${appointment.id}')">
                            <i class="fas fa-check"></i> مكتمل
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="rescheduleAppointment('${appointment.id}')">
                            <i class="fas fa-calendar"></i> إعادة جدولة
                        </button>
                    </div>
                </div>
            `;
        });
    }

    appointmentsHTML += `
            </div>
        </div>
    `;

    showModal('مواعيد اليوم', appointmentsHTML);
}

// عرض الأصناف منخفضة المخزون
function showLowStockItems() {
    const lowStockItems = appData.inventory.filter(item => item.quantity <= item.minQuantity);

    let itemsHTML = `
        <div class="low-stock-list">
            <h3>أصناف تحتاج إعادة تموين</h3>
            <div class="items-grid">
    `;

    if (lowStockItems.length === 0) {
        itemsHTML += '<div class="no-items">جميع الأصناف متوفرة بكمية كافية</div>';
    } else {
        lowStockItems.forEach(item => {
            const stockPercentage = (item.quantity / item.minQuantity) * 100;
            const urgencyClass = stockPercentage <= 50 ? 'critical' : 'warning';

            itemsHTML += `
                <div class="stock-item ${urgencyClass}">
                    <div class="item-info">
                        <h4>${item.name}</h4>
                        <p>الكود: ${item.code}</p>
                        <p>الفئة: ${item.category}</p>
                    </div>
                    <div class="stock-info">
                        <div class="current-stock">
                            <span class="label">المخزون الحالي:</span>
                            <span class="value">${item.quantity}</span>
                        </div>
                        <div class="min-stock">
                            <span class="label">الحد الأدنى:</span>
                            <span class="value">${item.minQuantity}</span>
                        </div>
                        <div class="stock-bar">
                            <div class="stock-fill" style="width: ${Math.min(stockPercentage, 100)}%"></div>
                        </div>
                    </div>
                    <div class="item-actions">
                        <button class="btn btn-sm btn-primary" onclick="addStock('${item.id}')">
                            <i class="fas fa-plus"></i> إضافة مخزون
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="orderFromSupplier('${item.id}')">
                            <i class="fas fa-shopping-cart"></i> طلب من مورد
                        </button>
                    </div>
                </div>
            `;
        });
    }

    itemsHTML += `
            </div>
            <div class="bulk-actions">
                <button class="btn btn-primary" onclick="generatePurchaseOrder()">
                    <i class="fas fa-file-alt"></i> إنشاء أمر شراء
                </button>
                <button class="btn btn-secondary" onclick="exportLowStockReport()">
                    <i class="fas fa-download"></i> تصدير تقرير
                </button>
            </div>
        </div>
    `;

    showModal('أصناف منخفضة المخزون', itemsHTML);
}

// عرض الديون المتأخرة
function showOverdueDebts() {
    const today = new Date();
    const overdueDebts = appData.debts.filter(debt => {
        const dueDate = new Date(debt.dueDate);
        return dueDate < today && debt.status !== 'مسدد';
    });

    let debtsHTML = `
        <div class="overdue-debts-list">
            <h3>الديون المتأخرة عن موعد السداد</h3>
            <div class="debts-grid">
    `;

    if (overdueDebts.length === 0) {
        debtsHTML += '<div class="no-debts">لا توجد ديون متأخرة</div>';
    } else {
        overdueDebts.forEach(debt => {
            const dueDate = new Date(debt.dueDate);
            const daysOverdue = Math.ceil((today - dueDate) / (1000 * 60 * 60 * 24));

            debtsHTML += `
                <div class="debt-item ${debt.type === 'لنا' ? 'debt-for-us' : 'debt-on-us'}">
                    <div class="debt-info">
                        <h4>${debt.name}</h4>
                        <p class="debt-type">${debt.type}</p>
                        <p class="debt-amount">${debt.amount} د.ج</p>
                        <p class="overdue-info">متأخر ${daysOverdue} يوم</p>
                        <p class="debt-notes">${debt.notes || 'لا توجد ملاحظات'}</p>
                    </div>
                    <div class="debt-actions">
                        <button class="btn btn-sm btn-success" onclick="markDebtAsPaid('${debt.id}')">
                            <i class="fas fa-check"></i> تسديد
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="extendDebtDueDate('${debt.id}')">
                            <i class="fas fa-calendar-plus"></i> تمديد
                        </button>
                        <button class="btn btn-sm btn-info" onclick="contactDebtor('${debt.id}')">
                            <i class="fas fa-phone"></i> اتصال
                        </button>
                    </div>
                </div>
            `;
        });
    }

    debtsHTML += `
            </div>
            <div class="debt-summary">
                <div class="summary-item">
                    <span class="label">إجمالي الديون المتأخرة:</span>
                    <span class="value">${overdueDebts.reduce((sum, debt) => sum + debt.amount, 0)} د.ج</span>
                </div>
            </div>
        </div>
    `;

    showModal('الديون المتأخرة', debtsHTML);
}

// تصدير جميع البيانات
function exportAllData() {
    const exportData = {
        exportDate: new Date().toISOString(),
        version: '1.0',
        companyInfo: appData.settings,
        data: {
            customers: appData.customers,
            gasCards: appData.gasCards,
            appointments: appData.appointments,
            transmissions: transmissionTableData,
            inventory: appData.inventory,
            suppliers: appData.suppliers,
            sales: appData.sales,
            purchases: appData.purchases,
            debts: appData.debts,
            notifications: appData.notifications
        },
        statistics: {
            totalCustomers: appData.customers.length,
            totalCards: appData.gasCards.length,
            totalAppointments: appData.appointments.length,
            totalTransmissions: transmissionTableData.length,
            exportedBy: 'نظام إدارة محطة الغاز',
            exportedAt: new Date().toLocaleString('ar-DZ')
        }
    };

    // تصدير كملف JSON
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `gas-shop-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showToast('تم تصدير جميع البيانات بنجاح', true);
    addNotification('📤 تصدير البيانات', 'تم تصدير جميع بيانات النظام بنجاح', 'success');
}

// تصدير البيانات كـ Excel
function exportToExcel() {
    // إنشاء workbook جديد
    const workbook = {
        SheetNames: [],
        Sheets: {}
    };

    // إضافة ورقة الزبائن
    if (appData.customers.length > 0) {
        const customersData = appData.customers.map(customer => ({
            'الاسم': customer.name,
            'الهاتف': customer.phone,
            'الهاتف الإضافي': customer.phone2 || '',
            'البريد الإلكتروني': customer.email || '',
            'العنوان': customer.address || '',
            'تاريخ الإضافة': formatDate(customer.createdAt),
            'الحالة': customer.status || 'نشط'
        }));

        workbook.SheetNames.push('الزبائن');
        workbook.Sheets['الزبائن'] = XLSX.utils.json_to_sheet(customersData);
    }

    // إضافة ورقة البطاقات
    if (appData.gasCards.length > 0) {
        const cardsData = appData.gasCards.map(card => ({
            'اسم الزبون': card.customerName,
            'رقم السيارة': card.vehicleNumber,
            'رقم البطاقة': card.cardNumber,
            'تاريخ الإصدار': formatDate(card.issueDate),
            'تاريخ الانتهاء': formatDate(card.expiryDate),
            'الحالة': card.status,
            'الملاحظات': card.notes || ''
        }));

        workbook.SheetNames.push('بطاقات الغاز');
        workbook.Sheets['بطاقات الغاز'] = XLSX.utils.json_to_sheet(cardsData);
    }

    // إضافة ورقة المواعيد
    if (appData.appointments.length > 0) {
        const appointmentsData = appData.appointments.map(appointment => ({
            'التاريخ': formatDate(appointment.date),
            'الوقت': appointment.time,
            'اسم الزبون': appointment.customerName,
            'نوع الخدمة': appointment.serviceType,
            'الملاحظات': appointment.notes || ''
        }));

        workbook.SheetNames.push('المواعيد');
        workbook.Sheets['المواعيد'] = XLSX.utils.json_to_sheet(appointmentsData);
    }

    // إضافة ورقة جدول الإرسال
    if (transmissionTableData.length > 0) {
        const transmissionData = transmissionTableData.map(transmission => ({
            'نوع العملية': transmission.type,
            'رقم الخزان': transmission.tankNumber,
            'نوع السيارة': transmission.carType,
            'الرقم التسلسلي': transmission.serialNumber,
            'رقم التسجيل': transmission.registrationNumber,
            'اسم المالك': transmission.ownerName,
            'تاريخ العملية': formatDate(transmission.operationDate)
        }));

        workbook.SheetNames.push('جدول الإرسال');
        workbook.Sheets['جدول الإرسال'] = XLSX.utils.json_to_sheet(transmissionData);
    }

    // تصدير الملف
    const fileName = `gas-shop-data-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, fileName);

    showToast('تم تصدير البيانات كملف Excel', true);
}

// استيراد البيانات
function importData() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const importedData = JSON.parse(e.target.result);

                // التحقق من صحة البيانات
                if (!importedData.data) {
                    throw new Error('تنسيق الملف غير صحيح');
                }

                // عرض معاينة البيانات المستوردة
                showImportPreview(importedData);

            } catch (error) {
                console.error('خطأ في استيراد البيانات:', error);
                showToast('خطأ في قراءة الملف: ' + error.message, false);
            }
        };

        reader.readAsText(file);
    };

    input.click();
}

// عرض معاينة البيانات المستوردة
function showImportPreview(importedData) {
    const data = importedData.data;

    let previewHTML = `
        <div class="import-preview">
            <h3>معاينة البيانات المستوردة</h3>
            <div class="import-stats">
                <div class="stat-item">
                    <span class="label">الزبائن:</span>
                    <span class="value">${data.customers?.length || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="label">بطاقات الغاز:</span>
                    <span class="value">${data.gasCards?.length || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="label">المواعيد:</span>
                    <span class="value">${data.appointments?.length || 0}</span>
                </div>
                <div class="stat-item">
                    <span class="label">عمليات الإرسال:</span>
                    <span class="value">${data.transmissions?.length || 0}</span>
                </div>
            </div>

            <div class="import-options">
                <h4>خيارات الاستيراد:</h4>
                <label>
                    <input type="radio" name="import-mode" value="replace" checked>
                    استبدال جميع البيانات الحالية
                </label>
                <label>
                    <input type="radio" name="import-mode" value="merge">
                    دمج مع البيانات الحالية
                </label>
                <label>
                    <input type="radio" name="import-mode" value="backup">
                    إنشاء نسخة احتياطية قبل الاستيراد
                </label>
            </div>

            <div class="import-actions">
                <button class="btn btn-primary" onclick="confirmImport('${btoa(JSON.stringify(importedData))}')">
                    <i class="fas fa-check"></i> تأكيد الاستيراد
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </div>
    `;

    showModal('استيراد البيانات', previewHTML);
}

// تأكيد الاستيراد
function confirmImport(encodedData) {
    try {
        const importedData = JSON.parse(atob(encodedData));
        const importMode = document.querySelector('input[name="import-mode"]:checked').value;

        // إنشاء نسخة احتياطية إذا طُلب ذلك
        if (importMode === 'backup') {
            createManualBackup();
        }

        // تطبيق البيانات المستوردة
        if (importMode === 'replace') {
            // استبدال كامل
            appData = {
                ...appData,
                ...importedData.data,
                settings: {
                    ...appData.settings,
                    ...importedData.companyInfo
                }
            };
        } else if (importMode === 'merge') {
            // دمج البيانات
            Object.keys(importedData.data).forEach(key => {
                if (Array.isArray(importedData.data[key])) {
                    appData[key] = [...(appData[key] || []), ...importedData.data[key]];
                }
            });
        }

        // حفظ وتحديث
        saveData();
        updateAllTables();
        updateStats();
        hideModal();

        showToast('تم استيراد البيانات بنجاح', true);
        addNotification('📥 استيراد البيانات', 'تم استيراد البيانات بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تأكيد الاستيراد:', error);
        showToast('خطأ في استيراد البيانات', false);
    }
}

// ==================== دوال إدارة الموظفين ====================

// إضافة موظف جديد
function addEmployee() {
    const employeeForm = `
        <div class="comprehensive-form-container">
            <form id="employee-form" class="comprehensive-form">
                <div class="form-header">
                    <h3><i class="fas fa-user-tie"></i> إضافة موظف جديد</h3>
                </div>

                <!-- المعلومات الشخصية -->
                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="employee-name">الاسم الكامل: *</label>
                            <input type="text" id="employee-name" required placeholder="أدخل الاسم الكامل">
                        </div>
                        <div class="form-group">
                            <label for="employee-id-number">رقم الهوية: *</label>
                            <input type="text" id="employee-id-number" required placeholder="رقم بطاقة الهوية">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="employee-phone">رقم الهاتف: *</label>
                            <input type="tel" id="employee-phone" required placeholder="0555123456">
                        </div>
                        <div class="form-group">
                            <label for="employee-email">البريد الإلكتروني:</label>
                            <input type="email" id="employee-email" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="employee-address">العنوان:</label>
                        <textarea id="employee-address" rows="2" placeholder="أدخل العنوان"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="employee-birth-date">تاريخ الميلاد:</label>
                            <input type="date" id="employee-birth-date">
                        </div>
                        <div class="form-group">
                            <label for="employee-gender">الجنس:</label>
                            <select id="employee-gender">
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمل -->
                <div class="form-header">
                    <h3><i class="fas fa-briefcase"></i> معلومات العمل</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="employee-position">المنصب: *</label>
                            <select id="employee-position" required>
                                <option value="">اختر المنصب</option>
                                <option value="مدير">مدير</option>
                                <option value="فني تركيب">فني تركيب</option>
                                <option value="فني مراقبة">فني مراقبة</option>
                                <option value="محاسب">محاسب</option>
                                <option value="موظف استقبال">موظف استقبال</option>
                                <option value="سائق">سائق</option>
                                <option value="عامل">عامل</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="employee-department">القسم:</label>
                            <select id="employee-department">
                                <option value="">اختر القسم</option>
                                <option value="الإدارة">الإدارة</option>
                                <option value="الفنيين">الفنيين</option>
                                <option value="المحاسبة">المحاسبة</option>
                                <option value="خدمة العملاء">خدمة العملاء</option>
                                <option value="الصيانة">الصيانة</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="employee-hire-date">تاريخ التوظيف: *</label>
                            <input type="date" id="employee-hire-date" required value="${new Date().toISOString().split('T')[0]}">
                        </div>
                        <div class="form-group">
                            <label for="employee-salary">الراتب الأساسي:</label>
                            <input type="number" id="employee-salary" placeholder="المبلغ بالدينار" min="0">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="employee-work-hours">ساعات العمل اليومية:</label>
                            <input type="number" id="employee-work-hours" value="8" min="1" max="12">
                        </div>
                        <div class="form-group">
                            <label for="employee-status">حالة الموظف:</label>
                            <select id="employee-status">
                                <option value="نشط">نشط</option>
                                <option value="إجازة">إجازة</option>
                                <option value="معلق">معلق</option>
                                <option value="مستقيل">مستقيل</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- معلومات الحساب -->
                <div class="form-header">
                    <h3><i class="fas fa-user-cog"></i> معلومات حساب النظام</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="employee-username">اسم المستخدم:</label>
                            <input type="text" id="employee-username" placeholder="اسم المستخدم للدخول">
                        </div>
                        <div class="form-group">
                            <label for="employee-password">كلمة المرور:</label>
                            <input type="password" id="employee-password" placeholder="كلمة المرور">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="employee-role">دور المستخدم:</label>
                        <select id="employee-role">
                            <option value="employee">موظف عادي</option>
                            <option value="supervisor">مشرف</option>
                            <option value="manager">مدير</option>
                            <option value="admin">مدير النظام</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>الصلاحيات:</label>
                        <div class="permissions-grid">
                            <label><input type="checkbox" value="customers"> إدارة الزبائن</label>
                            <label><input type="checkbox" value="cards"> إدارة البطاقات</label>
                            <label><input type="checkbox" value="appointments"> إدارة المواعيد</label>
                            <label><input type="checkbox" value="transmission"> جدول الإرسال</label>
                            <label><input type="checkbox" value="inventory"> إدارة المخزون</label>
                            <label><input type="checkbox" value="suppliers"> إدارة الموردين</label>
                            <label><input type="checkbox" value="sales"> إدارة المبيعات</label>
                            <label><input type="checkbox" value="reports"> التقارير</label>
                            <label><input type="checkbox" value="settings"> الإعدادات</label>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-large">
                        <i class="fas fa-save"></i> حفظ بيانات الموظف
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;

    showModal('إضافة موظف جديد', employeeForm);
    setupEmployeeForm();
}

// إعداد نموذج الموظف
function setupEmployeeForm() {
    const form = document.getElementById('employee-form');
    if (!form) return;

    form.addEventListener('submit', (e) => {
        e.preventDefault();

        // جمع بيانات الموظف
        const employeeData = {
            id: generateId(),
            name: document.getElementById('employee-name').value,
            idNumber: document.getElementById('employee-id-number').value,
            phone: document.getElementById('employee-phone').value,
            email: document.getElementById('employee-email').value || null,
            address: document.getElementById('employee-address').value || null,
            birthDate: document.getElementById('employee-birth-date').value || null,
            gender: document.getElementById('employee-gender').value,
            position: document.getElementById('employee-position').value,
            department: document.getElementById('employee-department').value || null,
            hireDate: document.getElementById('employee-hire-date').value,
            salary: parseFloat(document.getElementById('employee-salary').value) || 0,
            workHours: parseInt(document.getElementById('employee-work-hours').value) || 8,
            status: document.getElementById('employee-status').value,
            createdAt: new Date().toISOString()
        };

        // معلومات الحساب
        const username = document.getElementById('employee-username').value;
        const password = document.getElementById('employee-password').value;
        const role = document.getElementById('employee-role').value;

        // جمع الصلاحيات
        const permissions = Array.from(document.querySelectorAll('.permissions-grid input:checked'))
            .map(checkbox => checkbox.value);

        // إنشاء حساب مستخدم إذا تم توفير اسم المستخدم
        if (username) {
            const userData = {
                id: generateId(),
                employeeId: employeeData.id,
                username: username,
                password: password, // في التطبيق الحقيقي يجب تشفير كلمة المرور
                role: role,
                permissions: permissions,
                isActive: true,
                lastLogin: null,
                createdAt: new Date().toISOString()
            };

            appData.users.push(userData);
        }

        // حفظ بيانات الموظف
        appData.employees.push(employeeData);
        saveData();
        updateEmployeesTable();
        updateStats();
        hideModal();

        // إشعارات النجاح
        addNotification('👨‍💼 موظف جديد', `تم إضافة الموظف ${employeeData.name} بنجاح`, 'success');
        showToast('تم إضافة الموظف بنجاح!');
    });
}

// تحديث جدول الموظفين
function updateEmployeesTable() {
    const tbody = document.querySelector('#employees-table tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    appData.employees.forEach(employee => {
        const row = tbody.insertRow();

        // حساب سنوات الخبرة
        const hireDate = new Date(employee.hireDate);
        const today = new Date();
        const experience = Math.floor((today - hireDate) / (365.25 * 24 * 60 * 60 * 1000));

        // تحديد لون الحالة
        const statusClass = getEmployeeStatusClass(employee.status);

        // البحث عن حساب المستخدم
        const userAccount = appData.users.find(u => u.employeeId === employee.id);

        row.innerHTML = `
            <td>
                <div class="employee-info">
                    <strong>${employee.name}</strong>
                    <br><small><i class="fas fa-id-card"></i> ${employee.idNumber}</small>
                </div>
            </td>
            <td>${employee.position}</td>
            <td>${employee.department || 'غير محدد'}</td>
            <td>
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> ${employee.phone}</span>
                    ${employee.email ? `<br><small><i class="fas fa-envelope"></i> ${employee.email}</small>` : ''}
                </div>
            </td>
            <td>${formatDate(employee.hireDate)}</td>
            <td>${experience} سنة</td>
            <td>${employee.salary ? employee.salary.toLocaleString() + ' د.ج' : 'غير محدد'}</td>
            <td><span class="${statusClass}">${employee.status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-secondary" onclick="editEmployee('${employee.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-info" onclick="viewEmployeeDetails('${employee.id}')" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${userAccount ? `
                        <button class="btn btn-sm btn-warning" onclick="manageUserAccount('${userAccount.id}')" title="إدارة الحساب">
                            <i class="fas fa-user-cog"></i>
                        </button>
                    ` : `
                        <button class="btn btn-sm btn-success" onclick="createUserAccount('${employee.id}')" title="إنشاء حساب">
                            <i class="fas fa-user-plus"></i>
                        </button>
                    `}
                    <button class="btn btn-sm btn-primary" onclick="recordAttendance('${employee.id}')" title="تسجيل الحضور">
                        <i class="fas fa-clock"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${employee.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
    });
}

// تحديد فئة CSS لحالة الموظف
function getEmployeeStatusClass(status) {
    switch (status) {
        case 'نشط':
            return 'status-active';
        case 'إجازة':
            return 'status-pending';
        case 'معلق':
            return 'status-cancelled';
        case 'مستقيل':
            return 'status-expired';
        default:
            return 'status-default';
    }
}

// عرض تفاصيل الموظف
function viewEmployeeDetails(employeeId) {
    const employee = appData.employees.find(e => e.id === employeeId);
    if (!employee) return;

    const userAccount = appData.users.find(u => u.employeeId === employeeId);

    // حساب الإحصائيات
    const hireDate = new Date(employee.hireDate);
    const today = new Date();
    const workDays = Math.floor((today - hireDate) / (24 * 60 * 60 * 1000));
    const experience = Math.floor(workDays / 365.25);

    let detailsHTML = `
        <div class="employee-details">
            <div class="employee-header">
                <div class="employee-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div class="employee-basic-info">
                    <h2>${employee.name}</h2>
                    <p class="position">${employee.position} - ${employee.department || 'غير محدد'}</p>
                    <p class="status ${getEmployeeStatusClass(employee.status)}">${employee.status}</p>
                </div>
            </div>

            <div class="details-tabs">
                <button class="tab-btn active" onclick="showTab('personal-info', this)">المعلومات الشخصية</button>
                <button class="tab-btn" onclick="showTab('work-info', this)">معلومات العمل</button>
                ${userAccount ? '<button class="tab-btn" onclick="showTab(\'account-info\', this)">معلومات الحساب</button>' : ''}
                <button class="tab-btn" onclick="showTab('statistics', this)">الإحصائيات</button>
            </div>

            <div id="personal-info" class="tab-content active">
                <h3>المعلومات الشخصية</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <label>رقم الهوية:</label>
                        <span>${employee.idNumber}</span>
                    </div>
                    <div class="info-item">
                        <label>الهاتف:</label>
                        <span>${employee.phone}</span>
                    </div>
                    <div class="info-item">
                        <label>البريد الإلكتروني:</label>
                        <span>${employee.email || 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>العنوان:</label>
                        <span>${employee.address || 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>تاريخ الميلاد:</label>
                        <span>${employee.birthDate ? formatDate(employee.birthDate) : 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>الجنس:</label>
                        <span>${employee.gender}</span>
                    </div>
                </div>
            </div>

            <div id="work-info" class="tab-content">
                <h3>معلومات العمل</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <label>المنصب:</label>
                        <span>${employee.position}</span>
                    </div>
                    <div class="info-item">
                        <label>القسم:</label>
                        <span>${employee.department || 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>تاريخ التوظيف:</label>
                        <span>${formatDate(employee.hireDate)}</span>
                    </div>
                    <div class="info-item">
                        <label>سنوات الخبرة:</label>
                        <span>${experience} سنة</span>
                    </div>
                    <div class="info-item">
                        <label>الراتب الأساسي:</label>
                        <span>${employee.salary ? employee.salary.toLocaleString() + ' د.ج' : 'غير محدد'}</span>
                    </div>
                    <div class="info-item">
                        <label>ساعات العمل اليومية:</label>
                        <span>${employee.workHours} ساعة</span>
                    </div>
                </div>
            </div>
    `;

    if (userAccount) {
        detailsHTML += `
            <div id="account-info" class="tab-content">
                <h3>معلومات الحساب</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <label>اسم المستخدم:</label>
                        <span>${userAccount.username}</span>
                    </div>
                    <div class="info-item">
                        <label>الدور:</label>
                        <span>${getUserRoleName(userAccount.role)}</span>
                    </div>
                    <div class="info-item">
                        <label>حالة الحساب:</label>
                        <span class="${userAccount.isActive ? 'status-active' : 'status-cancelled'}">
                            ${userAccount.isActive ? 'نشط' : 'معطل'}
                        </span>
                    </div>
                    <div class="info-item">
                        <label>آخر دخول:</label>
                        <span>${userAccount.lastLogin ? formatDate(userAccount.lastLogin) : 'لم يدخل بعد'}</span>
                    </div>
                    <div class="info-item full-width">
                        <label>الصلاحيات:</label>
                        <div class="permissions-list">
                            ${userAccount.permissions.map(p => `<span class="permission-tag">${getPermissionName(p)}</span>`).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    detailsHTML += `
            <div id="statistics" class="tab-content">
                <h3>الإحصائيات</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="stat-info">
                            <h4>${workDays}</h4>
                            <p>يوم عمل</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h4>${(workDays * employee.workHours).toLocaleString()}</h4>
                            <p>ساعة عمل</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h4>${employee.salary ? (employee.salary * 12).toLocaleString() : '0'}</h4>
                            <p>راتب سنوي (د.ج)</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="employee-actions">
                <button class="btn btn-primary" onclick="editEmployee('${employee.id}')">
                    <i class="fas fa-edit"></i> تعديل البيانات
                </button>
                ${!userAccount ? `
                    <button class="btn btn-success" onclick="createUserAccount('${employee.id}')">
                        <i class="fas fa-user-plus"></i> إنشاء حساب
                    </button>
                ` : ''}
                <button class="btn btn-info" onclick="generateEmployeeReport('${employee.id}')">
                    <i class="fas fa-file-alt"></i> تقرير الموظف
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('تفاصيل الموظف', detailsHTML);
}

// الحصول على اسم الدور
function getUserRoleName(role) {
    const roles = {
        'employee': 'موظف عادي',
        'supervisor': 'مشرف',
        'manager': 'مدير',
        'admin': 'مدير النظام'
    };
    return roles[role] || role;
}

// الحصول على اسم الصلاحية
function getPermissionName(permission) {
    const permissions = {
        'customers': 'إدارة الزبائن',
        'cards': 'إدارة البطاقات',
        'appointments': 'إدارة المواعيد',
        'transmission': 'جدول الإرسال',
        'inventory': 'إدارة المخزون',
        'suppliers': 'إدارة الموردين',
        'sales': 'إدارة المبيعات',
        'reports': 'التقارير',
        'settings': 'الإعدادات'
    };
    return permissions[permission] || permission;
}

// ==================== نظام تسجيل الحضور ====================

// تسجيل الحضور والانصراف
function recordAttendance(employeeId) {
    const employee = appData.employees.find(e => e.id === employeeId);
    if (!employee) return;

    const today = new Date().toISOString().split('T')[0];

    // البحث عن سجل حضور اليوم
    if (!appData.attendance) appData.attendance = [];

    let todayRecord = appData.attendance.find(a =>
        a.employeeId === employeeId && a.date === today
    );

    const attendanceForm = `
        <div class="attendance-form">
            <div class="employee-info">
                <h3><i class="fas fa-user"></i> ${employee.name}</h3>
                <p>${employee.position} - ${employee.department || ''}</p>
                <p class="date">التاريخ: ${formatDate(today)}</p>
            </div>

            ${todayRecord ? `
                <div class="current-status">
                    <h4>حالة اليوم:</h4>
                    <div class="status-grid">
                        <div class="status-item">
                            <label>وقت الحضور:</label>
                            <span class="time">${todayRecord.checkIn || 'لم يسجل'}</span>
                        </div>
                        <div class="status-item">
                            <label>وقت الانصراف:</label>
                            <span class="time">${todayRecord.checkOut || 'لم يسجل'}</span>
                        </div>
                        <div class="status-item">
                            <label>ساعات العمل:</label>
                            <span class="hours">${calculateWorkHours(todayRecord)}</span>
                        </div>
                        <div class="status-item">
                            <label>الحالة:</label>
                            <span class="status ${getAttendanceStatusClass(todayRecord)}">${getAttendanceStatus(todayRecord)}</span>
                        </div>
                    </div>
                </div>
            ` : ''}

            <div class="attendance-actions">
                ${!todayRecord || !todayRecord.checkIn ? `
                    <button class="btn btn-success btn-large" onclick="checkIn('${employeeId}')">
                        <i class="fas fa-sign-in-alt"></i> تسجيل الحضور
                    </button>
                ` : ''}

                ${todayRecord && todayRecord.checkIn && !todayRecord.checkOut ? `
                    <button class="btn btn-warning btn-large" onclick="checkOut('${employeeId}')">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الانصراف
                    </button>
                ` : ''}

                <button class="btn btn-info" onclick="viewAttendanceHistory('${employeeId}')">
                    <i class="fas fa-history"></i> سجل الحضور
                </button>

                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>

            <div class="manual-entry">
                <h4>تسجيل يدوي:</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="manual-check-in">وقت الحضور:</label>
                        <input type="time" id="manual-check-in" value="${todayRecord?.checkIn || ''}">
                    </div>
                    <div class="form-group">
                        <label for="manual-check-out">وقت الانصراف:</label>
                        <input type="time" id="manual-check-out" value="${todayRecord?.checkOut || ''}">
                    </div>
                </div>
                <div class="form-group">
                    <label for="attendance-notes">ملاحظات:</label>
                    <textarea id="attendance-notes" rows="2" placeholder="أي ملاحظات...">${todayRecord?.notes || ''}</textarea>
                </div>
                <button class="btn btn-primary" onclick="saveManualAttendance('${employeeId}')">
                    <i class="fas fa-save"></i> حفظ التسجيل اليدوي
                </button>
            </div>
        </div>
    `;

    showModal('تسجيل الحضور والانصراف', attendanceForm);
}

// تسجيل الحضور
function checkIn(employeeId) {
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const time = now.toTimeString().split(' ')[0].substring(0, 5);

    if (!appData.attendance) appData.attendance = [];

    let record = appData.attendance.find(a =>
        a.employeeId === employeeId && a.date === today
    );

    if (!record) {
        record = {
            id: generateId(),
            employeeId: employeeId,
            date: today,
            checkIn: null,
            checkOut: null,
            notes: '',
            createdAt: new Date().toISOString()
        };
        appData.attendance.push(record);
    }

    record.checkIn = time;
    saveData();

    const employee = appData.employees.find(e => e.id === employeeId);
    showToast(`تم تسجيل حضور ${employee.name} في ${time}`);
    addNotification('⏰ تسجيل حضور', `${employee.name} - ${time}`, 'info');

    // إعادة فتح نافذة الحضور
    recordAttendance(employeeId);
}

// تسجيل الانصراف
function checkOut(employeeId) {
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const time = now.toTimeString().split(' ')[0].substring(0, 5);

    const record = appData.attendance.find(a =>
        a.employeeId === employeeId && a.date === today
    );

    if (record) {
        record.checkOut = time;
        saveData();

        const employee = appData.employees.find(e => e.id === employeeId);
        const workHours = calculateWorkHours(record);

        showToast(`تم تسجيل انصراف ${employee.name} في ${time} (${workHours})`);
        addNotification('⏰ تسجيل انصراف', `${employee.name} - ${time} - ${workHours}`, 'info');

        // إعادة فتح نافذة الحضور
        recordAttendance(employeeId);
    }
}

// حفظ التسجيل اليدوي
function saveManualAttendance(employeeId) {
    const today = new Date().toISOString().split('T')[0];
    const checkIn = document.getElementById('manual-check-in').value;
    const checkOut = document.getElementById('manual-check-out').value;
    const notes = document.getElementById('attendance-notes').value;

    if (!appData.attendance) appData.attendance = [];

    let record = appData.attendance.find(a =>
        a.employeeId === employeeId && a.date === today
    );

    if (!record) {
        record = {
            id: generateId(),
            employeeId: employeeId,
            date: today,
            checkIn: null,
            checkOut: null,
            notes: '',
            createdAt: new Date().toISOString()
        };
        appData.attendance.push(record);
    }

    record.checkIn = checkIn || record.checkIn;
    record.checkOut = checkOut || record.checkOut;
    record.notes = notes;
    record.isManual = true;

    saveData();

    const employee = appData.employees.find(e => e.id === employeeId);
    showToast(`تم حفظ سجل حضور ${employee.name}`);

    recordAttendance(employeeId);
}

// حساب ساعات العمل
function calculateWorkHours(record) {
    if (!record.checkIn || !record.checkOut) return '0:00';

    const checkIn = new Date(`2000-01-01T${record.checkIn}:00`);
    const checkOut = new Date(`2000-01-01T${record.checkOut}:00`);

    const diffMs = checkOut - checkIn;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    return `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
}

// الحصول على حالة الحضور
function getAttendanceStatus(record) {
    if (!record.checkIn) return 'لم يحضر';
    if (!record.checkOut) return 'في العمل';

    const workHours = calculateWorkHours(record);
    const [hours] = workHours.split(':').map(Number);

    if (hours >= 8) return 'مكتمل';
    if (hours >= 6) return 'ناقص';
    return 'غير مكتمل';
}

// فئة CSS لحالة الحضور
function getAttendanceStatusClass(record) {
    const status = getAttendanceStatus(record);
    switch (status) {
        case 'مكتمل': return 'status-active';
        case 'في العمل': return 'status-pending';
        case 'ناقص': return 'status-warning';
        case 'غير مكتمل': return 'status-cancelled';
        default: return 'status-default';
    }
}

// عرض سجل الحضور
function viewAttendanceHistory(employeeId) {
    const employee = appData.employees.find(e => e.id === employeeId);
    if (!employee) return;

    const attendanceRecords = (appData.attendance || [])
        .filter(a => a.employeeId === employeeId)
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 30); // آخر 30 يوم

    let historyHTML = `
        <div class="attendance-history">
            <div class="history-header">
                <h3><i class="fas fa-history"></i> سجل حضور ${employee.name}</h3>
                <p>آخر 30 يوم</p>
            </div>

            <div class="attendance-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <label>أيام الحضور:</label>
                        <span>${attendanceRecords.filter(r => r.checkIn).length}</span>
                    </div>
                    <div class="stat-item">
                        <label>أيام مكتملة:</label>
                        <span>${attendanceRecords.filter(r => getAttendanceStatus(r) === 'مكتمل').length}</span>
                    </div>
                    <div class="stat-item">
                        <label>متوسط ساعات العمل:</label>
                        <span>${calculateAverageWorkHours(attendanceRecords)}</span>
                    </div>
                </div>
            </div>

            <div class="attendance-table">
                <table>
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الحضور</th>
                            <th>الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>الحالة</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    if (attendanceRecords.length === 0) {
        historyHTML += '<tr><td colspan="6" class="no-data">لا يوجد سجل حضور</td></tr>';
    } else {
        attendanceRecords.forEach(record => {
            historyHTML += `
                <tr>
                    <td>${formatDate(record.date)}</td>
                    <td class="time">${record.checkIn || '-'}</td>
                    <td class="time">${record.checkOut || '-'}</td>
                    <td class="hours">${calculateWorkHours(record)}</td>
                    <td><span class="${getAttendanceStatusClass(record)}">${getAttendanceStatus(record)}</span></td>
                    <td class="notes">${record.notes || '-'}</td>
                </tr>
            `;
        });
    }

    historyHTML += `
                    </tbody>
                </table>
            </div>

            <div class="history-actions">
                <button class="btn btn-primary" onclick="exportAttendanceReport('${employeeId}')">
                    <i class="fas fa-file-export"></i> تصدير التقرير
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('سجل الحضور', historyHTML);
}

// حساب متوسط ساعات العمل
function calculateAverageWorkHours(records) {
    const validRecords = records.filter(r => r.checkIn && r.checkOut);
    if (validRecords.length === 0) return '0:00';

    let totalMinutes = 0;
    validRecords.forEach(record => {
        const workHours = calculateWorkHours(record);
        const [hours, minutes] = workHours.split(':').map(Number);
        totalMinutes += hours * 60 + minutes;
    });

    const avgMinutes = Math.round(totalMinutes / validRecords.length);
    const avgHours = Math.floor(avgMinutes / 60);
    const remainingMinutes = avgMinutes % 60;

    return `${avgHours}:${remainingMinutes.toString().padStart(2, '0')}`;
}

// ==================== نظام إدارة الرواتب ====================

// عرض نظام الرواتب
function showPayroll() {
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM

    let payrollHTML = `
        <div class="payroll-system">
            <div class="payroll-header">
                <h3><i class="fas fa-money-bill-wave"></i> نظام إدارة الرواتب</h3>
                <div class="month-selector">
                    <label for="payroll-month">الشهر:</label>
                    <input type="month" id="payroll-month" value="${currentMonth}" onchange="updatePayrollMonth()">
                    <button class="btn btn-primary" onclick="calculateMonthlyPayroll()">
                        <i class="fas fa-calculator"></i> حساب الرواتب
                    </button>
                </div>
            </div>

            <div class="payroll-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-info">
                            <h4 id="payroll-employees-count">0</h4>
                            <p>موظف</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="card-info">
                            <h4 id="total-work-hours">0</h4>
                            <p>ساعة عمل</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">
                            <i class="fas fa-money-bill"></i>
                        </div>
                        <div class="card-info">
                            <h4 id="total-basic-salary">0</h4>
                            <p>راتب أساسي</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="card-info">
                            <h4 id="total-net-salary">0</h4>
                            <p>صافي الراتب</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="payroll-table-container">
                <table id="payroll-table" class="payroll-table">
                    <thead>
                        <tr>
                            <th>الموظف</th>
                            <th>المنصب</th>
                            <th>أيام العمل</th>
                            <th>ساعات العمل</th>
                            <th>الراتب الأساسي</th>
                            <th>العلاوات</th>
                            <th>الخصومات</th>
                            <th>صافي الراتب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="payroll-tbody">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="payroll-actions">
                <button class="btn btn-success" onclick="generatePayrollReport()">
                    <i class="fas fa-file-alt"></i> تقرير الرواتب
                </button>
                <button class="btn btn-info" onclick="exportPayrollToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
                <button class="btn btn-warning" onclick="processPayroll()">
                    <i class="fas fa-credit-card"></i> معالجة الرواتب
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('نظام إدارة الرواتب', payrollHTML);
    calculateMonthlyPayroll();
}

// حساب رواتب الشهر
function calculateMonthlyPayroll() {
    const selectedMonth = document.getElementById('payroll-month').value;
    const [year, month] = selectedMonth.split('-');

    // الحصول على أيام الشهر
    const daysInMonth = new Date(year, month, 0).getDate();
    const workingDays = getWorkingDaysInMonth(year, month);

    const payrollData = [];
    let totalBasicSalary = 0;
    let totalNetSalary = 0;
    let totalWorkHours = 0;

    appData.employees.forEach(employee => {
        if (employee.status !== 'نشط') return;

        // حساب أيام وساعات العمل للموظف
        const employeeAttendance = getEmployeeAttendanceForMonth(employee.id, year, month);
        const workDays = employeeAttendance.length;
        const workHours = employeeAttendance.reduce((total, record) => {
            if (record.checkIn && record.checkOut) {
                const hours = calculateWorkHoursDecimal(record);
                return total + hours;
            }
            return total;
        }, 0);

        // حساب الراتب
        const dailySalary = employee.salary / workingDays;
        const basicSalary = dailySalary * workDays;

        // العلاوات والخصومات
        const allowances = calculateAllowances(employee, workDays, workHours);
        const deductions = calculateDeductions(employee, workDays, workHours);
        const netSalary = basicSalary + allowances - deductions;

        const payrollRecord = {
            employeeId: employee.id,
            employeeName: employee.name,
            position: employee.position,
            workDays: workDays,
            workHours: Math.round(workHours * 100) / 100,
            basicSalary: Math.round(basicSalary),
            allowances: Math.round(allowances),
            deductions: Math.round(deductions),
            netSalary: Math.round(netSalary),
            status: workDays >= workingDays * 0.8 ? 'مكتمل' : 'ناقص'
        };

        payrollData.push(payrollRecord);
        totalBasicSalary += basicSalary;
        totalNetSalary += netSalary;
        totalWorkHours += workHours;
    });

    // تحديث الملخص
    document.getElementById('payroll-employees-count').textContent = payrollData.length;
    document.getElementById('total-work-hours').textContent = Math.round(totalWorkHours);
    document.getElementById('total-basic-salary').textContent = Math.round(totalBasicSalary).toLocaleString() + ' د.ج';
    document.getElementById('total-net-salary').textContent = Math.round(totalNetSalary).toLocaleString() + ' د.ج';

    // تحديث الجدول
    updatePayrollTable(payrollData);
}

// تحديث جدول الرواتب
function updatePayrollTable(payrollData) {
    const tbody = document.getElementById('payroll-tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    payrollData.forEach(record => {
        const row = tbody.insertRow();
        const statusClass = record.status === 'مكتمل' ? 'status-active' : 'status-warning';

        row.innerHTML = `
            <td>
                <div class="employee-info">
                    <strong>${record.employeeName}</strong>
                </div>
            </td>
            <td>${record.position}</td>
            <td>${record.workDays}</td>
            <td>${record.workHours}</td>
            <td>${record.basicSalary.toLocaleString()} د.ج</td>
            <td class="allowances">${record.allowances.toLocaleString()} د.ج</td>
            <td class="deductions">${record.deductions.toLocaleString()} د.ج</td>
            <td class="net-salary"><strong>${record.netSalary.toLocaleString()} د.ج</strong></td>
            <td><span class="${statusClass}">${record.status}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info" onclick="viewPayrollDetails('${record.employeeId}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="generatePayslip('${record.employeeId}')">
                        <i class="fas fa-receipt"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="editPayrollRecord('${record.employeeId}')">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </td>
        `;
    });
}

// حساب أيام العمل في الشهر
function getWorkingDaysInMonth(year, month) {
    const daysInMonth = new Date(year, month, 0).getDate();
    let workingDays = 0;

    for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month - 1, day);
        const dayOfWeek = date.getDay();
        // استبعاد الجمعة (5) والسبت (6)
        if (dayOfWeek !== 5 && dayOfWeek !== 6) {
            workingDays++;
        }
    }

    return workingDays;
}

// الحصول على حضور الموظف للشهر
function getEmployeeAttendanceForMonth(employeeId, year, month) {
    if (!appData.attendance) return [];

    return appData.attendance.filter(record => {
        if (record.employeeId !== employeeId) return false;

        const recordDate = new Date(record.date);
        return recordDate.getFullYear() == year && (recordDate.getMonth() + 1) == month;
    });
}

// حساب ساعات العمل كرقم عشري
function calculateWorkHoursDecimal(record) {
    if (!record.checkIn || !record.checkOut) return 0;

    const checkIn = new Date(`2000-01-01T${record.checkIn}:00`);
    const checkOut = new Date(`2000-01-01T${record.checkOut}:00`);

    const diffMs = checkOut - checkIn;
    return diffMs / (1000 * 60 * 60); // تحويل إلى ساعات
}

// حساب العلاوات
function calculateAllowances(employee, workDays, workHours) {
    let allowances = 0;

    // علاوة الحضور المنتظم (إذا حضر جميع أيام العمل)
    const workingDays = getWorkingDaysInMonth(new Date().getFullYear(), new Date().getMonth() + 1);
    if (workDays >= workingDays) {
        allowances += employee.salary * 0.1; // 10% علاوة حضور
    }

    // علاوة الساعات الإضافية (أكثر من 8 ساعات يومياً)
    const standardHours = workDays * 8;
    if (workHours > standardHours) {
        const overtimeHours = workHours - standardHours;
        const hourlyRate = employee.salary / (workingDays * 8);
        allowances += overtimeHours * hourlyRate * 1.5; // 150% للساعات الإضافية
    }

    return allowances;
}

// حساب الخصومات
function calculateDeductions(employee, workDays, workHours) {
    let deductions = 0;

    // خصم التأمين الاجتماعي (9%)
    deductions += employee.salary * 0.09;

    // خصم الضريبة على الدخل (حسب الشرائح)
    if (employee.salary > 30000) {
        deductions += (employee.salary - 30000) * 0.2; // 20% للراتب الذي يزيد عن 30000
    }

    // خصم الغياب
    const workingDays = getWorkingDaysInMonth(new Date().getFullYear(), new Date().getMonth() + 1);
    const absentDays = workingDays - workDays;
    if (absentDays > 0) {
        const dailySalary = employee.salary / workingDays;
        deductions += absentDays * dailySalary;
    }

    return deductions;
}

// ==================== نظام إدارة الصيانة والخدمات ====================

// إضافة طلب صيانة جديد
function addMaintenanceRequest() {
    const maintenanceForm = `
        <div class="comprehensive-form-container">
            <form id="maintenance-form" class="comprehensive-form">
                <div class="form-header">
                    <h3><i class="fas fa-tools"></i> طلب صيانة جديد</h3>
                </div>

                <!-- معلومات الزبون والسيارة -->
                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="maintenance-customer">الزبون: *</label>
                            <select id="maintenance-customer" required onchange="loadCustomerVehicles()">
                                <option value="">اختر الزبون</option>
                                ${appData.customers.map(c => `<option value="${c.id}">${c.name} - ${c.phone}</option>`).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-vehicle">السيارة:</label>
                            <select id="maintenance-vehicle">
                                <option value="">اختر السيارة</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="maintenance-type">نوع الصيانة: *</label>
                            <select id="maintenance-type" required>
                                <option value="">اختر نوع الصيانة</option>
                                <option value="صيانة دورية">صيانة دورية</option>
                                <option value="إصلاح عطل">إصلاح عطل</option>
                                <option value="استبدال قطع">استبدال قطع</option>
                                <option value="فحص أمان">فحص أمان</option>
                                <option value="تنظيف نظام">تنظيف نظام</option>
                                <option value="معايرة">معايرة</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-priority">الأولوية:</label>
                            <select id="maintenance-priority">
                                <option value="عادية">عادية</option>
                                <option value="عالية">عالية</option>
                                <option value="عاجلة">عاجلة</option>
                                <option value="طارئة">طارئة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل المشكلة -->
                <div class="form-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> تفاصيل المشكلة</h3>
                </div>

                <div class="form-section">
                    <div class="form-group">
                        <label for="maintenance-description">وصف المشكلة: *</label>
                        <textarea id="maintenance-description" rows="4" required placeholder="اشرح المشكلة بالتفصيل..."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="maintenance-symptoms">الأعراض:</label>
                            <div class="checkbox-group">
                                <label><input type="checkbox" value="تسريب غاز"> تسريب غاز</label>
                                <label><input type="checkbox" value="رائحة غاز"> رائحة غاز</label>
                                <label><input type="checkbox" value="صوت غريب"> صوت غريب</label>
                                <label><input type="checkbox" value="عدم عمل النظام"> عدم عمل النظام</label>
                                <label><input type="checkbox" value="ضعف الأداء"> ضعف الأداء</label>
                                <label><input type="checkbox" value="مشاكل في الإشعال"> مشاكل في الإشعال</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="maintenance-location">موقع المشكلة:</label>
                            <select id="maintenance-location">
                                <option value="">حدد الموقع</option>
                                <option value="خزان الغاز">خزان الغاز</option>
                                <option value="أنابيب التوصيل">أنابيب التوصيل</option>
                                <option value="منظم الضغط">منظم الضغط</option>
                                <option value="حاقن الغاز">حاقن الغاز</option>
                                <option value="وحدة التحكم">وحدة التحكم</option>
                                <option value="مفتاح التبديل">مفتاح التبديل</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-urgency">مستوى الخطورة:</label>
                            <select id="maintenance-urgency">
                                <option value="منخفض">منخفض</option>
                                <option value="متوسط">متوسط</option>
                                <option value="عالي">عالي</option>
                                <option value="خطر">خطر</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- معلومات الجدولة -->
                <div class="form-header">
                    <h3><i class="fas fa-calendar-alt"></i> معلومات الجدولة</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="maintenance-date">تاريخ الصيانة المطلوب:</label>
                            <input type="date" id="maintenance-date" min="${new Date().toISOString().split('T')[0]}">
                        </div>
                        <div class="form-group">
                            <label for="maintenance-time">الوقت المفضل:</label>
                            <select id="maintenance-time">
                                <option value="">اختر الوقت</option>
                                <option value="08:00">08:00 صباحاً</option>
                                <option value="09:00">09:00 صباحاً</option>
                                <option value="10:00">10:00 صباحاً</option>
                                <option value="11:00">11:00 صباحاً</option>
                                <option value="14:00">02:00 مساءً</option>
                                <option value="15:00">03:00 مساءً</option>
                                <option value="16:00">04:00 مساءً</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="maintenance-technician">الفني المطلوب:</label>
                            <select id="maintenance-technician">
                                <option value="">أي فني متاح</option>
                                ${appData.employees.filter(e => e.position.includes('فني')).map(e =>
                                    `<option value="${e.id}">${e.name} - ${e.position}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-estimated-duration">المدة المتوقعة (ساعة):</label>
                            <select id="maintenance-estimated-duration">
                                <option value="1">ساعة واحدة</option>
                                <option value="2">ساعتان</option>
                                <option value="3">3 ساعات</option>
                                <option value="4">4 ساعات</option>
                                <option value="8">يوم كامل</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="maintenance-notes">ملاحظات إضافية:</label>
                        <textarea id="maintenance-notes" rows="3" placeholder="أي ملاحظات أو تعليمات خاصة..."></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-large">
                        <i class="fas fa-save"></i> إنشاء طلب الصيانة
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;

    showModal('طلب صيانة جديد', maintenanceForm);
    setupMaintenanceForm();
}

// إعداد نموذج الصيانة
function setupMaintenanceForm() {
    const form = document.getElementById('maintenance-form');
    if (!form) return;

    form.addEventListener('submit', (e) => {
        e.preventDefault();

        // جمع الأعراض المحددة
        const symptoms = Array.from(document.querySelectorAll('#maintenance-symptoms input:checked'))
            .map(cb => cb.value);

        const maintenanceData = {
            id: generateId(),
            customerId: document.getElementById('maintenance-customer').value,
            vehicleId: document.getElementById('maintenance-vehicle').value || null,
            type: document.getElementById('maintenance-type').value,
            priority: document.getElementById('maintenance-priority').value,
            description: document.getElementById('maintenance-description').value,
            symptoms: symptoms,
            location: document.getElementById('maintenance-location').value || null,
            urgency: document.getElementById('maintenance-urgency').value,
            scheduledDate: document.getElementById('maintenance-date').value || null,
            scheduledTime: document.getElementById('maintenance-time').value || null,
            assignedTechnician: document.getElementById('maintenance-technician').value || null,
            estimatedDuration: parseInt(document.getElementById('maintenance-estimated-duration').value) || 1,
            notes: document.getElementById('maintenance-notes').value || null,
            status: 'مجدول',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // إضافة الطلب
        if (!appData.maintenanceRequests) appData.maintenanceRequests = [];
        appData.maintenanceRequests.push(maintenanceData);

        // إنشاء موعد إذا تم تحديد تاريخ
        if (maintenanceData.scheduledDate) {
            const customer = appData.customers.find(c => c.id === maintenanceData.customerId);
            const appointment = {
                id: generateId(),
                customerId: maintenanceData.customerId,
                customerName: customer.name,
                vehicleNumber: customer.vehicleNumber || 'غير محدد',
                date: maintenanceData.scheduledDate,
                time: maintenanceData.scheduledTime || '09:00',
                serviceType: maintenanceData.type,
                priority: maintenanceData.priority,
                status: 'مجدول',
                notes: `طلب صيانة: ${maintenanceData.description}`,
                createdAt: new Date().toISOString()
            };

            appData.appointments.push(appointment);
        }

        saveData();
        updateAllTables();
        updateStats();
        hideModal();

        const customer = appData.customers.find(c => c.id === maintenanceData.customerId);
        addNotification('🔧 طلب صيانة جديد', `تم إنشاء طلب صيانة للزبون ${customer.name}`, 'info');
        showToast('تم إنشاء طلب الصيانة بنجاح!');
    });
}

// تحميل سيارات الزبون
function loadCustomerVehicles() {
    const customerSelect = document.getElementById('maintenance-customer');
    const vehicleSelect = document.getElementById('maintenance-vehicle');

    if (!customerSelect || !vehicleSelect) return;

    const customerId = customerSelect.value;
    vehicleSelect.innerHTML = '<option value="">اختر السيارة</option>';

    if (!customerId) return;

    // البحث عن سيارات الزبون
    const vehicles = appData.vehicles ? appData.vehicles.filter(v => v.customerId === customerId) : [];

    vehicles.forEach(vehicle => {
        const option = document.createElement('option');
        option.value = vehicle.id;
        option.textContent = `${vehicle.registrationNumber} - ${vehicle.brand} ${vehicle.model || ''}`;
        vehicleSelect.appendChild(option);
    });
}

// ==================== نظام إدارة المخزون المتقدم ====================

// إضافة صنف جديد للمخزون
function addAdvancedInventoryItem() {
    const inventoryForm = `
        <div class="comprehensive-form-container">
            <form id="advanced-inventory-form" class="comprehensive-form">
                <div class="form-header">
                    <h3><i class="fas fa-boxes"></i> إضافة صنف جديد للمخزون</h3>
                </div>

                <!-- المعلومات الأساسية -->
                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-name">اسم الصنف: *</label>
                            <input type="text" id="item-name" required placeholder="أدخل اسم الصنف">
                        </div>
                        <div class="form-group">
                            <label for="item-code">كود الصنف: *</label>
                            <input type="text" id="item-code" required placeholder="مثال: GAS-001">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-category">الفئة: *</label>
                            <select id="item-category" required>
                                <option value="">اختر الفئة</option>
                                <option value="خزانات غاز">خزانات غاز</option>
                                <option value="منظمات ضغط">منظمات ضغط</option>
                                <option value="أنابيب وخراطيم">أنابيب وخراطيم</option>
                                <option value="حاقنات غاز">حاقنات غاز</option>
                                <option value="وحدات تحكم">وحدات تحكم</option>
                                <option value="مفاتيح وأجهزة">مفاتيح وأجهزة</option>
                                <option value="قطع غيار">قطع غيار</option>
                                <option value="أدوات وعدد">أدوات وعدد</option>
                                <option value="مواد استهلاكية">مواد استهلاكية</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="item-subcategory">الفئة الفرعية:</label>
                            <input type="text" id="item-subcategory" placeholder="مثال: خزانات 60 لتر">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="item-description">الوصف:</label>
                        <textarea id="item-description" rows="3" placeholder="وصف تفصيلي للصنف..."></textarea>
                    </div>
                </div>

                <!-- معلومات المخزون -->
                <div class="form-header">
                    <h3><i class="fas fa-warehouse"></i> معلومات المخزون</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-quantity">الكمية الحالية: *</label>
                            <input type="number" id="item-quantity" required min="0" placeholder="0">
                        </div>
                        <div class="form-group">
                            <label for="item-unit">وحدة القياس:</label>
                            <select id="item-unit">
                                <option value="قطعة">قطعة</option>
                                <option value="متر">متر</option>
                                <option value="كيلوغرام">كيلوغرام</option>
                                <option value="لتر">لتر</option>
                                <option value="علبة">علبة</option>
                                <option value="مجموعة">مجموعة</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-min-quantity">الحد الأدنى: *</label>
                            <input type="number" id="item-min-quantity" required min="0" placeholder="5">
                        </div>
                        <div class="form-group">
                            <label for="item-max-quantity">الحد الأقصى:</label>
                            <input type="number" id="item-max-quantity" min="0" placeholder="100">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-location">موقع التخزين:</label>
                            <select id="item-location">
                                <option value="">اختر الموقع</option>
                                <option value="المخزن الرئيسي">المخزن الرئيسي</option>
                                <option value="مخزن قطع الغيار">مخزن قطع الغيار</option>
                                <option value="مخزن الخزانات">مخزن الخزانات</option>
                                <option value="ورشة العمل">ورشة العمل</option>
                                <option value="المكتب">المكتب</option>
                                <option value="السيارة">السيارة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="item-shelf">الرف/الموقع التفصيلي:</label>
                            <input type="text" id="item-shelf" placeholder="مثال: الرف A - الصف 3">
                        </div>
                    </div>
                </div>

                <!-- معلومات التكلفة -->
                <div class="form-header">
                    <h3><i class="fas fa-money-bill-wave"></i> معلومات التكلفة</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-cost-price">سعر التكلفة:</label>
                            <input type="number" id="item-cost-price" min="0" step="0.01" placeholder="0.00">
                        </div>
                        <div class="form-group">
                            <label for="item-selling-price">سعر البيع:</label>
                            <input type="number" id="item-selling-price" min="0" step="0.01" placeholder="0.00">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-profit-margin">هامش الربح (%):</label>
                            <input type="number" id="item-profit-margin" min="0" max="100" step="0.1" placeholder="20">
                        </div>
                        <div class="form-group">
                            <label for="item-tax-rate">معدل الضريبة (%):</label>
                            <input type="number" id="item-tax-rate" min="0" max="100" step="0.1" value="19" placeholder="19">
                        </div>
                    </div>
                </div>

                <!-- معلومات المورد -->
                <div class="form-header">
                    <h3><i class="fas fa-truck"></i> معلومات المورد</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-supplier">المورد الرئيسي:</label>
                            <select id="item-supplier">
                                <option value="">اختر المورد</option>
                                ${appData.suppliers.map(s => `<option value="${s.id}">${s.name}</option>`).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="item-supplier-code">كود المورد للصنف:</label>
                            <input type="text" id="item-supplier-code" placeholder="كود الصنف عند المورد">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-lead-time">مدة التوريد (يوم):</label>
                            <input type="number" id="item-lead-time" min="0" placeholder="7">
                        </div>
                        <div class="form-group">
                            <label for="item-min-order">الحد الأدنى للطلب:</label>
                            <input type="number" id="item-min-order" min="1" placeholder="1">
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="form-header">
                    <h3><i class="fas fa-info-circle"></i> معلومات إضافية</h3>
                </div>

                <div class="form-section">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-barcode">الباركود:</label>
                            <input type="text" id="item-barcode" placeholder="رقم الباركود">
                        </div>
                        <div class="form-group">
                            <label for="item-serial">الرقم التسلسلي:</label>
                            <input type="text" id="item-serial" placeholder="الرقم التسلسلي للصنف">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="item-warranty">فترة الضمان (شهر):</label>
                            <input type="number" id="item-warranty" min="0" placeholder="12">
                        </div>
                        <div class="form-group">
                            <label for="item-expiry">تاريخ انتهاء الصلاحية:</label>
                            <input type="date" id="item-expiry">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="item-notes">ملاحظات:</label>
                        <textarea id="item-notes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-large">
                        <i class="fas fa-save"></i> إضافة الصنف للمخزون
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="hideModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    `;

    showModal('إضافة صنف جديد للمخزون', inventoryForm);
    setupAdvancedInventoryForm();
}

// إعداد نموذج المخزون المتقدم
function setupAdvancedInventoryForm() {
    const form = document.getElementById('advanced-inventory-form');
    if (!form) return;

    // حساب سعر البيع تلقائياً عند تغيير التكلفة أو هامش الربح
    const costPrice = document.getElementById('item-cost-price');
    const profitMargin = document.getElementById('item-profit-margin');
    const sellingPrice = document.getElementById('item-selling-price');

    function calculateSellingPrice() {
        const cost = parseFloat(costPrice.value) || 0;
        const margin = parseFloat(profitMargin.value) || 0;

        if (cost > 0 && margin > 0) {
            const selling = cost * (1 + margin / 100);
            sellingPrice.value = selling.toFixed(2);
        }
    }

    costPrice.addEventListener('input', calculateSellingPrice);
    profitMargin.addEventListener('input', calculateSellingPrice);

    form.addEventListener('submit', (e) => {
        e.preventDefault();

        const itemData = {
            id: generateId(),
            name: document.getElementById('item-name').value,
            code: document.getElementById('item-code').value,
            category: document.getElementById('item-category').value,
            subcategory: document.getElementById('item-subcategory').value || null,
            description: document.getElementById('item-description').value || null,
            quantity: parseInt(document.getElementById('item-quantity').value) || 0,
            unit: document.getElementById('item-unit').value,
            minQuantity: parseInt(document.getElementById('item-min-quantity').value) || 0,
            maxQuantity: parseInt(document.getElementById('item-max-quantity').value) || null,
            location: document.getElementById('item-location').value || null,
            shelf: document.getElementById('item-shelf').value || null,
            costPrice: parseFloat(document.getElementById('item-cost-price').value) || 0,
            sellingPrice: parseFloat(document.getElementById('item-selling-price').value) || 0,
            profitMargin: parseFloat(document.getElementById('item-profit-margin').value) || 0,
            taxRate: parseFloat(document.getElementById('item-tax-rate').value) || 0,
            supplierId: document.getElementById('item-supplier').value || null,
            supplierCode: document.getElementById('item-supplier-code').value || null,
            leadTime: parseInt(document.getElementById('item-lead-time').value) || 0,
            minOrder: parseInt(document.getElementById('item-min-order').value) || 1,
            barcode: document.getElementById('item-barcode').value || null,
            serialNumber: document.getElementById('item-serial').value || null,
            warrantyPeriod: parseInt(document.getElementById('item-warranty').value) || 0,
            expiryDate: document.getElementById('item-expiry').value || null,
            notes: document.getElementById('item-notes').value || null,
            status: 'نشط',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        // إضافة الصنف للمخزون
        appData.inventory.push(itemData);

        // إضافة حركة مخزون أولية
        if (!appData.inventoryMovements) appData.inventoryMovements = [];

        if (itemData.quantity > 0) {
            appData.inventoryMovements.push({
                id: generateId(),
                itemId: itemData.id,
                type: 'إدخال',
                quantity: itemData.quantity,
                reason: 'رصيد افتتاحي',
                date: new Date().toISOString().split('T')[0],
                time: new Date().toTimeString().split(' ')[0].substring(0, 5),
                userId: appData.settings.currentUser.id,
                notes: 'إضافة صنف جديد للمخزون',
                createdAt: new Date().toISOString()
            });
        }

        saveData();
        updateAllTables();
        updateStats();
        hideModal();

        addNotification('📦 صنف جديد', `تم إضافة ${itemData.name} للمخزون`, 'success');
        showToast('تم إضافة الصنف للمخزون بنجاح!');
    });
}

// ==================== نظام التقارير المتقدم ====================

// عرض مركز التقارير
function showAdvancedReports() {
    const reportsHTML = `
        <div class="reports-center">
            <div class="reports-header">
                <h3><i class="fas fa-chart-bar"></i> مركز التقارير المتقدم</h3>
                <p>اختر نوع التقرير المطلوب</p>
            </div>

            <div class="reports-categories">
                <!-- تقارير العملاء -->
                <div class="report-category">
                    <div class="category-header">
                        <h4><i class="fas fa-users"></i> تقارير العملاء</h4>
                    </div>
                    <div class="reports-grid">
                        <div class="report-card" onclick="generateCustomersAnalyticsReport()">
                            <div class="report-icon">
                                <i class="fas fa-user-chart"></i>
                            </div>
                            <div class="report-info">
                                <h5>تحليل العملاء</h5>
                                <p>إحصائيات شاملة عن العملاء</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateCustomersGrowthReport()">
                            <div class="report-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="report-info">
                                <h5>نمو قاعدة العملاء</h5>
                                <p>تطور أعداد العملاء</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateCustomersSegmentationReport()">
                            <div class="report-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="report-info">
                                <h5>تصنيف العملاء</h5>
                                <p>تقسيم العملاء حسب الفئات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير الخدمات -->
                <div class="report-category">
                    <div class="category-header">
                        <h4><i class="fas fa-cogs"></i> تقارير الخدمات</h4>
                    </div>
                    <div class="reports-grid">
                        <div class="report-card" onclick="generateServicesPerformanceReport()">
                            <div class="report-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="report-info">
                                <h5>أداء الخدمات</h5>
                                <p>تحليل كفاءة الخدمات</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateMaintenanceReport()">
                            <div class="report-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="report-info">
                                <h5>تقرير الصيانة</h5>
                                <p>إحصائيات الصيانة والإصلاح</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateAppointmentsAnalysisReport()">
                            <div class="report-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="report-info">
                                <h5>تحليل المواعيد</h5>
                                <p>إحصائيات المواعيد والجدولة</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- التقارير المالية -->
                <div class="report-category">
                    <div class="category-header">
                        <h4><i class="fas fa-money-bill-wave"></i> التقارير المالية</h4>
                    </div>
                    <div class="reports-grid">
                        <div class="report-card" onclick="generateFinancialSummaryReport()">
                            <div class="report-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="report-info">
                                <h5>الملخص المالي</h5>
                                <p>نظرة شاملة على الوضع المالي</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateRevenueAnalysisReport()">
                            <div class="report-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="report-info">
                                <h5>تحليل الإيرادات</h5>
                                <p>مصادر الدخل والربحية</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateExpensesReport()">
                            <div class="report-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="report-info">
                                <h5>تقرير المصروفات</h5>
                                <p>تحليل التكاليف والمصروفات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير المخزون -->
                <div class="report-category">
                    <div class="category-header">
                        <h4><i class="fas fa-boxes"></i> تقارير المخزون</h4>
                    </div>
                    <div class="reports-grid">
                        <div class="report-card" onclick="generateInventoryValuationReport()">
                            <div class="report-icon">
                                <i class="fas fa-calculator"></i>
                            </div>
                            <div class="report-info">
                                <h5>تقييم المخزون</h5>
                                <p>قيمة المخزون الحالي</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateInventoryMovementReport()">
                            <div class="report-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="report-info">
                                <h5>حركة المخزون</h5>
                                <p>تتبع حركات الإدخال والإخراج</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateStockAlertsReport()">
                            <div class="report-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="report-info">
                                <h5>تنبيهات المخزون</h5>
                                <p>الأصناف التي تحتاج إعادة تموين</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تقارير الموظفين -->
                <div class="report-category">
                    <div class="category-header">
                        <h4><i class="fas fa-user-tie"></i> تقارير الموظفين</h4>
                    </div>
                    <div class="reports-grid">
                        <div class="report-card" onclick="generateEmployeesPerformanceReport()">
                            <div class="report-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="report-info">
                                <h5>أداء الموظفين</h5>
                                <p>تقييم أداء فريق العمل</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generateAttendanceAnalysisReport()">
                            <div class="report-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="report-info">
                                <h5>تحليل الحضور</h5>
                                <p>إحصائيات الحضور والانصراف</p>
                            </div>
                        </div>
                        <div class="report-card" onclick="generatePayrollSummaryReport()">
                            <div class="report-icon">
                                <i class="fas fa-money-check-alt"></i>
                            </div>
                            <div class="report-info">
                                <h5>ملخص الرواتب</h5>
                                <p>تحليل تكاليف الرواتب</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="reports-actions">
                <button class="btn btn-primary" onclick="generateComprehensiveReport()">
                    <i class="fas fa-file-alt"></i> تقرير شامل
                </button>
                <button class="btn btn-info" onclick="scheduleReport()">
                    <i class="fas fa-calendar-plus"></i> جدولة تقرير
                </button>
                <button class="btn btn-secondary" onclick="hideModal()">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    `;

    showModal('مركز التقارير المتقدم', reportsHTML);
}

// تقرير تحليل العملاء
function generateCustomersAnalyticsReport() {
    const totalCustomers = appData.customers.length;
    const activeCustomers = appData.customers.filter(c => c.status === 'نشط').length;
    const newCustomersThisMonth = appData.customers.filter(c => {
        const customerDate = new Date(c.createdAt);
        const now = new Date();
        return customerDate.getMonth() === now.getMonth() && customerDate.getFullYear() === now.getFullYear();
    }).length;

    // تحليل العملاء حسب المنطقة
    const customersByRegion = {};
    appData.customers.forEach(customer => {
        if (customer.address) {
            const region = customer.address.split(',')[0] || 'غير محدد';
            customersByRegion[region] = (customersByRegion[region] || 0) + 1;
        }
    });

    // تحليل العملاء حسب نوع الخدمة
    const serviceTypes = {};
    appData.appointments.forEach(appointment => {
        serviceTypes[appointment.serviceType] = (serviceTypes[appointment.serviceType] || 0) + 1;
    });

    let reportHTML = `
        <div class="analytics-report">
            <div class="report-header">
                <h2><i class="fas fa-user-chart"></i> تقرير تحليل العملاء</h2>
                <p class="report-date">تاريخ التقرير: ${formatDate(new Date().toISOString())}</p>
            </div>

            <div class="report-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-icon customers">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="card-content">
                            <h3>${totalCustomers}</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon active">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div class="card-content">
                            <h3>${activeCustomers}</h3>
                            <p>عملاء نشطين</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon new">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="card-content">
                            <h3>${newCustomersThisMonth}</h3>
                            <p>عملاء جدد هذا الشهر</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon percentage">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="card-content">
                            <h3>${totalCustomers > 0 ? Math.round((activeCustomers / totalCustomers) * 100) : 0}%</h3>
                            <p>معدل النشاط</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="report-sections">
                <div class="report-section">
                    <h3>توزيع العملاء حسب المنطقة</h3>
                    <div class="chart-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>المنطقة</th>
                                    <th>عدد العملاء</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.entries(customersByRegion).map(([region, count]) => `
                                    <tr>
                                        <td>${region}</td>
                                        <td>${count}</td>
                                        <td>${Math.round((count / totalCustomers) * 100)}%</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="report-section">
                    <h3>الخدمات الأكثر طلباً</h3>
                    <div class="chart-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>نوع الخدمة</th>
                                    <th>عدد الطلبات</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${Object.entries(serviceTypes).sort((a, b) => b[1] - a[1]).map(([service, count]) => `
                                    <tr>
                                        <td>${service}</td>
                                        <td>${count}</td>
                                        <td>${Math.round((count / Object.values(serviceTypes).reduce((a, b) => a + b, 0)) * 100)}%</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="report-actions">
                <button class="btn btn-primary" onclick="printReport()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-info" onclick="exportReportToPDF()">
                    <i class="fas fa-file-pdf"></i> تصدير PDF
                </button>
                <button class="btn btn-success" onclick="exportReportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير Excel
                </button>
            </div>
        </div>
    `;

    showModal('تقرير تحليل العملاء', reportHTML);
}

// اختبار بسيط للنظام
function testSystem() {
    console.log('🧪 اختبار النظام...');

    // اختبار الدوال الأساسية
    try {
        console.log('✅ addCustomer:', typeof addCustomer === 'function');
        console.log('✅ addCard:', typeof addCard === 'function');
        console.log('✅ addAppointment:', typeof addAppointment === 'function');
        console.log('✅ showAddTransmissionModal:', typeof showAddTransmissionModal === 'function');

        // اختبار العناصر
        console.log('✅ add-customer-btn:', !!document.getElementById('add-customer-btn'));
        console.log('✅ add-card-btn:', !!document.getElementById('add-card-btn'));
        console.log('✅ add-appointment-btn:', !!document.getElementById('add-appointment-btn'));

        // تشغيل التنبيهات الذكية
        checkSmartAlerts();

        showToast('النظام يعمل بشكل صحيح!', true);
    } catch (error) {
        console.error('❌ خطأ في النظام:', error);
        showToast('خطأ في النظام: ' + error.message, false);
    }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 تم تحميل DOM');

    // تأخير إضافي للتأكد من تحميل جميع العناصر
    setTimeout(() => {
        initApp();

        // اختبار النظام بعد التحميل
        setTimeout(() => {
            testSystem();

            // تشغيل التنبيهات الذكية كل 5 دقائق
            setInterval(() => {
                checkSmartAlerts();
            }, 5 * 60 * 1000);
        }, 1000);
    }, 200);
});
