<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية - مؤسسة وقود المستقبل</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; direction: rtl; }
        
        /* شاشة تسجيل الدخول */
        .login-screen { 
            position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex; align-items: center; justify-content: center; z-index: 1000;
        }
        .login-card { 
            background: white; padding: 40px; border-radius: 15px; 
            max-width: 400px; width: 90%; box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }
        .login-header { text-align: center; margin-bottom: 30px; }
        .login-header h1 { color: #2c3e50; margin-bottom: 10px; font-size: 1.8rem; }
        .login-header p { color: #7f8c8d; }
        
        .credentials-box { 
            background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; 
            padding: 15px; margin-bottom: 20px; font-size: 14px;
        }
        .credentials-box h3 { color: #1976d2; margin-bottom: 10px; }
        .credentials-box p { margin: 5px 0; font-family: monospace; color: #424242; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #2c3e50; }
        .form-group input { 
            width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; 
            font-size: 16px; transition: border-color 0.3s;
        }
        .form-group input:focus { outline: none; border-color: #667eea; }
        
        .btn { 
            width: 100%; padding: 15px; border: none; border-radius: 8px; 
            font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s;
        }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(102,126,234,0.3); }
        .btn-primary:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .btn-warning { background: #ffc107; color: #212529; margin-bottom: 15px; }
        .btn-warning:hover { background: #e0a800; }
        
        /* لوحة التحكم */
        .dashboard { display: none; min-height: 100vh; }
        .dashboard-header { 
            background: white; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex; justify-content: space-between; align-items: center;
        }
        .dashboard-title { color: #2c3e50; font-size: 1.5rem; }
        .btn-danger { background: #dc3545; color: white; padding: 10px 20px; border-radius: 5px; }
        .btn-danger:hover { background: #c82333; }
        
        .dashboard-content { padding: 30px; }
        .stats-grid { 
            display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; margin-bottom: 30px;
        }
        .stat-card { 
            background: white; padding: 25px; border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid #667eea;
        }
        .stat-card h3 { font-size: 2rem; color: #2c3e50; margin-bottom: 10px; }
        .stat-card p { color: #7f8c8d; font-size: 1rem; }
        
        .requests-section { 
            background: white; padding: 25px; border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section-title { 
            color: #2c3e50; font-size: 1.3rem; margin-bottom: 20px; 
            border-bottom: 2px solid #667eea; padding-bottom: 10px;
        }
        
        .request-item { 
            padding: 15px; border: 1px solid #e9ecef; border-radius: 8px; 
            margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;
        }
        .request-info h4 { color: #2c3e50; margin-bottom: 5px; }
        .request-info p { color: #6c757d; font-size: 0.9rem; }
        
        .status { padding: 5px 15px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        
        .hidden { display: none !important; }
        .message { 
            padding: 15px; border-radius: 5px; margin-bottom: 20px; text-align: center;
            display: none;
        }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .message.show { display: block; }
        
        .empty-state { 
            text-align: center; padding: 40px; color: #6c757d;
            font-size: 1.1rem;
        }
        .empty-state .icon { font-size: 3rem; margin-bottom: 15px; }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div class="login-screen" id="loginScreen">
        <div class="login-card">
            <div class="login-header">
                <h1>🛡️ لوحة التحكم الإدارية</h1>
                <p>مؤسسة وقود المستقبل</p>
            </div>

            <div class="credentials-box">
                <h3>📋 بيانات تسجيل الدخول:</h3>
                <p><strong>اسم المدير:</strong> developer</p>
                <p><strong>كلمة المرور:</strong> dev123456</p>
                <p><strong>المفتاح السري:</strong> FUTUREFUEL2024ADMIN</p>
            </div>

            <button class="btn btn-warning" onclick="autoFill()">📝 ملء البيانات تلقائياً</button>

            <div id="loginMessage" class="message"></div>

            <form id="loginForm">
                <div class="form-group">
                    <label>اسم المدير:</label>
                    <input type="text" id="username" required>
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" id="password" required>
                </div>
                <div class="form-group">
                    <label>المفتاح السري:</label>
                    <input type="text" id="secretKey" required>
                </div>
                <button type="submit" class="btn btn-primary" id="loginBtn">🔐 دخول لوحة التحكم</button>
            </form>
        </div>
    </div>

    <!-- لوحة التحكم -->
    <div class="dashboard hidden" id="dashboard">
        <header class="dashboard-header">
            <h1 class="dashboard-title">📊 لوحة التحكم الإدارية</h1>
            <button class="btn btn-danger" onclick="logout()">🚪 تسجيل الخروج</button>
        </header>

        <div class="dashboard-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalRequests">0</h3>
                    <p>إجمالي الطلبات</p>
                </div>
                <div class="stat-card">
                    <h3 id="pendingRequests">0</h3>
                    <p>طلبات معلقة</p>
                </div>
                <div class="stat-card">
                    <h3 id="approvedRequests">0</h3>
                    <p>طلبات مقبولة</p>
                </div>
                <div class="stat-card">
                    <h3 id="rejectedRequests">0</h3>
                    <p>طلبات مرفوضة</p>
                </div>
            </div>

            <div class="requests-section">
                <h2 class="section-title">📋 طلبات التفعيل</h2>
                <div id="requestsList"></div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المدير
        const ADMIN = { username: 'developer', password: 'dev123456', secretKey: 'FUTUREFUEL2024ADMIN' };
        let isLoggedIn = false;

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تحميل لوحة التحكم');
            checkAuth();
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
        });

        // فحص المصادقة
        function checkAuth() {
            const auth = localStorage.getItem('adminAuth');
            if (auth) {
                try {
                    const data = JSON.parse(auth);
                    const now = Date.now();
                    if (now - data.loginTime < 4 * 60 * 60 * 1000) { // 4 ساعات
                        showDashboard();
                        return;
                    }
                } catch (e) {}
                localStorage.removeItem('adminAuth');
            }
            showLogin();
        }

        // معالجة تسجيل الدخول
        async function handleLogin(e) {
            e.preventDefault();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const secretKey = document.getElementById('secretKey').value.trim();
            
            if (!username || !password || !secretKey) {
                showMessage('يرجى ملء جميع الحقول!', 'error');
                return;
            }

            const btn = document.getElementById('loginBtn');
            btn.disabled = true;
            btn.textContent = '🔄 جاري التحقق...';

            await new Promise(r => setTimeout(r, 1000)); // محاكاة تأخير

            if (username === ADMIN.username && password === ADMIN.password && secretKey === ADMIN.secretKey) {
                localStorage.setItem('adminAuth', JSON.stringify({ username, loginTime: Date.now() }));
                showMessage('✅ تم تسجيل الدخول بنجاح!', 'success');
                setTimeout(showDashboard, 1000);
            } else {
                showMessage('❌ بيانات المصادقة غير صحيحة!', 'error');
            }

            btn.disabled = false;
            btn.textContent = '🔐 دخول لوحة التحكم';
        }

        // إظهار شاشة تسجيل الدخول
        function showLogin() {
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('dashboard').classList.add('hidden');
        }

        // إظهار لوحة التحكم
        function showDashboard() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('dashboard').classList.remove('hidden');
            isLoggedIn = true;
            updateData();
        }

        // تحديث البيانات
        function updateData() {
            const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
            
            // إحصائيات
            const stats = {
                total: requests.length,
                pending: requests.filter(r => r.status === 'pending').length,
                approved: requests.filter(r => r.status === 'approved').length,
                rejected: requests.filter(r => r.status === 'rejected').length
            };

            document.getElementById('totalRequests').textContent = stats.total;
            document.getElementById('pendingRequests').textContent = stats.pending;
            document.getElementById('approvedRequests').textContent = stats.approved;
            document.getElementById('rejectedRequests').textContent = stats.rejected;

            // عرض الطلبات
            const container = document.getElementById('requestsList');
            if (requests.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">📭</div>
                        <p>لا توجد طلبات تفعيل حالياً</p>
                        <small>يمكنك إرسال طلب تفعيل من نظام تسجيل الدخول</small>
                    </div>
                `;
            } else {
                container.innerHTML = requests.map(req => `
                    <div class="request-item">
                        <div class="request-info">
                            <h4>${req.data.fullName}</h4>
                            <p>${req.data.email} • ${getTypeText(req.data.licenseType)} • ${new Date(req.createdAt).toLocaleDateString('ar')}</p>
                        </div>
                        <span class="status status-${req.status}">${getStatusText(req.status)}</span>
                    </div>
                `).join('');
            }
        }

        // نصوص الحالة والنوع
        function getStatusText(status) {
            return { pending: 'معلق', approved: 'مقبول', rejected: 'مرفوض', processing: 'قيد المعالجة' }[status] || status;
        }

        function getTypeText(type) {
            return { trial: 'تجريبي', basic: 'أساسي', professional: 'احترافي', enterprise: 'مؤسسي' }[type] || type;
        }

        // ملء البيانات تلقائياً
        function autoFill() {
            document.getElementById('username').value = ADMIN.username;
            document.getElementById('password').value = ADMIN.password;
            document.getElementById('secretKey').value = ADMIN.secretKey;
            showMessage('✅ تم ملء البيانات تلقائياً!', 'success');
        }

        // إظهار الرسائل
        function showMessage(text, type) {
            const msg = document.getElementById('loginMessage');
            msg.textContent = text;
            msg.className = `message ${type} show`;
            setTimeout(() => msg.classList.remove('show'), 5000);
        }

        // تسجيل الخروج
        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                localStorage.removeItem('adminAuth');
                isLoggedIn = false;
                showLogin();
            }
        }

        // تحديث البيانات كل 30 ثانية
        setInterval(() => { if (isLoggedIn) updateData(); }, 30000);
    </script>
</body>
</html>
