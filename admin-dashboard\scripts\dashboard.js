// متغيرات عامة
let currentSection = 'dashboard';
let isLoggedIn = false;
let adminData = {
    username: '',
    role: 'admin',
    loginTime: null
};

// بيانات لوحة التحكم
let dashboardData = {
    stats: {
        totalRequests: 0,
        pendingRequests: 0,
        approvedRequests: 0,
        rejectedRequests: 0
    },
    requests: [],
    users: [],
    licenses: [],
    notifications: []
};

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    setupEventListeners();
    checkAuthStatus();
});

// تهيئة لوحة التحكم
function initializeDashboard() {
    console.log('🚀 تم تحميل لوحة التحكم الإدارية');
    
    // تحميل البيانات المحفوظة
    loadDashboardData();
    
    // تحديث الواجهة
    updateDashboardStats();
    
    // تحميل الطلبات الأخيرة
    loadRecentRequests();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // نموذج تسجيل دخول المدير
    const adminLoginForm = document.getElementById('adminLoginForm');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', handleAdminLogin);
    }
    
    // روابط القائمة الجانبية
    const menuLinks = document.querySelectorAll('.menu-link');
    menuLinks.forEach(link => {
        link.addEventListener('click', handleMenuClick);
    });
    
    // البحث العام
    const globalSearch = document.getElementById('globalSearch');
    if (globalSearch) {
        globalSearch.addEventListener('input', handleGlobalSearch);
    }
    
    // فلاتر الطلبات
    setupFiltersListeners();
    
    // اختصارات لوحة المفاتيح
    setupKeyboardShortcuts();
}

// فحص حالة المصادقة
function checkAuthStatus() {
    const savedAuth = localStorage.getItem('adminAuth');
    if (savedAuth) {
        const authData = JSON.parse(savedAuth);
        const now = new Date().getTime();
        
        // فحص انتهاء الجلسة (4 ساعات)
        if (now - authData.loginTime < 4 * 60 * 60 * 1000) {
            adminData = authData;
            isLoggedIn = true;
            showDashboard();
            return;
        }
    }
    
    // إظهار شاشة تسجيل الدخول
    showAdminLogin();
}

// معالجة تسجيل دخول المدير
async function handleAdminLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('adminUsername').value;
    const password = document.getElementById('adminPassword').value;
    const secretKey = document.getElementById('adminSecretKey').value;
    
    // إظهار حالة التحميل
    showLoadingState('adminLoginForm');
    
    try {
        // التحقق من بيانات المدير
        const authResult = await authenticateAdmin(username, password, secretKey);
        
        if (authResult.success) {
            // حفظ بيانات المصادقة
            adminData = {
                username: username,
                role: 'admin',
                loginTime: new Date().getTime()
            };
            
            localStorage.setItem('adminAuth', JSON.stringify(adminData));
            isLoggedIn = true;
            
            // إظهار لوحة التحكم
            showDashboard();
            showToast('تم تسجيل الدخول بنجاح!', 'success');
            
        } else {
            throw new Error(authResult.message || 'فشل في تسجيل الدخول');
        }
        
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        showToast(error.message || 'خطأ في تسجيل الدخول', 'error');
    } finally {
        hideLoadingState('adminLoginForm');
    }
}

// مصادقة المدير
async function authenticateAdmin(username, password, secretKey) {
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // بيانات المدير الافتراضية (يجب تغييرها في الإنتاج)
    const adminCredentials = {
        username: 'developer',
        password: 'dev123456',
        secretKey: 'FUTUREFUEL2024ADMIN'
    };
    
    if (username === adminCredentials.username && 
        password === adminCredentials.password && 
        secretKey === adminCredentials.secretKey) {
        
        return {
            success: true,
            user: {
                username: username,
                role: 'admin',
                permissions: ['all']
            }
        };
    }
    
    return {
        success: false,
        message: 'بيانات المصادقة غير صحيحة'
    };
}

// إظهار شاشة تسجيل الدخول
function showAdminLogin() {
    document.getElementById('adminLoginOverlay').classList.remove('hidden');
    document.getElementById('dashboardContainer').classList.add('hidden');
}

// إظهار لوحة التحكم
function showDashboard() {
    document.getElementById('adminLoginOverlay').classList.add('hidden');
    document.getElementById('dashboardContainer').classList.remove('hidden');
    
    // تحديث البيانات
    refreshDashboard();
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('adminAuth');
        isLoggedIn = false;
        adminData = {};
        showAdminLogin();
        showToast('تم تسجيل الخروج بنجاح', 'info');
    }
}

// معالجة النقر على روابط القائمة
function handleMenuClick(event) {
    event.preventDefault();
    
    const link = event.currentTarget;
    const section = link.getAttribute('data-section');
    
    if (section) {
        showSection(section);
        updateActiveMenu(link);
    }
}

// إظهار قسم معين
function showSection(sectionId) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.dashboard-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    // إظهار القسم المطلوب
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionId;
        
        // تحميل بيانات القسم
        loadSectionData(sectionId);
    }
}

// تحديث القائمة النشطة
function updateActiveMenu(activeLink) {
    const menuLinks = document.querySelectorAll('.menu-link');
    menuLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    activeLink.classList.add('active');
}

// تحميل بيانات القسم
function loadSectionData(sectionId) {
    switch (sectionId) {
        case 'dashboard':
            refreshDashboard();
            break;
        case 'activation-requests':
            loadActivationRequests();
            break;
        case 'users-management':
            loadUsersManagement();
            break;
        case 'licenses-management':
            loadLicensesManagement();
            break;
        case 'analytics':
            loadAnalytics();
            break;
        case 'notifications':
            loadNotifications();
            break;
        case 'system-logs':
            loadSystemLogs();
            break;
        case 'settings':
            loadSettings();
            break;
    }
}

// تحديث لوحة التحكم
function refreshDashboard() {
    updateDashboardStats();
    loadRecentRequests();
    updateCharts();
    showToast('تم تحديث البيانات', 'success');
}

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    // تحميل طلبات التفعيل من التخزين المحلي
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    
    // حساب الإحصائيات
    const stats = {
        totalRequests: activationRequests.length,
        pendingRequests: activationRequests.filter(req => req.status === 'pending').length,
        approvedRequests: activationRequests.filter(req => req.status === 'approved').length,
        rejectedRequests: activationRequests.filter(req => req.status === 'rejected').length
    };
    
    // تحديث العناصر في الواجهة
    document.getElementById('totalRequests').textContent = stats.totalRequests;
    document.getElementById('pendingRequests').textContent = stats.pendingRequests;
    document.getElementById('approvedRequests').textContent = stats.approvedRequests;
    document.getElementById('rejectedRequests').textContent = stats.rejectedRequests;
    
    // تحديث شارات القائمة
    document.getElementById('requestsBadge').textContent = stats.pendingRequests;
    document.getElementById('notificationBadge').textContent = stats.pendingRequests;
    
    // حفظ الإحصائيات
    dashboardData.stats = stats;
}

// تحميل الطلبات الأخيرة
function loadRecentRequests() {
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    const recentRequests = activationRequests
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);
    
    const container = document.getElementById('recentRequestsList');
    if (!container) return;
    
    if (recentRequests.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <p>لا توجد طلبات حديثة</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = recentRequests.map(request => `
        <div class="request-item">
            <div class="request-info">
                <div class="request-avatar">
                    ${request.data.fullName.charAt(0).toUpperCase()}
                </div>
                <div class="request-details">
                    <h4>${request.data.fullName}</h4>
                    <p>${request.data.email} • ${request.data.licenseType}</p>
                </div>
            </div>
            <div class="request-meta">
                <span class="request-status ${request.status}">${getStatusText(request.status)}</span>
                <span class="request-time">${formatTimeAgo(request.createdAt)}</span>
            </div>
        </div>
    `).join('');
}

// تحميل بيانات لوحة التحكم
function loadDashboardData() {
    // تحميل البيانات من التخزين المحلي
    const savedData = localStorage.getItem('dashboardData');
    if (savedData) {
        dashboardData = { ...dashboardData, ...JSON.parse(savedData) };
    }
}

// حفظ بيانات لوحة التحكم
function saveDashboardData() {
    localStorage.setItem('dashboardData', JSON.stringify(dashboardData));
}

// البحث العام
function handleGlobalSearch(event) {
    const query = event.target.value.toLowerCase();
    
    if (query.length < 2) return;
    
    // البحث في الطلبات
    const activationRequests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
    const results = activationRequests.filter(request => 
        request.data.fullName.toLowerCase().includes(query) ||
        request.data.email.toLowerCase().includes(query) ||
        request.id.toLowerCase().includes(query)
    );
    
    // إظهار النتائج
    showSearchResults(results, query);
}

// إظهار نتائج البحث
function showSearchResults(results, query) {
    // يمكن تطوير هذه الوظيفة لإظهار نتائج البحث في نافذة منبثقة
    console.log(`نتائج البحث عن "${query}":`, results);
}

// وظائف مساعدة
function toggleAdminPassword() {
    const passwordInput = document.getElementById('adminPassword');
    const toggleBtn = document.querySelector('.toggle-password i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleBtn.className = 'fas fa-eye';
    }
}

function showLoadingState(formId) {
    const form = document.getElementById(formId);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
    }
}

function hideLoadingState(formId) {
    const form = document.getElementById(formId);
    const submitBtn = form.querySelector('button[type="submit"]');
    
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> دخول لوحة التحكم';
    }
}

function getStatusText(status) {
    const statusMap = {
        'pending': 'معلق',
        'approved': 'مقبول',
        'rejected': 'مرفوض',
        'processing': 'قيد المعالجة'
    };
    return statusMap[status] || status;
}

function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) return 'منذ لحظات';
    if (diffInSeconds < 3600) return `منذ ${Math.floor(diffInSeconds / 60)} دقيقة`;
    if (diffInSeconds < 86400) return `منذ ${Math.floor(diffInSeconds / 3600)} ساعة`;
    return `منذ ${Math.floor(diffInSeconds / 86400)} يوم`;
}

function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer');
    const toastId = 'toast-' + Date.now();
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.id = toastId;
    
    const iconMap = {
        success: 'fas fa-check-circle',
        error: 'fas fa-exclamation-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
    };
    
    toast.innerHTML = `
        <i class="toast-icon ${iconMap[type]}"></i>
        <span class="toast-message">${message}</span>
        <button class="toast-close" onclick="closeToast('${toastId}')">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    toastContainer.appendChild(toast);
    
    // إظهار التنبيه
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // إخفاء التنبيه تلقائياً
    setTimeout(() => {
        closeToast(toastId);
    }, 5000);
}

function closeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }
}

// إعداد اختصارات لوحة المفاتيح
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Ctrl + / للبحث
        if (event.ctrlKey && event.key === '/') {
            event.preventDefault();
            document.getElementById('globalSearch').focus();
        }
        
        // Escape لإغلاق النوافذ المنبثقة
        if (event.key === 'Escape') {
            closeModal();
        }
        
        // F5 لتحديث البيانات
        if (event.key === 'F5') {
            event.preventDefault();
            refreshDashboard();
        }
    });
}

// إعداد مستمعي الفلاتر
function setupFiltersListeners() {
    const filters = ['statusFilter', 'licenseFilter', 'dateFilter'];
    
    filters.forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', applyFilters);
        }
    });
}

// تطبيق الفلاتر
function applyFilters() {
    if (currentSection === 'activation-requests') {
        loadActivationRequests();
    }
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('statusFilter').value = 'all';
    document.getElementById('licenseFilter').value = 'all';
    document.getElementById('dateFilter').value = '';

    applyFilters();
    showToast('تم مسح الفلاتر', 'info');
}

// الوظائف المفقودة للأقسام الأخرى
function loadUsersManagement() {
    console.log('تحميل إدارة المستخدمين...');
    showToast('قسم إدارة المستخدمين قيد التطوير', 'info');
}

function loadLicensesManagement() {
    console.log('تحميل إدارة التراخيص...');
    showToast('قسم إدارة التراخيص قيد التطوير', 'info');
}

function loadAnalytics() {
    console.log('تحميل الإحصائيات...');
    showToast('قسم الإحصائيات قيد التطوير', 'info');
}

function loadNotifications() {
    console.log('تحميل الإشعارات...');
    showToast('قسم الإشعارات قيد التطوير', 'info');
}

function loadSystemLogs() {
    console.log('تحميل سجل النظام...');
    showToast('قسم سجل النظام قيد التطوير', 'info');
}

function loadSettings() {
    console.log('تحميل الإعدادات...');
    showToast('قسم الإعدادات قيد التطوير', 'info');
}

// إغلاق النافذة المنبثقة
function closeModal() {
    const modalOverlay = document.getElementById('modalOverlay');
    if (modalOverlay) {
        modalOverlay.classList.remove('active');
    }
}
